


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PermissionUtils</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.utils</a>
</div>

<h1>Coverage Summary for Class: PermissionUtils (clt.india.classroom.utils)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PermissionUtils</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/24)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.utils
&nbsp;
&nbsp;import android.app.Activity
&nbsp;import android.content.Intent
&nbsp;import android.content.pm.PackageManager
&nbsp;import android.net.Uri
&nbsp;import android.os.Build
&nbsp;import android.provider.Settings
&nbsp;import androidx.preference.PreferenceManager
&nbsp;
&nbsp;object PermissionUtils {
&nbsp;
&nbsp;    fun useRunTimePermissions():Boolean {
<b class="nc">&nbsp;        return Build.VERSION.SDK_INT &gt; Build.VERSION_CODES.LOLLIPOP_MR1;</b>
&nbsp;    }
&nbsp;
&nbsp;    fun hasPermission(activity: Activity, permission:String):Boolean {
<b class="nc">&nbsp;        if (useRunTimePermissions()) {</b>
<b class="nc">&nbsp;            return activity.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;</b>
&nbsp;        }
<b class="nc">&nbsp;        return true</b>
&nbsp;    }
&nbsp;
&nbsp;//    fun requestPermissions( activity:Activity, permission:Array&lt;String&gt;, requestCode:Int=100 ) {
&nbsp;//        if (useRunTimePermissions()) {
&nbsp;//            activity.requestPermissions(permission, requestCode);
&nbsp;//        }
&nbsp;//    }
&nbsp;//
&nbsp;//    fun requestPermissions(fragment: androidx.fragment.app.Fragment, permission:Array&lt;String&gt;, requestCode:Int) {
&nbsp;//        if (useRunTimePermissions()) {
&nbsp;//            fragment.requestPermissions(permission, requestCode);
&nbsp;//        }
&nbsp;//    }
&nbsp;
&nbsp;    fun shouldShowRational( activity:Activity,  permission:String):Boolean {
<b class="nc">&nbsp;        if (useRunTimePermissions()) {</b>
<b class="nc">&nbsp;            return activity.shouldShowRequestPermissionRationale(permission);</b>
&nbsp;        }
<b class="nc">&nbsp;        return false;</b>
&nbsp;    }
&nbsp;
&nbsp;    fun shouldAskForPermission( activity:Activity,  permission:String):Boolean {
<b class="nc">&nbsp;        if (useRunTimePermissions()) {</b>
<b class="nc">&nbsp;            return !hasPermission(activity, permission) &amp;&amp;</b>
<b class="nc">&nbsp;                    (!hasAskedForPermission(activity, permission) ||</b>
<b class="nc">&nbsp;                            shouldShowRational(activity, permission));</b>
&nbsp;        }
<b class="nc">&nbsp;        return false;</b>
&nbsp;    }
&nbsp;
&nbsp;    fun goToAppSettings(activity:Activity ) {
<b class="nc">&nbsp;        val intent = Intent(</b>
<b class="nc">&nbsp;            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,</b>
<b class="nc">&nbsp;            Uri.fromParts(&quot;package&quot;, activity.getPackageName(), null));</b>
<b class="nc">&nbsp;        intent.addCategory(Intent.CATEGORY_DEFAULT);</b>
<b class="nc">&nbsp;        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);</b>
<b class="nc">&nbsp;        activity.startActivity(intent);</b>
&nbsp;    }
&nbsp;
&nbsp;    fun  hasAskedForPermission(activity:Activity ,permission: String ):Boolean {
&nbsp;
&nbsp;        return PreferenceManager
<b class="nc">&nbsp;            .getDefaultSharedPreferences(activity)</b>
<b class="nc">&nbsp;            .getBoolean(permission, false);</b>
&nbsp;    }
&nbsp;
&nbsp;    fun markedPermissionAsAsked(activity:Activity, permission:String ) {
&nbsp;        PreferenceManager
<b class="nc">&nbsp;            .getDefaultSharedPreferences(activity)</b>
<b class="nc">&nbsp;            .edit()</b>
<b class="nc">&nbsp;            .putBoolean(permission, true)</b>
<b class="nc">&nbsp;            .apply();</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
