


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > SessionManager</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.utils</a>
</div>

<h1>Coverage Summary for Class: SessionManager (clt.india.classroom.utils)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">SessionManager</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.utils
&nbsp;
&nbsp;import android.content.Context
&nbsp;import android.content.SharedPreferences
&nbsp;import clt.india.classroom.R
&nbsp;
&nbsp;object SessionManager {
&nbsp;
&nbsp;    const val USER_TOKEN = &quot;user_token&quot;
&nbsp;
&nbsp;    /**
&nbsp;     * Function to save auth token
&nbsp;     */
&nbsp;    fun saveAuthToken(context: Context, token: String) {
<b class="nc">&nbsp;        saveString(context, USER_TOKEN, token)</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Function to fetch auth token
&nbsp;     */
&nbsp;    fun getToken(context: Context): String? {
<b class="nc">&nbsp;        return getString(context, USER_TOKEN)</b>
&nbsp;    }
&nbsp;
&nbsp;    fun saveString(context: Context, key: String, value: String) {
<b class="nc">&nbsp;        val prefs: SharedPreferences =</b>
<b class="nc">&nbsp;            context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)</b>
<b class="nc">&nbsp;        val editor = prefs.edit()</b>
<b class="nc">&nbsp;        editor.putString(key, value)</b>
<b class="nc">&nbsp;        editor.apply()</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;    fun getString(context: Context, key: String): String? {
<b class="nc">&nbsp;        val prefs: SharedPreferences =</b>
<b class="nc">&nbsp;            context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)</b>
<b class="nc">&nbsp;        return prefs.getString(this.USER_TOKEN, null)</b>
&nbsp;    }
&nbsp;
&nbsp;    fun clearData(context: Context){
<b class="nc">&nbsp;        val editor = context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE).edit()</b>
<b class="nc">&nbsp;        editor.clear()</b>
<b class="nc">&nbsp;        editor.apply()</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
