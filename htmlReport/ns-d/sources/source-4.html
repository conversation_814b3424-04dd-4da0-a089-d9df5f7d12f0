


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ValidationUtils</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.utils</a>
</div>

<h1>Coverage Summary for Class: ValidationUtils (clt.india.classroom.utils)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ValidationUtils</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (5/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (24/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (17/17)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.utils
&nbsp;
&nbsp;/**
&nbsp; * Utility class for validating user input
&nbsp; */
<b class="fc">&nbsp;object ValidationUtils {</b>
&nbsp;    
&nbsp;    /**
&nbsp;     * Validates an email address
&nbsp;     * @param email The email address to validate
&nbsp;     * @return true if the email is valid, false otherwise
&nbsp;     */
&nbsp;    fun isValidEmail(email: String): Boolean {
<b class="fc">&nbsp;        val emailRegex = Regex(&quot;^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$&quot;)</b>
<b class="fc">&nbsp;        return email.isNotBlank() &amp;&amp; emailRegex.matches(email)</b>
&nbsp;    }
&nbsp;    
&nbsp;    /**
&nbsp;     * Validates a password
&nbsp;     * @param password The password to validate
&nbsp;     * @return true if the password is valid, false otherwise
&nbsp;     * 
&nbsp;     * Password requirements:
&nbsp;     * - At least 8 characters long
&nbsp;     * - Contains at least one uppercase letter
&nbsp;     * - Contains at least one lowercase letter
&nbsp;     * - Contains at least one digit
&nbsp;     * - Contains at least one special character
&nbsp;     */
&nbsp;    fun isValidPassword(password: String): Boolean {
<b class="fc">&nbsp;        if (password.length &lt; 8) return false</b>
&nbsp;        
<b class="fc">&nbsp;        val hasUppercase = password.any { it.isUpperCase() }</b>
<b class="fc">&nbsp;        val hasLowercase = password.any { it.isLowerCase() }</b>
<b class="fc">&nbsp;        val hasDigit = password.any { it.isDigit() }</b>
<b class="fc">&nbsp;        val hasSpecialChar = password.any { !it.isLetterOrDigit() }</b>
&nbsp;        
<b class="fc">&nbsp;        return hasUppercase &amp;&amp; hasLowercase &amp;&amp; hasDigit &amp;&amp; hasSpecialChar</b>
&nbsp;    }
&nbsp;    
&nbsp;    /**
&nbsp;     * Validates a phone number
&nbsp;     * @param phoneNumber The phone number to validate
&nbsp;     * @return true if the phone number is valid, false otherwise
&nbsp;     * 
&nbsp;     * Valid phone number formats:
&nbsp;     * - 10 digits (e.g., 1234567890)
&nbsp;     * - With optional country code (e.g., +91 1234567890)
&nbsp;     * - With optional separators (e.g., ************)
&nbsp;     */
&nbsp;    fun isValidPhoneNumber(phoneNumber: String): Boolean {
&nbsp;        // Remove all non-digit characters except the + sign at the beginning
<b class="fc">&nbsp;        val digitsOnly = phoneNumber.replace(Regex(&quot;[^+\\d]&quot;), &quot;&quot;)</b>
&nbsp;        
&nbsp;        // Check if it&#39;s a valid format
<b class="fc">&nbsp;        return when {</b>
&nbsp;            // Format: 10 digits
<b class="fc">&nbsp;            digitsOnly.matches(Regex(&quot;\\d{10}&quot;)) -&gt; true</b>
&nbsp;            
&nbsp;            // Format: +[country code][phone number] (e.g., +911234567890)
<b class="fc">&nbsp;            digitsOnly.matches(Regex(&quot;\\+\\d{1,4}\\d{10}&quot;)) -&gt; true</b>
&nbsp;            
<b class="fc">&nbsp;            else -&gt; false</b>
&nbsp;        }
&nbsp;    }
&nbsp;    
&nbsp;    /**
&nbsp;     * Validates a name
&nbsp;     * @param name The name to validate
&nbsp;     * @return true if the name is valid, false otherwise
&nbsp;     * 
&nbsp;     * Name requirements:
&nbsp;     * - At least 2 characters long
&nbsp;     * - Contains only letters, spaces, hyphens, and apostrophes
&nbsp;     */
&nbsp;    fun isValidName(name: String): Boolean {
<b class="fc">&nbsp;        if (name.length &lt; 2) return false</b>
&nbsp;        
&nbsp;        // Allow letters, spaces, hyphens, and apostrophes
<b class="fc">&nbsp;        val nameRegex = Regex(&quot;^[A-Za-z\\s&#39;-]+$&quot;)</b>
<b class="fc">&nbsp;        return nameRegex.matches(name)</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
