


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > AnalyticsUtils</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.utils</a>
</div>

<h1>Coverage Summary for Class: AnalyticsUtils (clt.india.classroom.utils)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">AnalyticsUtils</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/12)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.utils
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import clt.india.classroom.CLTApplication
&nbsp;import com.google.firebase.analytics.FirebaseAnalytics
&nbsp;
&nbsp;object AnalyticsUtils {
&nbsp;
&nbsp;    // Track screen views
&nbsp;    fun logScreenView(screenName: String) {
<b class="nc">&nbsp;        val bundle = Bundle().apply {</b>
<b class="nc">&nbsp;            putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)</b>
<b class="nc">&nbsp;            putString(FirebaseAnalytics.Param.SCREEN_CLASS, screenName)</b>
<b class="nc">&nbsp;        }</b>
<b class="nc">&nbsp;        CLTApplication.analytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)</b>
&nbsp;    }
&nbsp;
&nbsp;    // Track login events
&nbsp;    fun logLogin(method: String) {
<b class="nc">&nbsp;        val bundle = Bundle().apply {</b>
<b class="nc">&nbsp;            putString(FirebaseAnalytics.Param.METHOD, method)</b>
<b class="nc">&nbsp;        }</b>
<b class="nc">&nbsp;        CLTApplication.analytics.logEvent(FirebaseAnalytics.Event.LOGIN, bundle)</b>
&nbsp;    }
&nbsp;
&nbsp;    // Track custom events
<b class="nc">&nbsp;    fun logCustomEvent(eventName: String, params: Bundle? = null) {</b>
<b class="nc">&nbsp;        CLTApplication.analytics.logEvent(eventName, params)</b>
&nbsp;    }
&nbsp;
&nbsp;    // Set user properties
&nbsp;    fun setUserProperty(name: String, value: String) {
<b class="nc">&nbsp;        CLTApplication.analytics.setUserProperty(name, value)</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
