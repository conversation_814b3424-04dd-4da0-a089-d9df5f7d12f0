


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > SplashActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui.splash</a>
</div>

<h1>Coverage Summary for Class: SplashActivity (clt.india.classroom.ui.splash)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">SplashActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
</tr>
  <tr>
    <td class="name">SplashActivity$onCreate$1</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;    package clt.india.classroom.ui.splash
&nbsp;
&nbsp;    import android.content.Intent
&nbsp;    import android.os.Bundle
&nbsp;    import android.provider.Settings
&nbsp;    import android.util.Log
&nbsp;    import androidx.appcompat.app.AppCompatActivity
&nbsp;    import androidx.lifecycle.lifecycleScope
&nbsp;    import clt.india.classroom.databinding.ActivitySplashBinding
&nbsp;    import clt.india.classroom.ui.ClassroomLessonsActivity
&nbsp;    import kotlinx.coroutines.delay
&nbsp;    import kotlinx.coroutines.launch
&nbsp;
<b class="nc">&nbsp;    class SplashActivity : AppCompatActivity() {</b>
<b class="nc">&nbsp;        val TAG: String = &quot;SplashScreen &quot;</b>
&nbsp;
&nbsp;        private lateinit var binding: ActivitySplashBinding
&nbsp;        override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;            super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;            binding = ActivitySplashBinding.inflate(layoutInflater)</b>
<b class="nc">&nbsp;            setContentView(binding.root)</b>
&nbsp;
<b class="nc">&nbsp;            Log.d(TAG,&quot;Android ID: &quot; + Settings.Secure.getString(getApplicationContext().getContentResolver(), Settings.Secure.ANDROID_ID));</b>
<b class="nc">&nbsp;            Log.d(TAG,&quot;IMEI ID: &quot; + &quot;12345&quot;);</b>
<b class="nc">&nbsp;            lifecycleScope.launch {</b>
<b class="nc">&nbsp;                delay(3000)</b>
<b class="nc">&nbsp;                startActivity(Intent(this@SplashActivity, ClassroomLessonsActivity::class.java))</b>
<b class="nc">&nbsp;                finish()</b>
&nbsp;            }
&nbsp;        }
&nbsp;        }
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
