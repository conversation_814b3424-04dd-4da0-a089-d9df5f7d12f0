

<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > Summary</title>
  <style type="text/css">
    @import "./css/coverage.css";
    @import "./css/idea.min.css";
  </style>
  <script type="text/javascript" src="./js/highlight.min.js"></script>
  <script type="text/javascript" src="./js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     all classes
</div>

<h1>Overall Coverage Summary </h1>
<table class="coverageStats">
  <tr>
    <th class="name">Package</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
  </tr>
  <tr>
    <td class="name">all classes</td>
<td class="coverageStat">
  <span class="percent">
    1.2%
  </span>
  <span class="absValue">
    (1/85)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    1.7%
  </span>
  <span class="absValue">
    (5/286)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    11%
  </span>
  <span class="absValue">
    (24/219)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    1.8%
  </span>
  <span class="absValue">
    (17/921)
  </span>
</td>
  </tr>
</table>

<br/>
<h2>Coverage Breakdown</h2>

<table class="coverageStats">
<tr>
  <th class="name  sortedDesc
">
<a href="index.html">Package</a>  </th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_CLASS.html">Class, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_METHOD.html">Method, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_BLOCK.html">Branch, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_LINE.html">Line, %</a>
</th>
</tr>
  <tr>
    <td class="name"><a href="ns-e/index_SORT_BY_NAME_DESC.html">clt.india.classroom.viewmodel</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/22)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-d/index_SORT_BY_NAME_DESC.html">clt.india.classroom.utils</a></td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (1/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    22.7%
  </span>
  <span class="absValue">
    (5/22)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (24/36)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25.8%
  </span>
  <span class="absValue">
    (17/66)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-c/index_SORT_BY_NAME_DESC.html">clt.india.classroom.ui.splash</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-b/index_SORT_BY_NAME_DESC.html">clt.india.classroom.ui</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/29)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/156)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/120)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/556)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-a/index_SORT_BY_NAME_DESC.html">clt.india.classroom.repository</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-9/index_SORT_BY_NAME_DESC.html">clt.india.classroom.datapck</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-8/index_SORT_BY_NAME_DESC.html">clt.india.classroom.data.api.response</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/15)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-7/index_SORT_BY_NAME_DESC.html">clt.india.classroom.data.api.request</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-6/index_SORT_BY_NAME_DESC.html">clt.india.classroom.data.api.methods</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-5/index_SORT_BY_NAME_DESC.html">clt.india.classroom.data.api</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/14)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-4/index_SORT_BY_NAME_DESC.html">clt.india.classroom.adapter</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/22)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/62)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/31)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/185)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-3/index_SORT_BY_NAME_DESC.html">clt.india.classroom</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/36)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-2/index_SORT_BY_NAME_DESC.html">androidx.databinding.library.baseAdapters</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="ns-1/index_SORT_BY_NAME_DESC.html">androidx.databinding</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
  </tr>
</table>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>

