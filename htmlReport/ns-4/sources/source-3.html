


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ChapterDetailsAdapter</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.adapter</a>
</div>

<h1>Coverage Summary for Class: ChapterDetailsAdapter (clt.india.classroom.adapter)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ChapterDetailsAdapter</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/19)
  </span>
</td>
</tr>
  <tr>
    <td class="name">ChapterDetailsAdapter$TabViewHolder</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/21)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.adapter
&nbsp;
&nbsp;import android.content.Intent
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.ViewGroup
&nbsp;import androidx.recyclerview.widget.RecyclerView
&nbsp;import clt.india.classroom.databinding.ItemChapterDetailsBinding
&nbsp;import clt.india.classroom.ui.VideoPlayingActivity
&nbsp;
&nbsp;class ChapterDetailsAdapter(
<b class="nc">&nbsp;    private val chaptersImage: List&lt;Int&gt;,</b>
<b class="nc">&nbsp;    private val chaptersTitle: List&lt;String&gt;,</b>
<b class="nc">&nbsp;    private var selectedPosition: Int = 0</b>
<b class="nc">&nbsp;) : RecyclerView.Adapter&lt;ChapterDetailsAdapter.TabViewHolder&gt;() {</b>
&nbsp;
<b class="nc">&nbsp;    inner class TabViewHolder(val binding: ItemChapterDetailsBinding) :</b>
<b class="nc">&nbsp;        RecyclerView.ViewHolder(binding.root)</b>
&nbsp;
&nbsp;    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
<b class="nc">&nbsp;        val binding = ItemChapterDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)</b>
<b class="nc">&nbsp;        return TabViewHolder(binding)</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
<b class="nc">&nbsp;        val imageRes = chaptersImage[position]</b>
<b class="nc">&nbsp;        val title = chaptersTitle[position]</b>
&nbsp;
<b class="nc">&nbsp;        val imageView = holder.binding.cardImage</b>
<b class="nc">&nbsp;        val textView = holder.binding.cardTitle</b>
&nbsp;
<b class="nc">&nbsp;        imageView.setImageResource(imageRes)</b>
<b class="nc">&nbsp;        textView.text = title</b>
<b class="nc">&nbsp;        textView.isSelected = position == selectedPosition</b>
&nbsp;
&nbsp;        // Set the click listener on the root view to navigate
<b class="nc">&nbsp;        holder.binding.root.setOnClickListener {</b>
<b class="nc">&nbsp;            val context = holder.itemView.context</b>
<b class="nc">&nbsp;            val intent = Intent(context, VideoPlayingActivity::class.java)</b>
<b class="nc">&nbsp;            intent.putExtra(&quot;chapter_title&quot;, title)</b>
<b class="nc">&nbsp;            context.startActivity(intent)</b>
&nbsp;        }
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    override fun getItemCount(): Int = chaptersImage.size</b>
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
