


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OneToFourChapterContentPagerAdapter</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.adapter</a>
</div>

<h1>Coverage Summary for Class: OneToFourChapterContentPagerAdapter (clt.india.classroom.adapter)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">OneToFourChapterContentPagerAdapter</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
</tr>
  <tr>
    <td class="name">OneToFourChapterContentPagerAdapter$Companion</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.adapter
&nbsp;
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import androidx.fragment.app.FragmentActivity
&nbsp;import androidx.viewpager2.adapter.FragmentStateAdapter
&nbsp;import clt.india.classroom.ui.OneToFourVideoFragment
&nbsp;import clt.india.classroom.ui.WorkSheetsFragment
&nbsp;
&nbsp;
&nbsp;class OneToFourChapterContentPagerAdapter(fragmentActivity: FragmentActivity) :
<b class="nc">&nbsp;    FragmentStateAdapter(fragmentActivity) {</b>
&nbsp;
&nbsp;
&nbsp;    companion object {
<b class="nc">&nbsp;        val TAB_TITLES = arrayOf(&quot;Videos&quot;, &quot;Worksheets&quot;)</b>
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    override fun getItemCount(): Int = TAB_TITLES.size</b>
&nbsp;
&nbsp;    override fun createFragment(position: Int): Fragment {
&nbsp;
<b class="nc">&nbsp;        return when (position) {</b>
<b class="nc">&nbsp;            0 -&gt; OneToFourVideoFragment.newInstance()</b>
<b class="nc">&nbsp;            1 -&gt; WorkSheetsFragment.newInstance()</b>
<b class="nc">&nbsp;            else -&gt; throw IllegalArgumentException(&quot;Invalid position: $position&quot;)</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
