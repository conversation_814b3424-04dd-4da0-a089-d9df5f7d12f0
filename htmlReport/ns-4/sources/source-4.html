


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OneToFourChapterAdapter</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.adapter</a>
</div>

<h1>Coverage Summary for Class: OneToFourChapterAdapter (clt.india.classroom.adapter)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">OneToFourChapterAdapter</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
</tr>
  <tr>
    <td class="name">OneToFourChapterAdapter$ChapterViewHolder</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.adapter
&nbsp;
&nbsp;import android.content.Intent
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.ViewGroup
&nbsp;import androidx.recyclerview.widget.RecyclerView
&nbsp;import clt.india.classroom.databinding.ItemChaptersOnetofourBinding
&nbsp;import clt.india.classroom.datapck.Chapter
&nbsp;import clt.india.classroom.ui.HomeActivity
&nbsp;import clt.india.classroom.ui.OneToFourHomeActivity
&nbsp;
<b class="nc">&nbsp;class OneToFourChapterAdapter(private var chapters: List&lt;Chapter&gt;) :</b>
<b class="nc">&nbsp;    RecyclerView.Adapter&lt;OneToFourChapterAdapter.ChapterViewHolder&gt;() {</b>
&nbsp;
<b class="nc">&nbsp;    inner class ChapterViewHolder(val binding: ItemChaptersOnetofourBinding) :</b>
<b class="nc">&nbsp;        RecyclerView.ViewHolder(binding.root)</b>
&nbsp;
&nbsp;    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
<b class="nc">&nbsp;        val binding = ItemChaptersOnetofourBinding.inflate(LayoutInflater.from(parent.context), parent, false)</b>
<b class="nc">&nbsp;        return ChapterViewHolder(binding)</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
<b class="nc">&nbsp;        val chapter = chapters[position]</b>
<b class="nc">&nbsp;        holder.binding.chapterNumber.text = chapter.chapterNumber</b>
<b class="nc">&nbsp;        holder.binding.chapterTitle.text = chapter.chapterTitle</b>
<b class="nc">&nbsp;        holder.binding.viewButton.setOnClickListener{</b>
<b class="nc">&nbsp;            val context = holder.itemView.context</b>
<b class="nc">&nbsp;            val intent = Intent(context, OneToFourHomeActivity::class.java)</b>
<b class="nc">&nbsp;            intent.putExtra(&quot;chapterNumber&quot;, chapter.chapterNumber)</b>
<b class="nc">&nbsp;            intent.putExtra(&quot;chapterTitle&quot;, chapter.chapterTitle)</b>
<b class="nc">&nbsp;            context.startActivity(intent)</b>
&nbsp;        }
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    override fun getItemCount(): Int = chapters.size</b>
&nbsp;
&nbsp;    fun updateChapters(newChapters: List&lt;Chapter&gt;) {
<b class="nc">&nbsp;        chapters = newChapters</b>
<b class="nc">&nbsp;        notifyDataSetChanged()</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
