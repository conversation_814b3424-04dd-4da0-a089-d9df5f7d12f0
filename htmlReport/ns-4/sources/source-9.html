


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > QuestionAdapter</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.adapter</a>
</div>

<h1>Coverage Summary for Class: QuestionAdapter (clt.india.classroom.adapter)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">QuestionAdapter</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/29)
  </span>
</td>
</tr>
  <tr>
    <td class="name">QuestionAdapter$QuestionViewHolder</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/31)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.adapter
&nbsp;
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.ViewGroup
&nbsp;import androidx.recyclerview.widget.RecyclerView
&nbsp;import clt.india.classroom.databinding.ItemQuestionsBinding
&nbsp;import clt.india.classroom.datapck.Question
&nbsp;
&nbsp;class QuestionAdapter(
<b class="nc">&nbsp;    private val questions: List&lt;Question&gt;</b>
<b class="nc">&nbsp;) : RecyclerView.Adapter&lt;QuestionAdapter.QuestionViewHolder&gt;() {</b>
&nbsp;
<b class="nc">&nbsp;    inner class QuestionViewHolder(val binding: ItemQuestionsBinding) :</b>
<b class="nc">&nbsp;        RecyclerView.ViewHolder(binding.root)</b>
&nbsp;
&nbsp;    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): QuestionViewHolder {
<b class="nc">&nbsp;        val binding = ItemQuestionsBinding.inflate(</b>
<b class="nc">&nbsp;            LayoutInflater.from(parent.context), parent, false</b>
&nbsp;        )
<b class="nc">&nbsp;        return QuestionViewHolder(binding)</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onBindViewHolder(holder: QuestionViewHolder, position: Int) {
<b class="nc">&nbsp;        val question = questions[position]</b>
&nbsp;
&nbsp;        // Set question text
<b class="nc">&nbsp;        holder.binding.questionText.text = &quot;${position + 1}. ${question.questionText}&quot;</b>
&nbsp;
&nbsp;        // Set options text
<b class="nc">&nbsp;        if (question.options.size &gt;= 4) {</b>
<b class="nc">&nbsp;            holder.binding.option1.text = question.options[0]</b>
<b class="nc">&nbsp;            holder.binding.option2.text = question.options[1]</b>
<b class="nc">&nbsp;            holder.binding.option3.text = question.options[2]</b>
<b class="nc">&nbsp;            holder.binding.option4.text = question.options[3]</b>
&nbsp;        }
&nbsp;
&nbsp;        // Clear any previous selection
<b class="nc">&nbsp;        holder.binding.optionsRadioGroup.setOnCheckedChangeListener(null)</b>
<b class="nc">&nbsp;        holder.binding.optionsRadioGroup.clearCheck()</b>
&nbsp;
&nbsp;        // Set previously selected option if any
<b class="nc">&nbsp;        question.selectedOption?.let { selected -&gt;</b>
<b class="nc">&nbsp;            when (selected) {</b>
<b class="nc">&nbsp;                0 -&gt; holder.binding.option1.isChecked = true</b>
<b class="nc">&nbsp;                1 -&gt; holder.binding.option2.isChecked = true</b>
<b class="nc">&nbsp;                2 -&gt; holder.binding.option3.isChecked = true</b>
<b class="nc">&nbsp;                3 -&gt; holder.binding.option4.isChecked = true</b>
&nbsp;            }
<b class="nc">&nbsp;        }</b>
&nbsp;
&nbsp;        // Handle selection change
<b class="nc">&nbsp;        holder.binding.optionsRadioGroup.setOnCheckedChangeListener { _, checkedId -&gt;</b>
<b class="nc">&nbsp;            question.selectedOption = when (checkedId) {</b>
<b class="nc">&nbsp;                holder.binding.option1.id -&gt; 0</b>
<b class="nc">&nbsp;                holder.binding.option2.id -&gt; 1</b>
<b class="nc">&nbsp;                holder.binding.option3.id -&gt; 2</b>
<b class="nc">&nbsp;                holder.binding.option4.id -&gt; 3</b>
<b class="nc">&nbsp;                else -&gt; null</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    override fun getItemCount(): Int = questions.size</b>
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
