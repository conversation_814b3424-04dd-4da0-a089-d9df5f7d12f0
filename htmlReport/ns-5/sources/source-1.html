


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ApiClient</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.data.api</a>
</div>

<h1>Coverage Summary for Class: ApiClient (clt.india.classroom.data.api)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ApiClient</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/14)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.data.api
&nbsp;
&nbsp;import clt.india.classroom.utils.Constant
&nbsp;import okhttp3.OkHttpClient
&nbsp;import okhttp3.logging.HttpLoggingInterceptor
&nbsp;import retrofit2.Retrofit
&nbsp;import retrofit2.converter.gson.GsonConverterFactory
&nbsp;
&nbsp;object ApiClient {
<b class="nc">&nbsp;    var mHttpLoggingInterceptor = HttpLoggingInterceptor()</b>
<b class="nc">&nbsp;        .setLevel(HttpLoggingInterceptor.Level.BODY)</b>
&nbsp;
<b class="nc">&nbsp;    var mOkHttpClient = OkHttpClient</b>
<b class="nc">&nbsp;        .Builder()</b>
<b class="nc">&nbsp;        .addInterceptor(mHttpLoggingInterceptor)</b>
<b class="nc">&nbsp;        .build()</b>
&nbsp;
<b class="nc">&nbsp;    var mRetrofit: Retrofit? = null</b>
&nbsp;
&nbsp;
&nbsp;    val client: Retrofit?
&nbsp;        get() {
<b class="nc">&nbsp;            if(mRetrofit == null){</b>
<b class="nc">&nbsp;                mRetrofit = Retrofit.Builder()</b>
<b class="nc">&nbsp;                    .baseUrl(Constant.BASE_URL)</b>
<b class="nc">&nbsp;                    .client(mOkHttpClient)</b>
<b class="nc">&nbsp;                    .addConverterFactory(GsonConverterFactory.create())</b>
<b class="nc">&nbsp;                    .build()</b>
&nbsp;            }
<b class="nc">&nbsp;            return mRetrofit</b>
&nbsp;        }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
