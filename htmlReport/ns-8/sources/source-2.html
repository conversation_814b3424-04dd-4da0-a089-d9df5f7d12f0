


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > LoginResponse</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.data.api.response</a>
</div>

<h1>Coverage Summary for Class: LoginResponse (clt.india.classroom.data.api.response)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">LoginResponse</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
</tr>
  <tr>
    <td class="name">LoginResponse$Data</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.data.api.response
&nbsp;
&nbsp;
&nbsp;import com.google.gson.annotations.SerializedName
&nbsp;
<b class="nc">&nbsp;data class LoginResponse(</b>
<b class="nc">&nbsp;    @SerializedName(&quot;code&quot;)</b>
&nbsp;    var code: Int,
<b class="nc">&nbsp;    @SerializedName(&quot;data&quot;)</b>
&nbsp;    var `data`: Data,
<b class="nc">&nbsp;    @SerializedName(&quot;id&quot;)</b>
&nbsp;    var id: String,
<b class="nc">&nbsp;    @SerializedName(&quot;message&quot;)</b>
&nbsp;    var message: String
&nbsp;) {
<b class="nc">&nbsp;    data class Data(</b>
<b class="nc">&nbsp;        @SerializedName(&quot;Email&quot;)</b>
&nbsp;        var email: String,
<b class="nc">&nbsp;        @SerializedName(&quot;id&quot;)</b>
&nbsp;        var id: String,
<b class="nc">&nbsp;        @SerializedName(&quot;Id&quot;)</b>
&nbsp;        var id2: Int,
<b class="nc">&nbsp;        @SerializedName(&quot;Name&quot;)</b>
&nbsp;        var name: String,
<b class="nc">&nbsp;        @SerializedName(&quot;Token&quot;)</b>
&nbsp;        var token: String
&nbsp;    )
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
