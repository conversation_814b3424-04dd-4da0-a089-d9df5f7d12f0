/*
 * Copyright 2000-2021 JetBrains s.r.o.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

* {
  margin: 0;
  padding: 0;
}

body {
  background-color: #fff;
  font-family: helvetica neue, tahoma, arial, sans-serif;
  font-size: 82%;
  color: #151515;
}

h1 {
  margin: 0.5em 0;
  color: #010101;
  font-weight: normal;
  font-size: 18px;
}

h2 {
  margin: 0.5em 0;
  color: #010101;
  font-weight: normal;
  font-size: 16px;
}

a {
  color: #1564C2;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

span.separator {
  color: #9BA9BA;
  padding-left: 5px;
  padding-right: 5px;
}

div.content {
  width: 99%;
}

table.coverageStats {
  width: 100%;
  border-collapse: collapse;
}

table.overallStats {
  width: 20%;
}

table.coverageStats td, table.coverageStats th {
  padding: 4px 2px;
  border-bottom: 1px solid #ccc;
}

table.coverageStats th {
  background-color: #959BA4;
  border: none;
  font-weight: bold;
  text-align: left;
  color: #FFF;
}

table.coverageStats th.coverageStat {
  width: 15%;
}

table.coverageStats th a {
  color: #FFF;
}

table.coverageStats th a:hover {
  text-decoration: none;
}

table.coverageStats th.sortedDesc a {
  background: url(../img/arrowDown.gif) no-repeat 100% 2px;
  padding-right: 20px;
}

table.coverageStats th.sortedAsc a {
  background: url(../img/arrowUp.gif) no-repeat 100% 2px;
  padding-right: 20px;
}

div.footer {
  margin: 2em .5em;
  font-size: 85%;
  text-align: left;
  line-height: 140%;
}

code.sourceCode {
  width: 100%;
  border: 1px solid #ccc;
  font: normal 12px 'Menlo', 'Bitstream Vera Sans Mono', 'Courier New', 'Courier', monospace;
  white-space: pre;
}

code.sourceCode b {
  font-weight: normal;
}

code.sourceCode span.number {
  color: #151515;
}

code.sourceCode .fc {
  background-color: #cfc;
}

code.sourceCode .pc {
  background-color: #ffc;
}

code.sourceCode .nc {
  background-color: #fcc;
}

.percent, .absValue {
  font-size: 90%;
}

.percent .green, .absValue .green {
  color: #32cc32;
}

.percent .red, .absValue .red {
  color: #f00;
}

.percent .totalDiff {
  color: #3f3f3f;
}
