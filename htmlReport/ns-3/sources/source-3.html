


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > DataBinderMapperImpl</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom</a>
</div>

<h1>Coverage Summary for Class: DataBinderMapperImpl (clt.india.classroom)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">DataBinderMapperImpl</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/26)
  </span>
</td>
</tr>
  <tr>
    <td class="name">DataBinderMapperImpl$InnerBrLookup</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">DataBinderMapperImpl$InnerLayoutIdLookup</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/9)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/29)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom;
&nbsp;
&nbsp;import android.util.SparseArray;
&nbsp;import android.util.SparseIntArray;
&nbsp;import android.view.View;
&nbsp;import androidx.databinding.DataBinderMapper;
&nbsp;import androidx.databinding.DataBindingComponent;
&nbsp;import androidx.databinding.ViewDataBinding;
&nbsp;import java.lang.Integer;
&nbsp;import java.lang.Object;
&nbsp;import java.lang.Override;
&nbsp;import java.lang.RuntimeException;
&nbsp;import java.lang.String;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.HashMap;
&nbsp;import java.util.List;
&nbsp;
<b class="nc">&nbsp;public class DataBinderMapperImpl extends DataBinderMapper {</b>
<b class="nc">&nbsp;  private static final SparseIntArray INTERNAL_LAYOUT_ID_LOOKUP = new SparseIntArray(0);</b>
&nbsp;
&nbsp;  @Override
&nbsp;  public ViewDataBinding getDataBinder(DataBindingComponent component, View view, int layoutId) {
<b class="nc">&nbsp;    int localizedLayoutId = INTERNAL_LAYOUT_ID_LOOKUP.get(layoutId);</b>
<b class="nc">&nbsp;    if(localizedLayoutId &gt; 0) {</b>
<b class="nc">&nbsp;      final Object tag = view.getTag();</b>
<b class="nc">&nbsp;      if(tag == null) {</b>
<b class="nc">&nbsp;        throw new RuntimeException(&quot;view must have a tag&quot;);</b>
&nbsp;      }
&nbsp;    }
<b class="nc">&nbsp;    return null;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public ViewDataBinding getDataBinder(DataBindingComponent component, View[] views, int layoutId) {
<b class="nc">&nbsp;    if(views == null || views.length == 0) {</b>
<b class="nc">&nbsp;      return null;</b>
&nbsp;    }
<b class="nc">&nbsp;    int localizedLayoutId = INTERNAL_LAYOUT_ID_LOOKUP.get(layoutId);</b>
<b class="nc">&nbsp;    if(localizedLayoutId &gt; 0) {</b>
<b class="nc">&nbsp;      final Object tag = views[0].getTag();</b>
<b class="nc">&nbsp;      if(tag == null) {</b>
<b class="nc">&nbsp;        throw new RuntimeException(&quot;view must have a tag&quot;);</b>
&nbsp;      }
<b class="nc">&nbsp;      switch(localizedLayoutId) {</b>
&nbsp;      }
&nbsp;    }
<b class="nc">&nbsp;    return null;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int getLayoutId(String tag) {
<b class="nc">&nbsp;    if (tag == null) {</b>
<b class="nc">&nbsp;      return 0;</b>
&nbsp;    }
<b class="nc">&nbsp;    Integer tmpVal = InnerLayoutIdLookup.sKeys.get(tag);</b>
<b class="nc">&nbsp;    return tmpVal == null ? 0 : tmpVal;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String convertBrIdToString(int localId) {
<b class="nc">&nbsp;    String tmpVal = InnerBrLookup.sKeys.get(localId);</b>
<b class="nc">&nbsp;    return tmpVal;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public List&lt;DataBinderMapper&gt; collectDependencies() {
<b class="nc">&nbsp;    ArrayList&lt;DataBinderMapper&gt; result = new ArrayList&lt;DataBinderMapper&gt;(1);</b>
<b class="nc">&nbsp;    result.add(new androidx.databinding.library.baseAdapters.DataBinderMapperImpl());</b>
<b class="nc">&nbsp;    return result;</b>
&nbsp;  }
&nbsp;
&nbsp;  private static class InnerBrLookup {
<b class="nc">&nbsp;    static final SparseArray&lt;String&gt; sKeys = new SparseArray&lt;String&gt;(1);</b>
&nbsp;
&nbsp;    static {
<b class="nc">&nbsp;      sKeys.put(0, &quot;_all&quot;);</b>
&nbsp;    }
&nbsp;  }
&nbsp;
&nbsp;  private static class InnerLayoutIdLookup {
<b class="nc">&nbsp;    static final HashMap&lt;String, Integer&gt; sKeys = new HashMap&lt;String, Integer&gt;(0);</b>
&nbsp;  }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
