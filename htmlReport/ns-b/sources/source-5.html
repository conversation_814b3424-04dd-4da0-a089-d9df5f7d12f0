


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > GradeSelectionFragment</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: GradeSelectionFragment (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">GradeSelectionFragment</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/14)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/53)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import android.widget.AdapterView
&nbsp;import android.widget.ArrayAdapter
&nbsp;import android.widget.PopupMenu
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import androidx.lifecycle.ViewModelProvider
&nbsp;import androidx.navigation.NavController
&nbsp;import androidx.navigation.fragment.findNavController
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.databinding.FragmentGradeSelectionBinding
&nbsp;import clt.india.classroom.viewmodel.GradeSelectionViewModel
&nbsp;
<b class="nc">&nbsp;class GradeSelectionFragment : Fragment() {</b>
&nbsp;
&nbsp;    private var _binding: FragmentGradeSelectionBinding? = null
<b class="nc">&nbsp;    private val binding get() = _binding!!</b>
&nbsp;    private lateinit var navController: NavController
&nbsp;    private lateinit var gradeSelectionViewModel: GradeSelectionViewModel
&nbsp;    override fun onCreateView(
&nbsp;        inflater: LayoutInflater, container: ViewGroup?,
&nbsp;        savedInstanceState: Bundle?
&nbsp;    ): View {
<b class="nc">&nbsp;        _binding = FragmentGradeSelectionBinding.inflate(inflater, container, false)</b>
<b class="nc">&nbsp;        return binding.root</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onViewCreated(view, savedInstanceState)</b>
<b class="nc">&nbsp;        navController = findNavController()</b>
<b class="nc">&nbsp;        gradeSelectionViewModel = ViewModelProvider(this).get(GradeSelectionViewModel::class.java)</b>
<b class="nc">&nbsp;        setupTabLayout()</b>
<b class="nc">&nbsp;        handleclickevents()</b>
<b class="nc">&nbsp;        setupGradeSpinner()</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun handleclickevents() {
<b class="nc">&nbsp;        binding.submit.setOnClickListener {</b>
&nbsp;
<b class="nc">&nbsp;            val selectedGrade = gradeSelectionViewModel.selectedGrade.value ?: &quot;Grade 1&quot;</b>
<b class="nc">&nbsp;            val gradeNum = selectedGrade.substringAfter(&quot;Grade &quot;).toIntOrNull() ?: 1</b>
&nbsp;
<b class="nc">&nbsp;            if (gradeNum in 1..4) {</b>
<b class="nc">&nbsp;                val action = GradeSelectionFragmentDirections</b>
<b class="nc">&nbsp;                    .actionGradeSelectionFragmentToOneToFourIndexPageFragment(selectedGrade)</b>
<b class="nc">&nbsp;                navController.navigate(action)</b>
&nbsp;            } else {
<b class="nc">&nbsp;                val action = GradeSelectionFragmentDirections</b>
<b class="nc">&nbsp;                    .actionGradeSelectionFragmentToIndexPageFragment(selectedGrade)</b>
<b class="nc">&nbsp;                navController.navigate(action)</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    private fun setupTabLayout() {
<b class="nc">&nbsp;        binding.tabClassroom.setOnClickListener {</b>
<b class="nc">&nbsp;            selectTab(isClassroomSelected = true)</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.tabDigital.setOnClickListener {</b>
<b class="nc">&nbsp;            selectTab(isClassroomSelected = false)</b>
&nbsp;        }
&nbsp;
&nbsp;        // Set default selected tab
<b class="nc">&nbsp;        selectTab(isClassroomSelected = true)</b>
&nbsp;    }
&nbsp;    private fun selectTab(isClassroomSelected: Boolean) {
<b class="nc">&nbsp;        if (isClassroomSelected) {</b>
<b class="nc">&nbsp;            binding.tabClassroom.setBackgroundResource(R.drawable.selected_tab_background)</b>
<b class="nc">&nbsp;            binding.tabClassroom.setTextColor(resources.getColor(R.color.white))</b>
&nbsp;
<b class="nc">&nbsp;            binding.tabDigital.setBackgroundResource(R.drawable.unselected_tab_background)</b>
<b class="nc">&nbsp;            binding.tabDigital.setTextColor(resources.getColor(R.color.black))</b>
&nbsp;        } else {
<b class="nc">&nbsp;            binding.tabDigital.setBackgroundResource(R.drawable.selected_tab_background)</b>
<b class="nc">&nbsp;            binding.tabDigital.setTextColor(resources.getColor(R.color.white))</b>
&nbsp;
<b class="nc">&nbsp;            binding.tabClassroom.setBackgroundResource(R.drawable.unselected_tab_background)</b>
<b class="nc">&nbsp;            binding.tabClassroom.setTextColor(resources.getColor(R.color.black))</b>
&nbsp;        }
&nbsp;
&nbsp;        // If you want to update content based on tab, add logic here
&nbsp;        // e.g., show/hide views or swap fragments based on selected tab
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    /* private fun setupGradeSpinner() {
&nbsp;         val grades = listOf(&quot;Grade 1&quot;, &quot;Grade 2&quot;, &quot;Grade 3&quot;, &quot;Grade 4&quot;)
&nbsp;         val adapter = ArrayAdapter(requireContext(), R.layout.spinner_dropdown_item, grades)
&nbsp;         adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
&nbsp;         binding.gradeSelect.adapter = adapter
&nbsp;         binding.gradeSelect.setSelection(0)
&nbsp;         binding.gradeSelect.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
&nbsp;             override fun onItemSelected(
&nbsp;                 parent: AdapterView&lt;*&gt;?,
&nbsp;                 view: View?,
&nbsp;                 position: Int,
&nbsp;                 id: Long
&nbsp;             ) {
&nbsp;                 val selectedGrade = grades[position]
&nbsp;                 gradeSelectionViewModel.selectGrade(selectedGrade)
&nbsp;             }
&nbsp;
&nbsp;             override fun onNothingSelected(parent: AdapterView&lt;*&gt;?) {}
&nbsp;         }
&nbsp;     }*/
&nbsp;
&nbsp;    private fun setupGradeSpinner() {
<b class="nc">&nbsp;        val grades = listOf(&quot;Grade 1&quot;, &quot;Grade 2&quot;, &quot;Grade 3&quot;, &quot;Grade 4&quot;, &quot;Grade 5&quot;, &quot;Grade 6&quot;, &quot;Grade 7&quot;,&quot;Grade 8&quot;,&quot;Grade 9&quot;,&quot;Grade 10&quot;)</b>
<b class="nc">&nbsp;        val defaultGrade = &quot;Grade 1&quot;</b>
<b class="nc">&nbsp;        binding.gradeText.text = defaultGrade</b>
<b class="nc">&nbsp;        gradeSelectionViewModel.selectGrade(defaultGrade)</b>
&nbsp;
<b class="nc">&nbsp;        val dropdownClickListener = View.OnClickListener {</b>
<b class="nc">&nbsp;            val popupMenu = PopupMenu(requireContext(), binding.gradeText)</b>
<b class="nc">&nbsp;            grades.forEachIndexed { index, grade -&gt;</b>
<b class="nc">&nbsp;                popupMenu.menu.add(0, index, index, grade)</b>
<b class="nc">&nbsp;            }</b>
&nbsp;
<b class="nc">&nbsp;            popupMenu.setOnMenuItemClickListener { item -&gt;</b>
<b class="nc">&nbsp;                val selectedGrade = grades[item.itemId]</b>
<b class="nc">&nbsp;                binding.gradeText.text = selectedGrade</b>
<b class="nc">&nbsp;                gradeSelectionViewModel.selectGrade(selectedGrade)</b>
<b class="nc">&nbsp;                true</b>
&nbsp;            }
<b class="nc">&nbsp;            popupMenu.show()</b>
&nbsp;        }
<b class="nc">&nbsp;        binding.gradeText.setOnClickListener(dropdownClickListener)</b>
<b class="nc">&nbsp;        binding.dropdownIcon.setOnClickListener(dropdownClickListener)</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onDestroyView() {
<b class="nc">&nbsp;        super.onDestroyView()</b>
<b class="nc">&nbsp;        _binding = null</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
