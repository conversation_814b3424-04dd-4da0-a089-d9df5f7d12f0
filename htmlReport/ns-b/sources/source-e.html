


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > TextBookViewActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: TextBookViewActivity (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">TextBookViewActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/14)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import androidx.appcompat.app.AppCompatActivity
&nbsp;import clt.india.classroom.databinding.ActivityTextBookViewBinding
&nbsp;
&nbsp;
<b class="nc">&nbsp;class TextBookViewActivity : AppCompatActivity() {</b>
&nbsp;    private lateinit var binding: ActivityTextBookViewBinding
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        binding = ActivityTextBookViewBinding.inflate(layoutInflater)</b>
<b class="nc">&nbsp;        setContentView(binding.root)</b>
<b class="nc">&nbsp;        supportActionBar?.hide()</b>
<b class="nc">&nbsp;        loadPdf()</b>
<b class="nc">&nbsp;        handleclicks()</b>
&nbsp;
&nbsp;    }
&nbsp;    private fun loadPdf() {
<b class="nc">&nbsp;        binding.pdfView.fromAsset(&quot;ved_removed.pdf&quot;)</b>
<b class="nc">&nbsp;            .enableSwipe(true)</b>
<b class="nc">&nbsp;            .swipeHorizontal(false)</b>
<b class="nc">&nbsp;            .enableDoubletap(true)</b>
<b class="nc">&nbsp;            .load()</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun handleclicks() {
<b class="nc">&nbsp;        binding.backButton.setOnClickListener {</b>
<b class="nc">&nbsp;            onBackPressedDispatcher.onBackPressed()</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
