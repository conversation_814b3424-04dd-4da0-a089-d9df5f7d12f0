


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > LogoutActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: LogoutActivity (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">LogoutActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.content.Intent
&nbsp;import androidx.appcompat.app.AppCompatActivity
&nbsp;import android.os.Bundle
&nbsp;import clt.india.classroom.databinding.ActivityLogoutBinding
&nbsp;import clt.india.classroom.utils.AnalyticsUtils
&nbsp;import clt.india.classroom.utils.SessionManager
&nbsp;
<b class="nc">&nbsp;class LogoutActivity : AppCompatActivity() {</b>
&nbsp;
&nbsp;    private lateinit var binding: ActivityLogoutBinding
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        binding = ActivityLogoutBinding.inflate(layoutInflater)</b>
<b class="nc">&nbsp;        val view = binding.root</b>
<b class="nc">&nbsp;        setContentView(view)</b>
&nbsp;
&nbsp;        // Track screen view
<b class="nc">&nbsp;        AnalyticsUtils.logScreenView(&quot;LogoutActivity&quot;)</b>
&nbsp;
<b class="nc">&nbsp;        binding.btnLogout.setOnClickListener {</b>
&nbsp;            // Track logout event
<b class="nc">&nbsp;            AnalyticsUtils.logCustomEvent(&quot;logout_clicked&quot;)</b>
&nbsp;
<b class="nc">&nbsp;            SessionManager.clearData(this)</b>
<b class="nc">&nbsp;            val intent = Intent(this, MainActivity::class.java)</b>
<b class="nc">&nbsp;            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)</b>
<b class="nc">&nbsp;            intent.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)</b>
<b class="nc">&nbsp;            startActivity(intent)</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
