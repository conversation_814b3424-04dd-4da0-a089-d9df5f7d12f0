


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OneToFourWorkSheetsFragment</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: OneToFourWorkSheetsFragment (clt.india.classroom.ui)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">OneToFourWorkSheetsFragment</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
</tr>
  <tr>
    <td class="name">OneToFourWorkSheetsFragment$Companion</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/14)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import clt.india.classroom.R
&nbsp;
&nbsp;// TODO: Rename parameter arguments, choose names that match
&nbsp;// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
&nbsp;private const val ARG_PARAM1 = &quot;param1&quot;
&nbsp;private const val ARG_PARAM2 = &quot;param2&quot;
&nbsp;
&nbsp;/**
&nbsp; * A simple [Fragment] subclass.
&nbsp; * Use the [OneToFourWorkSheetsFragment.newInstance] factory method to
&nbsp; * create an instance of this fragment.
&nbsp; */
<b class="nc">&nbsp;class OneToFourWorkSheetsFragment : Fragment() {</b>
&nbsp;    // TODO: Rename and change types of parameters
&nbsp;    private var param1: String? = null
&nbsp;    private var param2: String? = null
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        arguments?.let {</b>
<b class="nc">&nbsp;            param1 = it.getString(ARG_PARAM1)</b>
<b class="nc">&nbsp;            param2 = it.getString(ARG_PARAM2)</b>
<b class="nc">&nbsp;        }</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onCreateView(
&nbsp;        inflater: LayoutInflater, container: ViewGroup?,
&nbsp;        savedInstanceState: Bundle?
&nbsp;    ): View? {
&nbsp;        // Inflate the layout for this fragment
<b class="nc">&nbsp;        return inflater.inflate(R.layout.fragment_one_to_four_work_sheets, container, false)</b>
&nbsp;    }
&nbsp;
&nbsp;    companion object {
&nbsp;        /**
&nbsp;         * Use this factory method to create a new instance of
&nbsp;         * this fragment using the provided parameters.
&nbsp;         *
&nbsp;         * @param param1 Parameter 1.
&nbsp;         * @param param2 Parameter 2.
&nbsp;         * @return A new instance of fragment OneToFourWorkSheetsFragment.
&nbsp;         */
&nbsp;        // TODO: Rename and change types and number of parameters
&nbsp;        @JvmStatic
&nbsp;        fun newInstance(param1: String, param2: String) =
<b class="nc">&nbsp;            OneToFourWorkSheetsFragment().apply {</b>
<b class="nc">&nbsp;                arguments = Bundle().apply {</b>
<b class="nc">&nbsp;                    putString(ARG_PARAM1, param1)</b>
<b class="nc">&nbsp;                    putString(ARG_PARAM2, param2)</b>
<b class="nc">&nbsp;                }</b>
<b class="nc">&nbsp;            }</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
