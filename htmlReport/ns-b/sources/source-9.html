


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > MainActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: MainActivity (clt.india.classroom.ui)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">MainActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/26)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/41)
  </span>
</td>
</tr>
  <tr>
    <td class="name">MainActivity$sam$androidx_lifecycle_Observer$0</td>
  </tr>
  <tr>
    <td class="name">MainActivity$special$$inlined$viewModels$default$1</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">MainActivity$special$$inlined$viewModels$default$2</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">MainActivity$special$$inlined$viewModels$default$3</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/30)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/44)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.content.Intent
&nbsp;import android.content.Intent.FLAG_ACTIVITY_NO_HISTORY
&nbsp;import android.os.Bundle
&nbsp;import android.view.View
&nbsp;import android.widget.Toast
&nbsp;import androidx.activity.viewModels
&nbsp;import androidx.appcompat.app.AppCompatActivity
&nbsp;import clt.india.classroom.data.api.response.BaseResponse
&nbsp;import clt.india.classroom.data.api.response.LoginResponse
&nbsp;import clt.india.classroom.databinding.ActivityMainBinding
&nbsp;import clt.india.classroom.utils.AnalyticsUtils
&nbsp;import clt.india.classroom.utils.SessionManager
&nbsp;import clt.india.classroom.viewmodel.LoginViewModel
&nbsp;
<b class="nc">&nbsp;class MainActivity : AppCompatActivity() {</b>
&nbsp;
&nbsp;    private lateinit var binding: ActivityMainBinding
<b class="nc">&nbsp;    private val viewModel by viewModels&lt;LoginViewModel&gt;()</b>
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        binding = ActivityMainBinding.inflate(layoutInflater)</b>
<b class="nc">&nbsp;        val view = binding.root</b>
<b class="nc">&nbsp;        setContentView(view)</b>
&nbsp;
&nbsp;        // Track screen view
<b class="nc">&nbsp;        AnalyticsUtils.logScreenView(&quot;MainActivity&quot;)</b>
&nbsp;
<b class="nc">&nbsp;        val token = SessionManager.getToken(this)</b>
<b class="nc">&nbsp;        if (!token.isNullOrBlank()) {</b>
<b class="nc">&nbsp;           navigateToHome()</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        viewModel.loginResult.observe(this) { it -&gt;</b>
<b class="nc">&nbsp;            when (it) {</b>
<b class="nc">&nbsp;                is BaseResponse.Loading -&gt; {</b>
<b class="nc">&nbsp;                    showLoading()</b>
&nbsp;                }
&nbsp;
<b class="nc">&nbsp;                is BaseResponse.Success -&gt; {</b>
<b class="nc">&nbsp;                    stopLoading()</b>
<b class="nc">&nbsp;                    processLogin(it.data)</b>
&nbsp;                }
&nbsp;
<b class="nc">&nbsp;                is BaseResponse.Error -&gt; {</b>
<b class="nc">&nbsp;                    processError(it.msg)</b>
&nbsp;                }
&nbsp;                else -&gt; {
<b class="nc">&nbsp;                    stopLoading()</b>
&nbsp;                }
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.btnLogin.setOnClickListener {</b>
<b class="nc">&nbsp;            doLogin()</b>
&nbsp;
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.btnRegister.setOnClickListener {</b>
<b class="nc">&nbsp;            doSignup()</b>
&nbsp;        }
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;    private fun navigateToHome() {
<b class="nc">&nbsp;        val intent = Intent(this, LogoutActivity::class.java)</b>
<b class="nc">&nbsp;        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)</b>
<b class="nc">&nbsp;        intent.addFlags(FLAG_ACTIVITY_NO_HISTORY)</b>
<b class="nc">&nbsp;        startActivity(intent)</b>
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    fun doLogin() {</b>
<b class="nc">&nbsp;        val email = binding.txtInputEmail.text.toString()</b>
<b class="nc">&nbsp;        val pwd = binding.txtPass.text.toString()</b>
<b class="nc">&nbsp;        viewModel.loginUser(email = email, pwd = pwd)</b>
<b class="nc">&nbsp;</b>
&nbsp;    }
<b class="nc">&nbsp;</b>
&nbsp;    fun doSignup() {
&nbsp;
<b class="nc">&nbsp;    }</b>
&nbsp;
&nbsp;    fun showLoading() {
<b class="nc">&nbsp;        binding.prgbar.visibility = View.VISIBLE</b>
&nbsp;    }
&nbsp;
&nbsp;    fun stopLoading() {
<b class="nc">&nbsp;        binding.prgbar.visibility = View.GONE</b>
&nbsp;    }
&nbsp;
&nbsp;    fun processLogin(data: LoginResponse?) {
<b class="nc">&nbsp;        showToast(&quot;Success:&quot; + data?.message)</b>
<b class="nc">&nbsp;        if (!data?.data?.token.isNullOrEmpty()) {</b>
&nbsp;            // Track successful login event
<b class="nc">&nbsp;            AnalyticsUtils.logLogin(&quot;email&quot;)</b>
&nbsp;
<b class="nc">&nbsp;            data?.data?.token?.let { SessionManager.saveAuthToken(this, it) }</b>
<b class="nc">&nbsp;            navigateToHome()</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    fun processError(msg: String?) {
<b class="nc">&nbsp;        showToast(&quot;Error:&quot; + msg)</b>
&nbsp;    }
&nbsp;
&nbsp;    fun showToast(msg: String) {
<b class="nc">&nbsp;        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()</b>
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
