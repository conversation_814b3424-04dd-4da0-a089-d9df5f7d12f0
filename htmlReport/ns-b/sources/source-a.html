


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OneToFourHomeActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: OneToFourHomeActivity (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">OneToFourHomeActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/21)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import androidx.activity.enableEdgeToEdge
&nbsp;import androidx.appcompat.app.AppCompatActivity
&nbsp;import androidx.recyclerview.widget.LinearLayoutManager
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.adapter.ChapterContentPagerAdapter
&nbsp;import clt.india.classroom.adapter.OneToFourChapterContentPagerAdapter
&nbsp;import clt.india.classroom.adapter.SubjectTabAdapter
&nbsp;import clt.india.classroom.databinding.ActivityHomeBinding
&nbsp;import com.google.android.material.tabs.TabLayoutMediator
&nbsp;
&nbsp;
<b class="nc">&nbsp;class OneToFourHomeActivity : AppCompatActivity() {</b>
&nbsp;    private var selectedSubject: String? = null
&nbsp;    private lateinit var binding: ActivityHomeBinding
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        binding = ActivityHomeBinding.inflate(layoutInflater)</b>
&nbsp;
<b class="nc">&nbsp;        enableEdgeToEdge()</b>
<b class="nc">&nbsp;        setContentView(binding.root)</b>
<b class="nc">&nbsp;        supportActionBar?.hide()</b>
&nbsp;
<b class="nc">&nbsp;        setupSubjectTabs()</b>
<b class="nc">&nbsp;        setupViewPagerWithTabs()</b>
<b class="nc">&nbsp;        handleclicks()</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun handleclicks() {
<b class="nc">&nbsp;        binding.chapterNavigation.setOnClickListener {</b>
<b class="nc">&nbsp;            finish()</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private fun setupSubjectTabs() {
<b class="nc">&nbsp;        val subjects = listOf(&quot;Foundational Literacy&quot;, &quot;Numeracy&quot;)</b>
&nbsp;
<b class="nc">&nbsp;        val adapter = SubjectTabAdapter(subjects) { subject -&gt;</b>
<b class="nc">&nbsp;            selectedSubject = subject</b>
&nbsp;
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(this)</b>
<b class="nc">&nbsp;        binding.subjectTabsRecyclerView.adapter = adapter</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun setupViewPagerWithTabs() {
<b class="nc">&nbsp;        val pagerAdapter = OneToFourChapterContentPagerAdapter(this)</b>
<b class="nc">&nbsp;        binding.viewPager.adapter = pagerAdapter</b>
&nbsp;
<b class="nc">&nbsp;        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position -&gt;</b>
<b class="nc">&nbsp;            tab.text = OneToFourChapterContentPagerAdapter.TAB_TITLES[position]</b>
<b class="nc">&nbsp;        }.attach()</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
