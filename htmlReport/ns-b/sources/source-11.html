


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > VideoPlayingActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: VideoPlayingActivity (clt.india.classroom.ui)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">VideoPlayingActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/15)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/14)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/59)
  </span>
</td>
</tr>
  <tr>
    <td class="name">VideoPlayingActivity$setupControls$2</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">VideoPlayingActivity$updateSeekBar$runnable$1</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/21)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/71)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.content.Context
&nbsp;import android.media.AudioManager
&nbsp;import android.media.MediaPlayer
&nbsp;import android.net.Uri
&nbsp;import android.os.Bundle
&nbsp;import android.os.Handler
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import android.widget.MediaController
&nbsp;import android.widget.SeekBar
&nbsp;import androidx.activity.enableEdgeToEdge
&nbsp;import androidx.appcompat.app.AppCompatActivity
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.databinding.ActivityVideoPlayingBinding
&nbsp;
<b class="nc">&nbsp;class VideoPlayingActivity : AppCompatActivity() {</b>
&nbsp;    private lateinit var binding: ActivityVideoPlayingBinding
&nbsp;    private lateinit var videoPlayer: MediaPlayer
<b class="nc">&nbsp;    private val handler = Handler()</b>
<b class="nc">&nbsp;    var isFullscreen = false</b>
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
&nbsp;
&nbsp;
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        enableEdgeToEdge()</b>
<b class="nc">&nbsp;        supportActionBar?.hide()</b>
<b class="nc">&nbsp;        binding = ActivityVideoPlayingBinding.inflate(layoutInflater)</b>
<b class="nc">&nbsp;        setContentView(binding.root)</b>
<b class="nc">&nbsp;        handleClicks()</b>
<b class="nc">&nbsp;        playVideo()</b>
<b class="nc">&nbsp;        setupControls()</b>
&nbsp;    }
&nbsp;    private fun handleClicks() {
<b class="nc">&nbsp;        binding.backButton.setOnClickListener {</b>
<b class="nc">&nbsp;            onBackPressedDispatcher.onBackPressed()</b>
&nbsp;        }
&nbsp;       /* binding.volumeButton.setOnClickListener {
&nbsp;            val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
&nbsp;
&nbsp;            isMuted = !isMuted
&nbsp;
&nbsp;            if (isMuted) {
&nbsp;                audioManager.adjustStreamVolume(
&nbsp;                    AudioManager.STREAM_MUSIC,
&nbsp;                    AudioManager.ADJUST_MUTE,
&nbsp;                    0
&nbsp;                )
&nbsp;                binding.volumeButton.setImageResource(R.drawable.ic_volume)
&nbsp;            } else {
&nbsp;                audioManager.adjustStreamVolume(
&nbsp;                    AudioManager.STREAM_MUSIC,
&nbsp;                    AudioManager.ADJUST_UNMUTE,
&nbsp;                    0
&nbsp;                )
&nbsp;                binding.volumeButton.setImageResource(R.drawable.ic_volume)
&nbsp;            }
&nbsp;        }*/
&nbsp;
<b class="nc">&nbsp;        binding.fullScreen.setOnClickListener {</b>
<b class="nc">&nbsp;            if (!isFullscreen) {</b>
<b class="nc">&nbsp;                binding.videoView.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT</b>
<b class="nc">&nbsp;                binding.videoView.layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT</b>
<b class="nc">&nbsp;                window.decorView.systemUiVisibility = (</b>
<b class="nc">&nbsp;                        View.SYSTEM_UI_FLAG_FULLSCREEN</b>
&nbsp;                                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
&nbsp;                                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
&nbsp;                        )
<b class="nc">&nbsp;                supportActionBar?.hide()</b>
&nbsp;            } else {
<b class="nc">&nbsp;                binding.videoView.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT</b>
<b class="nc">&nbsp;                binding.videoView.layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT</b>
<b class="nc">&nbsp;                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE</b>
<b class="nc">&nbsp;                supportActionBar?.show()</b>
&nbsp;            }
<b class="nc">&nbsp;            binding.videoView.requestLayout()</b>
<b class="nc">&nbsp;            isFullscreen = !isFullscreen</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.forwardbutton.setOnClickListener {</b>
<b class="nc">&nbsp;            if (binding.videoView.duration &gt; 0) {</b>
<b class="nc">&nbsp;                val pos = (binding.videoView.currentPosition + 10_000)</b>
<b class="nc">&nbsp;                    .coerceAtMost(binding.videoView.duration)</b>
<b class="nc">&nbsp;                binding.videoView.seekTo(pos)</b>
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.replayButton.setOnClickListener {</b>
<b class="nc">&nbsp;            if (binding.videoView.duration &gt; 0) {</b>
<b class="nc">&nbsp;                val pos = (binding.videoView.currentPosition - 10_000)</b>
<b class="nc">&nbsp;                    .coerceAtLeast(0)</b>
<b class="nc">&nbsp;                binding.videoView.seekTo(pos)</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private fun playVideo() {
<b class="nc">&nbsp;        val uri = Uri.parse(&quot;android.resource://${packageName}/${R.raw.sample_video}&quot;)</b>
<b class="nc">&nbsp;        binding.videoView.setVideoURI(uri)</b>
<b class="nc">&nbsp;        binding.videoView.setOnPreparedListener { mp -&gt;</b>
<b class="nc">&nbsp;            videoPlayer = mp</b>
<b class="nc">&nbsp;            binding.videoView.start()</b>
<b class="nc">&nbsp;            binding.playPauseButton.setImageResource(R.drawable.ic_pause)</b>
<b class="nc">&nbsp;            updateSeekBar()</b>
&nbsp;        }
<b class="nc">&nbsp;        binding.videoView.setOnCompletionListener {</b>
<b class="nc">&nbsp;            binding.playPauseButton.setImageResource(R.drawable.ic_replay)</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private fun setupControls() {
<b class="nc">&nbsp;        binding.playPauseButton.setOnClickListener {</b>
<b class="nc">&nbsp;            if (binding.videoView.isPlaying) {</b>
<b class="nc">&nbsp;                binding.videoView.pause()</b>
<b class="nc">&nbsp;                binding.playPauseButton.setImageResource(R.drawable.ic_pause)</b>
&nbsp;            } else {
<b class="nc">&nbsp;                binding.videoView.start()</b>
<b class="nc">&nbsp;                binding.playPauseButton.setImageResource(R.drawable.ic_pause)</b>
&nbsp;            }
&nbsp;        }
&nbsp;
&nbsp;
<b class="nc">&nbsp;        binding.seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {</b>
&nbsp;            override fun onProgressChanged(sb: SeekBar, prog: Int, fromUser: Boolean) {
<b class="nc">&nbsp;                if (fromUser) binding.videoView.seekTo(prog)</b>
&nbsp;            }
<b class="nc">&nbsp;            override fun onStartTrackingTouch(sb: SeekBar) {}</b>
<b class="nc">&nbsp;            override fun onStopTrackingTouch(sb: SeekBar) {}</b>
&nbsp;        })
&nbsp;    }
&nbsp;
&nbsp;    private fun updateSeekBar() {
<b class="nc">&nbsp;        val duration = binding.videoView.duration</b>
<b class="nc">&nbsp;        binding.seekBar.max = duration</b>
&nbsp;
<b class="nc">&nbsp;        val runnable = object : Runnable {</b>
&nbsp;            override fun run() {
<b class="nc">&nbsp;                if (binding.videoView.isPlaying) {</b>
<b class="nc">&nbsp;                    val currentPos = binding.videoView.currentPosition</b>
<b class="nc">&nbsp;                    binding.seekBar.progress = currentPos</b>
&nbsp;
&nbsp;                    // Update time text
<b class="nc">&nbsp;                    val current = formatTime(currentPos)</b>
<b class="nc">&nbsp;                    val total = formatTime(duration)</b>
<b class="nc">&nbsp;                    binding.timeText.text = &quot;$current / $total&quot;</b>
&nbsp;                }
<b class="nc">&nbsp;                handler.postDelayed(this, 500)</b>
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        handler.postDelayed(runnable, 0)</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun formatTime(ms: Int): String {
<b class="nc">&nbsp;        val seconds = ms / 1000 % 60</b>
<b class="nc">&nbsp;        val minutes = ms / 1000 / 60</b>
<b class="nc">&nbsp;        return String.format(&quot;%d:%02d&quot;, minutes, seconds)</b>
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
