


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OneToFourWorkSheetsFragmentKt</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: OneToFourWorkSheetsFragmentKt (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">OneToFourWorkSheetsFragmentKt</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import clt.india.classroom.R
&nbsp;
&nbsp;// TODO: Rename parameter arguments, choose names that match
&nbsp;// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
&nbsp;private const val ARG_PARAM1 = &quot;param1&quot;
&nbsp;private const val ARG_PARAM2 = &quot;param2&quot;
&nbsp;
&nbsp;/**
&nbsp; * A simple [Fragment] subclass.
&nbsp; * Use the [OneToFourWorkSheetsFragment.newInstance] factory method to
&nbsp; * create an instance of this fragment.
&nbsp; */
&nbsp;class OneToFourWorkSheetsFragment : Fragment() {
&nbsp;    // TODO: Rename and change types of parameters
&nbsp;    private var param1: String? = null
&nbsp;    private var param2: String? = null
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
&nbsp;        super.onCreate(savedInstanceState)
&nbsp;        arguments?.let {
&nbsp;            param1 = it.getString(ARG_PARAM1)
&nbsp;            param2 = it.getString(ARG_PARAM2)
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    override fun onCreateView(
&nbsp;        inflater: LayoutInflater, container: ViewGroup?,
&nbsp;        savedInstanceState: Bundle?
&nbsp;    ): View? {
&nbsp;        // Inflate the layout for this fragment
&nbsp;        return inflater.inflate(R.layout.fragment_one_to_four_work_sheets, container, false)
&nbsp;    }
&nbsp;
&nbsp;    companion object {
&nbsp;        /**
&nbsp;         * Use this factory method to create a new instance of
&nbsp;         * this fragment using the provided parameters.
&nbsp;         *
&nbsp;         * @param param1 Parameter 1.
&nbsp;         * @param param2 Parameter 2.
&nbsp;         * @return A new instance of fragment OneToFourWorkSheetsFragment.
&nbsp;         */
&nbsp;        // TODO: Rename and change types and number of parameters
&nbsp;        @JvmStatic
&nbsp;        fun newInstance(param1: String, param2: String) =
&nbsp;            OneToFourWorkSheetsFragment().apply {
&nbsp;                arguments = Bundle().apply {
&nbsp;                    putString(ARG_PARAM1, param1)
&nbsp;                    putString(ARG_PARAM2, param2)
&nbsp;                }
&nbsp;            }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
