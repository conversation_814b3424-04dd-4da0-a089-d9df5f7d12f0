


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OneToFourIndexPageFragment</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: OneToFourIndexPageFragment (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">OneToFourIndexPageFragment</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/64)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.content.Context
&nbsp;import android.os.Bundle
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import android.view.inputmethod.EditorInfo
&nbsp;import android.view.inputmethod.InputMethodManager
&nbsp;import androidx.recyclerview.widget.LinearLayoutManager
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.adapter.ChapterAdapter
&nbsp;import clt.india.classroom.adapter.OneToFourChapterAdapter
&nbsp;import clt.india.classroom.adapter.SubjectTabAdapter
&nbsp;import clt.india.classroom.databinding.FragmentIndexPageBinding
&nbsp;import clt.india.classroom.databinding.FragmentOneToFourIndexPageBinding
&nbsp;import clt.india.classroom.datapck.Chapter
&nbsp;
&nbsp;
<b class="nc">&nbsp;class OneToFourIndexPageFragment : Fragment() {</b>
&nbsp;    private var _binding: FragmentOneToFourIndexPageBinding? = null
<b class="nc">&nbsp;    private val binding get() = _binding!!</b>
&nbsp;    private var selectedSubject: String? = null
&nbsp;    private lateinit var chapterAdapter: OneToFourChapterAdapter
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        arguments?.let {</b>
&nbsp;
<b class="nc">&nbsp;        }</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onCreateView(
&nbsp;        inflater: LayoutInflater, container: ViewGroup?,
&nbsp;        savedInstanceState: Bundle?
&nbsp;    ): View? {
<b class="nc">&nbsp;        _binding = FragmentOneToFourIndexPageBinding.inflate(inflater, container, false)</b>
<b class="nc">&nbsp;        return binding.root</b>
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onViewCreated(view, savedInstanceState)</b>
<b class="nc">&nbsp;        setupSubjectTabs()</b>
<b class="nc">&nbsp;        setupChapterRecyclerView()</b>
<b class="nc">&nbsp;        handleClicks()</b>
&nbsp;    }
&nbsp;    private fun filterChapters(query: String) {
<b class="nc">&nbsp;        val chapters = subjectChaptersMap[selectedSubject ?: &quot;English Grammar&quot;] ?: emptyList()</b>
<b class="nc">&nbsp;        val filtered = chapters.filter {</b>
<b class="nc">&nbsp;            it.chapterNumber.contains(query, ignoreCase = true) ||</b>
<b class="nc">&nbsp;                    it.chapterTitle.contains(query, ignoreCase = true)</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        chapterAdapter.updateChapters(filtered)</b>
&nbsp;    }
&nbsp;    private fun handleClicks() {
<b class="nc">&nbsp;        binding.searchVideo.setOnEditorActionListener { _, actionId, _ -&gt;</b>
<b class="nc">&nbsp;            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {</b>
<b class="nc">&nbsp;                val query = binding.searchVideo.text.toString().trim()</b>
<b class="nc">&nbsp;                filterChapters(query)</b>
<b class="nc">&nbsp;                hideKeyboard() // Hides the keyboard</b>
<b class="nc">&nbsp;                true</b>
&nbsp;            } else {
<b class="nc">&nbsp;                false</b>
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.searchFun.setOnClickListener {</b>
<b class="nc">&nbsp;            val query = binding.searchVideo.text.toString().trim()</b>
<b class="nc">&nbsp;            filterChapters(query)</b>
<b class="nc">&nbsp;            hideKeyboard()</b>
&nbsp;        }
&nbsp;    }
&nbsp;    private fun hideKeyboard() {
<b class="nc">&nbsp;        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager</b>
<b class="nc">&nbsp;        imm.hideSoftInputFromWindow(binding.searchVideo.windowToken, 0)</b>
&nbsp;    }
&nbsp;    private fun setupSubjectTabs() {
<b class="nc">&nbsp;        val subjects = listOf(&quot;Foundational Literacy&quot;, &quot;Numeracy&quot;)</b>
&nbsp;
<b class="nc">&nbsp;        val adapter = SubjectTabAdapter(subjects) { subject -&gt;</b>
<b class="nc">&nbsp;            selectedSubject = subject</b>
<b class="nc">&nbsp;            val chapters = subjectChaptersMap[subject] ?: emptyList()</b>
<b class="nc">&nbsp;            chapterAdapter.updateChapters(chapters)</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext())</b>
<b class="nc">&nbsp;        binding.subjectTabsRecyclerView.adapter = adapter</b>
&nbsp;
&nbsp;    }
&nbsp;    private fun setupChapterRecyclerView() {
<b class="nc">&nbsp;        val defaultSubject = selectedSubject ?: &quot;Foundational Literacy&quot;</b>
<b class="nc">&nbsp;        val chapters = subjectChaptersMap[defaultSubject] ?: emptyList()</b>
<b class="nc">&nbsp;        chapterAdapter = OneToFourChapterAdapter(chapters)</b>
<b class="nc">&nbsp;        binding.chaptersRecyclerView.layoutManager = LinearLayoutManager(requireContext())</b>
<b class="nc">&nbsp;        binding.chaptersRecyclerView.adapter = chapterAdapter</b>
&nbsp;    }
<b class="nc">&nbsp;    private val subjectChaptersMap = mapOf(</b>
<b class="nc">&nbsp;        &quot;Foundational Literacy&quot; to listOf(</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 1&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 2&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 3&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 4&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 5&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 6&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 7&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 8&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 9&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 10&quot;, &quot;Lorem Ipsum&quot;)</b>
&nbsp;
&nbsp;
&nbsp;        ),
<b class="nc">&nbsp;        &quot;Numeracy&quot; to listOf(</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 1&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 2&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 3&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 4&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 5&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 6&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 7&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 8&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 9&quot;, &quot;Lorem Ipsum&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 10&quot;, &quot;Lorem Ipsum&quot;)</b>
&nbsp;        ),
&nbsp;
&nbsp;    )
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
