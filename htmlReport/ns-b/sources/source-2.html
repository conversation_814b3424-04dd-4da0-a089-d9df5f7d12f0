


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > AssesmentViewActivity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: AssesmentViewActivity (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">AssesmentViewActivity</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/57)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.content.Intent
&nbsp;import android.os.Bundle
&nbsp;import android.util.Log
&nbsp;import android.widget.TextView
&nbsp;import androidx.appcompat.app.AppCompatActivity
&nbsp;import androidx.core.content.ContextCompat
&nbsp;import androidx.recyclerview.widget.LinearLayoutManager
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.adapter.QuestionAdapter
&nbsp;import clt.india.classroom.databinding.ActivityAssesmentViewBinding
&nbsp;import clt.india.classroom.datapck.Question
&nbsp;import com.google.android.material.tabs.TabLayout
&nbsp;
<b class="nc">&nbsp;class AssesmentViewActivity : AppCompatActivity() {</b>
&nbsp;
&nbsp;    private lateinit var binding: ActivityAssesmentViewBinding
&nbsp;    private lateinit var questionAdapter: QuestionAdapter
<b class="nc">&nbsp;    private var assessment1Questions = listOf&lt;Question&gt;()</b>
<b class="nc">&nbsp;    private var assessment2Questions = listOf&lt;Question&gt;()</b>
<b class="nc">&nbsp;    private var currentQuestions = listOf&lt;Question&gt;()</b>
&nbsp;
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
&nbsp;
<b class="nc">&nbsp;        supportActionBar?.hide()</b>
<b class="nc">&nbsp;        binding = ActivityAssesmentViewBinding.inflate(layoutInflater)</b>
<b class="nc">&nbsp;        setContentView(binding.root)</b>
&nbsp;
<b class="nc">&nbsp;        setupSampleData()</b>
<b class="nc">&nbsp;        setupTabs()</b>
&nbsp;
<b class="nc">&nbsp;        setupRecyclerView(assessment1Questions)</b>
&nbsp;
<b class="nc">&nbsp;        handleclicks()</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun handleclicks() {
&nbsp;
<b class="nc">&nbsp;            binding.submitButton.setOnClickListener {</b>
<b class="nc">&nbsp;                val intent = Intent(this, AssesmentSubmissionActivity::class.java)</b>
<b class="nc">&nbsp;                startActivity(intent)</b>
&nbsp;            }
&nbsp;
<b class="nc">&nbsp;        binding.backButton.setOnClickListener{</b>
<b class="nc">&nbsp;            onBackPressedDispatcher.onBackPressed()</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private fun setupSampleData() {
<b class="nc">&nbsp;        assessment1Questions = listOf(</b>
<b class="nc">&nbsp;            Question(</b>
<b class="nc">&nbsp;                1,</b>
<b class="nc">&nbsp;                &quot;The method of separating seeds of paddy from their stalks is called?&quot;,</b>
<b class="nc">&nbsp;                listOf(&quot;Threshing&quot;, &quot;Winnowing&quot;, &quot;Sieving&quot;, &quot;Filtration&quot;)</b>
&nbsp;            ),
<b class="nc">&nbsp;            Question(</b>
<b class="nc">&nbsp;                2,</b>
<b class="nc">&nbsp;                &quot;The process of heating milk to kill bacteria is known as?&quot;,</b>
<b class="nc">&nbsp;                listOf(&quot;Boiling&quot;, &quot;Pasteurization&quot;, &quot;Freezing&quot;, &quot;Evaporation&quot;)</b>
&nbsp;            )
&nbsp;        )
&nbsp;
<b class="nc">&nbsp;        assessment2Questions = listOf(</b>
<b class="nc">&nbsp;            Question(</b>
<b class="nc">&nbsp;                1,</b>
<b class="nc">&nbsp;                &quot;Which gas is responsible for global warming?&quot;,</b>
<b class="nc">&nbsp;                listOf(&quot;Oxygen&quot;, &quot;Carbon Dioxide&quot;, &quot;Nitrogen&quot;, &quot;Hydrogen&quot;)</b>
&nbsp;            ),
<b class="nc">&nbsp;            Question(</b>
<b class="nc">&nbsp;                2,</b>
<b class="nc">&nbsp;                &quot;Which is used to measure temperature?&quot;,</b>
<b class="nc">&nbsp;                listOf(&quot;Barometer&quot;, &quot;Thermometer&quot;, &quot;Hygrometer&quot;, &quot;Speedometer&quot;)</b>
&nbsp;            )
&nbsp;        )
&nbsp;    }
&nbsp;
&nbsp;    private fun setupRecyclerView(questions: List&lt;Question&gt;) {
<b class="nc">&nbsp;        currentQuestions = questions</b>
<b class="nc">&nbsp;        questionAdapter = QuestionAdapter(currentQuestions)</b>
<b class="nc">&nbsp;        binding.assessmentRecyclerView.layoutManager = LinearLayoutManager(this)</b>
<b class="nc">&nbsp;        binding.assessmentRecyclerView.adapter = questionAdapter</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun setupTabs() {
<b class="nc">&nbsp;        val tab1 = binding.tabAssessment1</b>
<b class="nc">&nbsp;        val tab2 = binding.tabAssessment2</b>
&nbsp;
&nbsp;        // Default selection
<b class="nc">&nbsp;        selectTab(tab1, isSelected = true)</b>
<b class="nc">&nbsp;        selectTab(tab2, isSelected = false)</b>
<b class="nc">&nbsp;        setupRecyclerView(assessment1Questions) // Load default</b>
&nbsp;
<b class="nc">&nbsp;        tab1.setOnClickListener {</b>
<b class="nc">&nbsp;            selectTab(tab1, true)</b>
<b class="nc">&nbsp;            selectTab(tab2, false)</b>
<b class="nc">&nbsp;            setupRecyclerView(assessment1Questions)</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        tab2.setOnClickListener {</b>
<b class="nc">&nbsp;            selectTab(tab1, false)</b>
<b class="nc">&nbsp;            selectTab(tab2, true)</b>
<b class="nc">&nbsp;            setupRecyclerView(assessment2Questions)</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private fun selectTab(tab: TextView, isSelected: Boolean) {
<b class="nc">&nbsp;        if (isSelected) {</b>
<b class="nc">&nbsp;            tab.setBackgroundResource(R.drawable.selected_tab_background)</b>
<b class="nc">&nbsp;            tab.setTextColor(ContextCompat.getColor(this, android.R.color.white))</b>
&nbsp;        } else {
<b class="nc">&nbsp;            tab.setBackgroundResource(R.drawable.unselected_tab_background)</b>
<b class="nc">&nbsp;            tab.setTextColor(ContextCompat.getColor(this, android.R.color.black))</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
