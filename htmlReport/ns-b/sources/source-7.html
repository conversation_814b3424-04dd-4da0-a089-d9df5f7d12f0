


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > IndexPageFragment</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: IndexPageFragment (clt.india.classroom.ui)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">IndexPageFragment</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/77)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.content.Context
&nbsp;import android.os.Bundle
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import android.view.inputmethod.EditorInfo
&nbsp;import android.view.inputmethod.InputMethodManager
&nbsp;import androidx.core.content.ContextCompat.getSystemService
&nbsp;import androidx.recyclerview.widget.LinearLayoutManager
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.adapter.ChapterAdapter
&nbsp;import clt.india.classroom.adapter.SubjectTabAdapter
&nbsp;import clt.india.classroom.databinding.FragmentGradeSelectionBinding
&nbsp;import clt.india.classroom.databinding.FragmentIndexPageBinding
&nbsp;import clt.india.classroom.datapck.Chapter
&nbsp;import com.google.android.material.button.MaterialButton
&nbsp;
&nbsp;
<b class="nc">&nbsp;class IndexPageFragment : Fragment() {</b>
&nbsp;
&nbsp;    private var _binding: FragmentIndexPageBinding? = null
<b class="nc">&nbsp;    private val binding get() = _binding!!</b>
&nbsp;    private var selectedSubject: String? = null
&nbsp;    private var selectedGrade: String? = null
&nbsp;    private lateinit var chapterAdapter: ChapterAdapter
&nbsp;    override fun onCreate(savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onCreate(savedInstanceState)</b>
<b class="nc">&nbsp;        arguments?.let {</b>
<b class="nc">&nbsp;            selectedGrade = arguments?.let {</b>
<b class="nc">&nbsp;                IndexPageFragmentArgs.fromBundle(it).selectedGrade</b>
&nbsp;            }
<b class="nc">&nbsp;        }</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onCreateView(
&nbsp;        inflater: LayoutInflater, container: ViewGroup?,
&nbsp;        savedInstanceState: Bundle?
&nbsp;    ): View? {
<b class="nc">&nbsp;        _binding = FragmentIndexPageBinding.inflate(inflater, container, false)</b>
<b class="nc">&nbsp;        return binding.root</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onViewCreated(view, savedInstanceState)</b>
<b class="nc">&nbsp;        setupSubjectTabs()</b>
<b class="nc">&nbsp;        setupChapterRecyclerView()</b>
<b class="nc">&nbsp;        handleClicks()</b>
&nbsp;    }
&nbsp;    private fun setupSubjectTabs() {
<b class="nc">&nbsp;        val subjects = listOf(&quot;English Grammar&quot;, &quot;Science&quot;, &quot;MatheMatics&quot;)</b>
&nbsp;
<b class="nc">&nbsp;        val adapter = SubjectTabAdapter(subjects) { subject -&gt;</b>
<b class="nc">&nbsp;            selectedSubject = subject</b>
<b class="nc">&nbsp;            val chapters = subjectChaptersMap[subject] ?: emptyList()</b>
<b class="nc">&nbsp;            chapterAdapter.updateChapters(chapters)</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext())</b>
<b class="nc">&nbsp;        binding.subjectTabsRecyclerView.adapter = adapter</b>
&nbsp;    }
&nbsp;    private fun filterChapters(query: String) {
<b class="nc">&nbsp;        val chapters = subjectChaptersMap[selectedSubject ?: &quot;English Grammar&quot;] ?: emptyList()</b>
<b class="nc">&nbsp;        val filtered = chapters.filter {</b>
<b class="nc">&nbsp;            it.chapterNumber.contains(query, ignoreCase = true) ||</b>
<b class="nc">&nbsp;                    it.chapterTitle.contains(query, ignoreCase = true)</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        chapterAdapter.updateChapters(filtered)</b>
&nbsp;    }
&nbsp;    private fun handleClicks() {
<b class="nc">&nbsp;        binding.searchVideo.setOnEditorActionListener { _, actionId, _ -&gt;</b>
<b class="nc">&nbsp;            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {</b>
<b class="nc">&nbsp;                val query = binding.searchVideo.text.toString().trim()</b>
<b class="nc">&nbsp;                filterChapters(query)</b>
<b class="nc">&nbsp;                hideKeyboard() // Hides the keyboard</b>
<b class="nc">&nbsp;                true</b>
&nbsp;            } else {
<b class="nc">&nbsp;                false</b>
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        binding.searchFun.setOnClickListener {</b>
<b class="nc">&nbsp;            val query = binding.searchVideo.text.toString().trim()</b>
<b class="nc">&nbsp;            filterChapters(query)</b>
<b class="nc">&nbsp;            hideKeyboard()</b>
&nbsp;        }
&nbsp;    }
&nbsp;    private fun hideKeyboard() {
<b class="nc">&nbsp;        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager</b>
<b class="nc">&nbsp;        imm.hideSoftInputFromWindow(binding.searchVideo.windowToken, 0)</b>
&nbsp;    }
&nbsp;
&nbsp;    private fun setupChapterRecyclerView() {
<b class="nc">&nbsp;        val defaultSubject = selectedSubject ?: &quot;English Grammar&quot;</b>
<b class="nc">&nbsp;        val chapters = subjectChaptersMap[defaultSubject] ?: emptyList()</b>
<b class="nc">&nbsp;        chapterAdapter = ChapterAdapter(chapters)</b>
<b class="nc">&nbsp;        binding.chaptersRecyclerView.layoutManager = LinearLayoutManager(requireContext())</b>
<b class="nc">&nbsp;        binding.chaptersRecyclerView.adapter = chapterAdapter</b>
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    private val subjectChaptersMap = mapOf(</b>
<b class="nc">&nbsp;        &quot;English Grammar&quot; to listOf(</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 1&quot;, &quot;Nouns&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 2&quot;, &quot;Pronouns&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 3&quot;, &quot;Verbs&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 4&quot;, &quot;Nouns&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 5&quot;, &quot;Pronouns&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 6&quot;, &quot;Verbs&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 7&quot;, &quot;Nouns&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 8&quot;, &quot;Pronouns&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 9&quot;, &quot;Verbs&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 10&quot;, &quot;Verbs&quot;)</b>
&nbsp;
&nbsp;
&nbsp;        ),
<b class="nc">&nbsp;        &quot;Science&quot; to listOf(</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 1&quot;, &quot;Air&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 2&quot;, &quot;Water&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 3&quot;, &quot;Plants&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 4&quot;, &quot;Air&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 5&quot;, &quot;Water&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 6&quot;, &quot;Plants&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 7&quot;, &quot;Air&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 8&quot;, &quot;Water&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 9&quot;, &quot;Plants&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 10&quot;, &quot;Air&quot;),</b>
&nbsp;        ),
<b class="nc">&nbsp;        &quot;MatheMatics&quot; to listOf(</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 1&quot;, &quot;Addition&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 2&quot;, &quot;Subtraction&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 3&quot;, &quot;Multiplication&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 4&quot;, &quot;Addition&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 5&quot;, &quot;Subtraction&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 6&quot;, &quot;Multiplication&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 7&quot;, &quot;Multiplication&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 8&quot;, &quot;Addition&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 9&quot;, &quot;Subtraction&quot;),</b>
<b class="nc">&nbsp;            Chapter(&quot;Chapter 10&quot;, &quot;Multiplication&quot;),</b>
&nbsp;
&nbsp;        )
&nbsp;    )
&nbsp;
&nbsp;
&nbsp;
&nbsp;
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
