


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > VideoFragment</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.ui</a>
</div>

<h1>Coverage Summary for Class: VideoFragment (clt.india.classroom.ui)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">VideoFragment</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/17)
  </span>
</td>
</tr>
  <tr>
    <td class="name">VideoFragment$Companion</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.ui
&nbsp;
&nbsp;import android.os.Bundle
&nbsp;import androidx.fragment.app.Fragment
&nbsp;import android.view.LayoutInflater
&nbsp;import android.view.View
&nbsp;import android.view.ViewGroup
&nbsp;import android.widget.Toast
&nbsp;import androidx.recyclerview.widget.GridLayoutManager
&nbsp;import androidx.recyclerview.widget.LinearLayoutManager
&nbsp;import clt.india.classroom.R
&nbsp;import clt.india.classroom.adapter.ChapterDetailsAdapter
&nbsp;import clt.india.classroom.databinding.FragmentVideoBinding
&nbsp;
&nbsp;
<b class="nc">&nbsp;class VideoFragment : Fragment() {</b>
&nbsp;
&nbsp;    private var _binding: FragmentVideoBinding? = null
<b class="nc">&nbsp;    private val binding get() = _binding!!</b>
&nbsp;
&nbsp;    private lateinit var chapterAdapter: ChapterDetailsAdapter
&nbsp;
&nbsp;    override fun onCreateView(
&nbsp;        inflater: LayoutInflater, container: ViewGroup?,
&nbsp;        savedInstanceState: Bundle?
&nbsp;    ): View {
<b class="nc">&nbsp;        _binding = FragmentVideoBinding.inflate(inflater, container, false)</b>
<b class="nc">&nbsp;        return binding.root</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
<b class="nc">&nbsp;        super.onViewCreated(view, savedInstanceState)</b>
&nbsp;
<b class="nc">&nbsp;        val images = listOf(</b>
<b class="nc">&nbsp;            R.drawable.image1, R.drawable.image1, R.drawable.image1,</b>
<b class="nc">&nbsp;            R.drawable.image1, R.drawable.image1, R.drawable.image1</b>
&nbsp;        )
&nbsp;
<b class="nc">&nbsp;        val titles = listOf(</b>
<b class="nc">&nbsp;            &quot;Chapter 1&quot;, &quot;Chapter 2&quot;, &quot;Chapter 3&quot;,</b>
<b class="nc">&nbsp;            &quot;Chapter 4&quot;, &quot;Chapter 5&quot;, &quot;Chapter 6&quot;</b>
&nbsp;        )
&nbsp;
<b class="nc">&nbsp;        chapterAdapter = ChapterDetailsAdapter(images, titles)</b>
&nbsp;
<b class="nc">&nbsp;        binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)</b>
<b class="nc">&nbsp;        binding.videoRecyclerView.adapter = chapterAdapter</b>
&nbsp;
<b class="nc">&nbsp;        println(&quot;DEBUG: Adapter set with ${titles.size} items&quot;)</b>
&nbsp;    }
&nbsp;
&nbsp;    override fun onDestroyView() {
<b class="nc">&nbsp;        super.onDestroyView()</b>
<b class="nc">&nbsp;        _binding = null</b>
&nbsp;    }
&nbsp;
&nbsp;    companion object {
&nbsp;        fun newInstance(): VideoFragment {
<b class="nc">&nbsp;            return VideoFragment()</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
