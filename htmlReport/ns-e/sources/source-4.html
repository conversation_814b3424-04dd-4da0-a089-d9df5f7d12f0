


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > LoginViewModel</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">clt.india.classroom.viewmodel</a>
</div>

<h1>Coverage Summary for Class: LoginViewModel (clt.india.classroom.viewmodel)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">LoginViewModel</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
</tr>
  <tr>
    <td class="name">LoginViewModel$loginUser$1</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package clt.india.classroom.viewmodel
&nbsp;
&nbsp;import android.app.Application
&nbsp;import androidx.lifecycle.*
&nbsp;import clt.india.classroom.data.api.ApiClient
&nbsp;import clt.india.classroom.data.api.methods.UserApi
&nbsp;import clt.india.classroom.data.api.request.LoginRequest
&nbsp;import clt.india.classroom.data.api.response.BaseResponse
&nbsp;import clt.india.classroom.data.api.response.LoginResponse
&nbsp;import clt.india.classroom.repository.UserRepository
&nbsp;import kotlinx.coroutines.launch
&nbsp;
<b class="nc">&nbsp;class LoginViewModel(application: Application) : AndroidViewModel(application) {</b>
&nbsp;
<b class="nc">&nbsp;    val userRepo = UserRepository()</b>
<b class="nc">&nbsp;    val loginResult: MutableLiveData&lt;BaseResponse&lt;LoginResponse&gt;&gt; = MutableLiveData()</b>
&nbsp;
&nbsp;    fun loginUser(email: String, pwd: String) {
&nbsp;
<b class="nc">&nbsp;        loginResult.value = BaseResponse.Loading()</b>
<b class="nc">&nbsp;        viewModelScope.launch {</b>
&nbsp;            try {
&nbsp;
<b class="nc">&nbsp;                val loginRequest = LoginRequest(</b>
<b class="nc">&nbsp;                    password = pwd,</b>
<b class="nc">&nbsp;                    email = email</b>
&nbsp;                )
<b class="nc">&nbsp;                val response = userRepo.loginUser(loginRequest = loginRequest)</b>
<b class="nc">&nbsp;                if (response?.code() == 200) {</b>
<b class="nc">&nbsp;                    loginResult.value = BaseResponse.Success(response.body())</b>
&nbsp;                } else {
<b class="nc">&nbsp;                    loginResult.value = BaseResponse.Error(response?.message())</b>
&nbsp;                }
&nbsp;
&nbsp;            } catch (ex: Exception) {
<b class="nc">&nbsp;                loginResult.value = BaseResponse.Error(ex.message)</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-05-27 12:05</div>
</div>
</body>
</html>
