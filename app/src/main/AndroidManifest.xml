<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-feature android:name="android.hardware.usb.host" />
    <uses-permission android:name="android.permission.USB_PERMISSION" />


    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />

    <application
        android:name=".CLTApplication"
        android:allowBackup="true"
        android:banner="@drawable/splash_logo"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.RetrofitLogin"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".ui.assesment.AssesmentshostActivity"
            android:exported="false" />
        <activity
            android:name=".ui.splash.StartUpActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.splash.SplashVideoActivity"
            android:exported="false" />
        <activity
            android:name=".ui.DeviceIdVerificationActivity"
            android:exported="false" />
        <activity
            android:name=".ui.egr.OneToFourHomeActivity"
            android:exported="false" />
        <activity
            android:name=".ui.resources.TextBookViewActivity"
            android:exported="false" />
        <activity
            android:name=".ui.resources.VideoPlayingActivity"
            android:exported="false" />

        <activity
            android:name=".ui.resources.VideoPlayingActivityOld"
            android:exported="false" />
        <activity
            android:name=".ui.gradeselection.HomeActivity"
            android:exported="false"
            android:theme="@style/Theme.RetrofitLogin" /> <!-- <activity -->
        <!-- android:name=".ui.HomeActivity" -->
        <!-- android:banner="@drawable/app_icon_your_company" -->
        <!-- android:exported="true" -->
        <!-- android:icon="@drawable/app_icon_your_company" -->
        <!-- android:label="@string/title_activity_home" -->
        <!-- android:logo="@drawable/app_icon_your_company" -->
        <!-- android:screenOrientation="landscape"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.intent.action.MAIN" /> -->
        <!-- <category android:name="android.intent.category.LEANBACK_LAUNCHER" /> -->
        <!-- </intent-filter> -->
        <!-- </activity> -->
        <activity
            android:name=".ui.egr.ClassroomLessonsActivity"
            android:exported="false"
            android:theme="@style/Theme.RetrofitLogin" >
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>
            <!--            <meta-data android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"  />-->

        </activity>

        <activity
            android:name=".ui.LogoutActivity"
            android:exported="false" />
        <activity
            android:name=".ui.MainActivity"
            android:exported="false" />
    </application>

</manifest>