package clt.india.classroom.examples

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import clt.india.classroom.ui.resources.VideoFragment5To10

/**
 * Example showing how to properly use VideoFragment5To10
 * and handle subject selection
 */
class VideoFragment5To10Usage : AppCompatActivity() {

    private var currentVideoFragment: VideoFragment5To10? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Example 1: Create fragment with a specific subject
        loadVideoFragmentWithSubject("Mathematics")
        
        // Example 2: Create fragment without subject (will use first available)
        // loadVideoFragmentWithoutSubject()
    }

    /**
     * Method 1: Load fragment with a specific subject
     */
    private fun loadVideoFragmentWithSubject(subject: String) {
        Log.i("VideoFragment5To10Usage", "Loading video fragment with subject: $subject")
        
        currentVideoFragment = VideoFragment5To10.newInstance(subject)
        
        supportFragmentManager.beginTransaction()
            .replace(android.R.id.content, currentVideoFragment!!)
            .commit()
    }

    /**
     * Method 2: Load fragment without specifying subject
     */
    private fun loadVideoFragmentWithoutSubject() {
        Log.i("VideoFragment5To10Usage", "Loading video fragment without subject")
        
        currentVideoFragment = VideoFragment5To10.newInstance(null)
        
        supportFragmentManager.beginTransaction()
            .replace(android.R.id.content, currentVideoFragment!!)
            .commit()
    }

    /**
     * Method 3: Change subject after fragment is loaded
     */
    private fun changeSubject(newSubject: String) {
        Log.i("VideoFragment5To10Usage", "Changing subject to: $newSubject")
        
        currentVideoFragment?.let { fragment ->
            // Check if fragment is added and visible
            if (fragment.isAdded && fragment.isVisible) {
                fragment.onSubjectChanged(newSubject)
            } else {
                Log.w("VideoFragment5To10Usage", "Fragment is not ready for subject change")
            }
        } ?: run {
            Log.w("VideoFragment5To10Usage", "No video fragment available")
        }
    }

    /**
     * Method 4: Get available subjects for debugging
     */
    private fun getAvailableSubjects() {
        currentVideoFragment?.let { fragment ->
            if (fragment.isAdded) {
                val subjects = fragment.getAvailableSubjects()
                Log.i("VideoFragment5To10Usage", "Available subjects: $subjects")
            }
        }
    }

    /**
     * Example of handling subject selection from a tab or spinner
     */
    private fun onSubjectTabClicked(subject: String) {
        Log.i("VideoFragment5To10Usage", "Subject tab clicked: $subject")
        changeSubject(subject)
    }

    /**
     * Example of handling subject selection from a dropdown
     */
    private fun onSubjectDropdownSelected(subject: String) {
        Log.i("VideoFragment5To10Usage", "Subject dropdown selected: $subject")
        changeSubject(subject)
    }
}

/**
 * Example Fragment that contains VideoFragment5To10 as a child fragment
 */
class ParentFragmentExample : Fragment() {

    private var videoFragment: VideoFragment5To10? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create the video fragment
        videoFragment = VideoFragment5To10.newInstance("English")
        
        // Add it as a child fragment
        childFragmentManager.beginTransaction()
            .replace(android.R.id.content, videoFragment!!)
            .commit()
    }

    /**
     * Method to change subject from parent fragment
     */
    fun changeVideoSubject(newSubject: String) {
        videoFragment?.let { fragment ->
            if (fragment.isAdded) {
                fragment.onSubjectChanged(newSubject)
            }
        }
    }

    /**
     * Method to get available subjects
     */
    fun getVideoSubjects(): List<String> {
        return videoFragment?.let { fragment ->
            if (fragment.isAdded) {
                fragment.getAvailableSubjects()
            } else {
                emptyList()
            }
        } ?: emptyList()
    }
}

/**
 * Common issues and solutions
 */
object VideoFragment5To10Troubleshooting {

    /**
     * Issue 1: Empty chapter details until subject selection
     * Solution: Ensure data is loaded properly in onViewCreated
     */
    fun issue1_EmptyChapterDetails() {
        Log.i("Troubleshooting", """
            Issue: Empty chapter details until subject selection
            
            Possible causes:
            1. CLT Videos data is not loaded
            2. Subject is null or invalid
            3. Data parsing failed
            4. Grade/Language mismatch
            
            Solutions:
            1. Check if PrefUtilsManager.getFromPrefs(Constant.CLTVIDEO) returns valid data
            2. Verify selectedGrade and selectedLang match the data structure
            3. Check logs for parsing errors
            4. Ensure loadInitialData() is called in onViewCreated
        """.trimIndent())
    }

    /**
     * Issue 2: Subject change not working
     * Solution: Use onSubjectChanged() method
     */
    fun issue2_SubjectChangeNotWorking() {
        Log.i("Troubleshooting", """
            Issue: Subject change not working
            
            Solution:
            // Correct way to change subject
            videoFragment.onSubjectChanged("New Subject")
            
            // Don't recreate the fragment, just update the data
        """.trimIndent())
    }

    /**
     * Issue 3: Data not loading
     * Solution: Check data availability
     */
    fun issue3_DataNotLoading() {
        Log.i("Troubleshooting", """
            Issue: Data not loading
            
            Debug steps:
            1. Check debugDataAvailability() output in logs
            2. Verify CLT Videos JSON is not null/empty
            3. Check if selectedGrade and selectedLang are correct
            4. Verify data structure matches expected format
        """.trimIndent())
    }
}
