package clt.india.classroom.examples

import clt.india.classroom.data.api.request.Children
import clt.india.classroom.utils.ContentFilterUtils

/**
 * Comprehensive guide showing different ways to use ContentFilterUtils
 * in OneToFourVideoFragment and OneToFourWorkSheetsFragment
 */
object FilterUsageGuide {

    // ========== VIDEO FRAGMENT FILTERING OPTIONS ==========
    
    /**
     * Option 1: Basic exclusion filtering for videos
     * Excludes specific patterns from the list
     */
    fun videoFilter_BasicExclusion(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterChildren(
            children = originalChildren,
            exclusionPatterns = listOf(
                "listening comprehension - activity.mp4",
                "worksheet",
                "activity.mp4",
                "test.mp4",
                "quiz.mp4"
            ),
            logTag = "VideoFragment_BasicExclusion"
        )
    }

    /**
     * Option 2: File type filtering for videos
     * Only allows specific video file types
     */
    fun videoFilter_FileType(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterByFileType(
            children = originalChildren,
            allowedTypes = listOf("mp4", "avi", "mov", "mkv"),
            logTag = "VideoFragment_FileType"
        )
    }

    /**
     * Option 3: Custom logic for videos
     * Complex filtering with multiple conditions
     */
    fun videoFilter_CustomLogic(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterCustom(
            children = originalChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                // Include only if:
                // 1. It's a video file AND
                // 2. Doesn't contain "activity" AND
                // 3. Doesn't contain "worksheet" AND
                // 4. File size is greater than 0 (if available)
                name.endsWith(".mp4") && 
                !name.contains("activity") && 
                !name.contains("worksheet") &&
                (child.size ?: 0) > 0
            },
            logTag = "VideoFragment_CustomLogic"
        )
    }

    /**
     * Option 4: Grade-specific video filtering
     * Different filters based on grade level
     */
    fun videoFilter_GradeSpecific(
        originalChildren: ArrayList<Children>, 
        grade: String
    ): ArrayList<Children> {
        val exclusionPatterns = when (grade) {
            "Grade 1" -> listOf("advanced", "complex", "worksheet", "activity.mp4")
            "Grade 2" -> listOf("worksheet", "activity.mp4", "test.mp4")
            "Grade 3" -> listOf("worksheet", "listening comprehension - activity.mp4")
            "Grade 4" -> listOf("worksheet", "basic", "simple")
            else -> listOf("worksheet", "activity.mp4")
        }
        
        return ContentFilterUtils.filterChildren(
            children = originalChildren,
            exclusionPatterns = exclusionPatterns,
            logTag = "VideoFragment_Grade$grade"
        )
    }

    // ========== WORKSHEET FRAGMENT FILTERING OPTIONS ==========

    /**
     * Option 1: Custom filtering for worksheets
     * Includes only worksheet-related content
     */
    fun worksheetFilter_CustomInclusion(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterCustom(
            children = originalChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                // Include if it contains "worksheet" OR is a document file
                name.contains("worksheet") || 
                name.endsWith(".pdf") || 
                name.endsWith(".doc") || 
                name.endsWith(".docx") ||
                name.endsWith(".txt")
            },
            logTag = "WorksheetFragment_CustomInclusion"
        )
    }

    /**
     * Option 2: Document file type filtering
     * Only allows document file types
     */
    fun worksheetFilter_DocumentTypes(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterByFileType(
            children = originalChildren,
            allowedTypes = listOf("pdf", "doc", "docx", "txt", "rtf"),
            logTag = "WorksheetFragment_DocumentTypes"
        )
    }

    /**
     * Option 3: Exclude video files
     * Keeps everything except video files
     */
    fun worksheetFilter_ExcludeVideos(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterExcludeFileTypes(
            children = originalChildren,
            excludedTypes = listOf("mp4", "avi", "mov", "mkv", "wmv"),
            logTag = "WorksheetFragment_ExcludeVideos"
        )
    }

    /**
     * Option 4: Exclusion-based worksheet filtering
     * Excludes video-related patterns
     */
    fun worksheetFilter_ExclusionBased(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return ContentFilterUtils.filterChildren(
            children = originalChildren,
            exclusionPatterns = listOf(
                ".mp4",
                ".avi", 
                ".mov",
                "video",
                "listening comprehension",
                "activity.mp4"
            ),
            logTag = "WorksheetFragment_ExclusionBased"
        )
    }

    /**
     * Option 5: Subject-specific worksheet filtering
     * Different filters based on subject
     */
    fun worksheetFilter_SubjectSpecific(
        originalChildren: ArrayList<Children>,
        subject: String
    ): ArrayList<Children> {
        return ContentFilterUtils.filterCustom(
            children = originalChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                when (subject.lowercase()) {
                    "math", "mathematics" -> {
                        name.contains("worksheet") && 
                        (name.contains("math") || name.contains("number") || name.contains("calculation"))
                    }
                    "english" -> {
                        name.contains("worksheet") && 
                        (name.contains("reading") || name.contains("writing") || name.contains("grammar"))
                    }
                    "science" -> {
                        name.contains("worksheet") && 
                        (name.contains("experiment") || name.contains("observation") || name.contains("science"))
                    }
                    else -> {
                        name.contains("worksheet") || name.endsWith(".pdf")
                    }
                }
            },
            logTag = "WorksheetFragment_$subject"
        )
    }

    // ========== COMBINED/CHAINED FILTERING ==========

    /**
     * Chained filtering example
     * Apply multiple filters in sequence
     */
    fun chainedFiltering(originalChildren: ArrayList<Children>): ArrayList<Children> {
        return originalChildren
            .let { ContentFilterUtils.filterChildren(it, listOf("unwanted_pattern")) }
            .let { ContentFilterUtils.filterByFileType(it, listOf("mp4", "pdf")) }
            .let { ContentFilterUtils.filterCustom(it) { child -> 
                (child.size ?: 0) > 1000 // Only files larger than 1KB
            }}
    }

    /**
     * Conditional filtering based on user preferences
     */
    fun conditionalFiltering(
        originalChildren: ArrayList<Children>,
        showWorksheets: Boolean,
        showVideos: Boolean,
        userGrade: String
    ): ArrayList<Children> {
        return ContentFilterUtils.filterCustom(
            children = originalChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                
                val isWorksheet = name.contains("worksheet") || name.endsWith(".pdf")
                val isVideo = name.endsWith(".mp4") || name.endsWith(".avi")
                
                when {
                    showWorksheets && showVideos -> true // Show everything
                    showWorksheets && !showVideos -> isWorksheet
                    !showWorksheets && showVideos -> isVideo && !name.contains("activity")
                    else -> false // Show nothing
                }
            },
            logTag = "ConditionalFilter_Grade$userGrade"
        )
    }
}
