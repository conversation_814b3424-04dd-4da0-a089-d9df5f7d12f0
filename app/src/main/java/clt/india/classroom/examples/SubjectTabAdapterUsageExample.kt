package clt.india.classroom.examples

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.adapter.SubjectTabAdapter5To10

/**
 * Example showing how to properly use SubjectTabAdapter5To10
 */
class SubjectTabAdapterUsageExample : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: SubjectTabAdapter5To10

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupRecyclerView()
        setupAdapter()
    }

    private fun setupRecyclerView() {
        // Assuming you have a RecyclerView in your layout
        // recyclerView = findViewById(R.id.recyclerViewSubjects)
        // recyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
    }

    private fun setupAdapter() {
        val subjects = listOf("Mathematics", "Science", "English", "Social Studies", "Hindi")
        
        // Method 1: Using both onTabClick callback and OnItemClickListener
        adapter = SubjectTabAdapter5To10(
            subjects = subjects,
            selectedPosition = 0,
            onTabClick = { subject ->
                Log.d("TabAdapter", "Tab clicked via callback: $subject")
                Toast.makeText(this, "Selected: $subject", Toast.LENGTH_SHORT).show()
            }
        )
        
        // Set the OnItemClickListener
        adapter.setOnItemClickListener(object : SubjectTabAdapter5To10.OnItemClickListener {
            override fun onItemClick(position: Int) {
                Log.d("TabAdapter", "Tab clicked via listener at position: $position")
                val subject = subjects[position]
                // Handle the click - navigate to subject details, update UI, etc.
                handleSubjectSelection(subject, position)
            }
        })
        
        // recyclerView.adapter = adapter
    }

    private fun handleSubjectSelection(subject: String, position: Int) {
        // Example of handling subject selection
        when (subject) {
            "Mathematics" -> {
                // Navigate to math content
                Log.d("Navigation", "Navigating to Mathematics content")
            }
            "Science" -> {
                // Navigate to science content
                Log.d("Navigation", "Navigating to Science content")
            }
            "English" -> {
                // Navigate to english content
                Log.d("Navigation", "Navigating to English content")
            }
            // Add more cases as needed
            else -> {
                Log.d("Navigation", "Navigating to $subject content")
            }
        }
    }

    // Method 2: Using only OnItemClickListener (without onTabClick callback)
    private fun setupAdapterWithListenerOnly() {
        val subjects = listOf("Mathematics", "Science", "English", "Social Studies", "Hindi")
        
//        adapter = SubjectTabAdapter5To10(
//            subjects = subjects,
//            selectedPosition = 0
//            // onTabClick is null by default
//        )
//
        adapter.setOnItemClickListener(object : SubjectTabAdapter5To10.OnItemClickListener {
            override fun onItemClick(position: Int) {
                val subject = subjects[position]
                Toast.makeText(this@SubjectTabAdapterUsageExample, "Selected: $subject", Toast.LENGTH_SHORT).show()
                handleSubjectSelection(subject, position)
            }
        })
        
        // recyclerView.adapter = adapter
    }

    // Method 3: Using only onTabClick callback (without OnItemClickListener)
    private fun setupAdapterWithCallbackOnly() {
        val subjects = listOf("Mathematics", "Science", "English", "Social Studies", "Hindi")
        
        adapter = SubjectTabAdapter5To10(
            subjects = subjects,
            selectedPosition = 0,
            onTabClick = { subject ->
                Toast.makeText(this, "Selected: $subject", Toast.LENGTH_SHORT).show()
                // Handle the selection directly here
                val position = subjects.indexOf(subject)
                handleSubjectSelection(subject, position)
            }
        )
        
        // No need to set OnItemClickListener in this case
        // recyclerView.adapter = adapter
    }

    // Example of programmatically updating the selected position
    private fun updateSelectedSubject(newPosition: Int) {
        adapter.updateSelectedPosition(newPosition)
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clear the listener to prevent memory leaks
        if (::adapter.isInitialized) {
            adapter.clearOnItemClickListener()
        }
    }
}
