package clt.india.classroom.examples

import clt.india.classroom.data.api.request.Children
import clt.india.classroom.utils.ContentFilterUtils

/**
 * Examples showing different ways to filter content using ContentFilterUtils
 */
class ContentFilteringExamples {

//    fun example1_BasicFiltering(originalChildren: ArrayList<Children>): ArrayList<Children> {
//        // Basic filtering using default exclusion patterns
//        return ContentFilterUtils.filterChildren(originalChildren)
//    }

    fun example2_CustomExclusionPatterns(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Custom exclusion patterns
        val customPatterns = listOf(
            "listening comprehension - activity.mp4",
            "worksheet",
            "test",
            "quiz"
        )
        
        return ContentFilterUtils.filterChildren(
            children = originalChildren,
            exclusionPatterns = customPatterns,
            logTag = "CustomFilter"
        )
    }

    fun example3_FilterByFileType(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Only allow video files
        return ContentFilterUtils.filterByFileType(
            children = originalChildren,
            allowedTypes = listOf("mp4", "avi", "mov"),
            logTag = "VideoFilter"
        )
    }

    fun example4_ExcludeFileTypes(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Exclude specific file types
        return ContentFilterUtils.filterExcludeFileTypes(
            children = originalChildren,
            excludedTypes = listOf("pdf", "doc", "txt"),
            logTag = "ExcludeDocsFilter"
        )
    }

    fun example5_CustomFilterLogic(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Custom filtering logic
        return ContentFilterUtils.filterCustom(
            children = originalChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                // Include only if:
                // 1. It's a video file AND
                // 2. Doesn't contain "activity" AND
                // 3. File size is greater than 0
                name.endsWith(".mp4") && 
                !name.contains("activity") && 
                (child.size ?: 0) > 0
            },
            logTag = "CustomLogicFilter"
        )
    }

//    fun example6_ChainedFiltering(originalChildren: ArrayList<Children>): ArrayList<Children> {
//        // Chain multiple filters
//        return originalChildren
//            .let { ContentFilterUtils.filterChildren(it) } // Remove default exclusions
//            .let { ContentFilterUtils.filterByFileType(it, listOf("mp4")) } // Only videos
//            .let { ContentFilterUtils.filterCustom(it) { child ->
//                // Additional custom logic
//                (child.size ?: 0) > 1000 // Only files larger than 1KB
//            }}
//    }

    fun example7_FunctionalApproach(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Using the functional approach
        return ContentFilterUtils.filterChildrenFunctional(
            children = originalChildren,
            exclusionPatterns = listOf("worksheet", "activity.mp4"),
            logTag = "FunctionalFilter"
        )
    }

    fun example8_VideoSpecificFiltering(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Specific filtering for video fragments
        val videoExclusionPatterns = listOf(
            "listening comprehension - activity.mp4",
            "worksheet",
            "activity.mp4",
            "test.mp4",
            "quiz.mp4"
        )
        
        return ContentFilterUtils.filterChildren(
            children = originalChildren,
            exclusionPatterns = videoExclusionPatterns,
            logTag = "VideoFragmentFilter"
        )
    }

    fun example9_WorksheetSpecificFiltering(originalChildren: ArrayList<Children>): ArrayList<Children> {
        // Specific filtering for worksheet fragments (opposite of video filtering)
        return ContentFilterUtils.filterCustom(
            children = originalChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                // Include only worksheets and PDFs
                name.contains("worksheet") || name.endsWith(".pdf")
            },
            logTag = "WorksheetFilter"
        )
    }

    fun example10_ConditionalFiltering(
        originalChildren: ArrayList<Children>,
        userGrade: String,
        userPreferences: Map<String, Boolean>
    ): ArrayList<Children> {
        // Conditional filtering based on user grade and preferences
        val exclusionPatterns = mutableListOf<String>()
        
        // Add grade-specific exclusions
        when (userGrade) {
            "Grade 1" -> {
                exclusionPatterns.addAll(listOf("advanced", "complex"))
            }
            "Grade 4" -> {
                exclusionPatterns.addAll(listOf("basic", "simple"))
            }
        }
        
        // Add preference-based exclusions
        if (userPreferences["hideActivities"] == true) {
            exclusionPatterns.add("activity.mp4")
        }
        
        if (userPreferences["hideWorksheets"] == true) {
            exclusionPatterns.add("worksheet")
        }
        
        return ContentFilterUtils.filterChildren(
            children = originalChildren,
            exclusionPatterns = exclusionPatterns,
            logTag = "ConditionalFilter"
        )
    }
}
