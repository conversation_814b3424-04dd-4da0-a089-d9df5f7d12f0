package clt.india.classroom.examples

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import clt.india.classroom.ui.resources.VideoFragment5To10

/**
 * Examples showing how to use VideoFragment5To10 with specific chapter selection
 */
object VideoFragment5To10ChapterExample {

    /**
     * Example 1: Create fragment for a specific chapter by name
     */
    fun createFragmentForChapter(subject: String, chapterName: String): VideoFragment5To10 {
        Log.i("VideoFragment5To10Example", "Creating fragment for subject: $subject, chapter: $chapterName")
        return VideoFragment5To10.newInstance(subject, chapterName, null)
    }

    /**
     * Example 2: Create fragment for a specific chapter by index
     */
    fun createFragmentForChapterIndex(subject: String, chapterIndex: Int): VideoFragment5To10 {
        Log.i("VideoFragment5To10Example", "Creating fragment for subject: $subject, chapter index: $chapterIndex")
        return VideoFragment5To10.newInstance(subject, null, chapterIndex)
    }

    /**
     * Example 3: Create fragment with both chapter name and index
     */
    fun createFragmentWithBothParams(subject: String, chapterName: String, chapterIndex: Int): VideoFragment5To10 {
        Log.i("VideoFragment5To10Example", "Creating fragment for subject: $subject, chapter: $chapterName, index: $chapterIndex")
        return VideoFragment5To10.newInstance(subject, chapterName, chapterIndex)
    }

    /**
     * Example 4: Update chapter selection after fragment is created
     */
    fun updateChapterSelection(fragment: VideoFragment5To10, newChapterName: String) {
        Log.i("VideoFragment5To10Example", "Updating chapter selection to: $newChapterName")
        if (fragment.isAdded) {
            fragment.updateSelectedChapter(newChapterName)
        }
    }

    /**
     * Example 5: Update chapter selection by index
     */
    fun updateChapterSelectionByIndex(fragment: VideoFragment5To10, chapterIndex: Int) {
        Log.i("VideoFragment5To10Example", "Updating chapter selection to index: $chapterIndex")
        if (fragment.isAdded) {
            fragment.updateSelectedChapterByIndex(chapterIndex)
        }
    }

    /**
     * Example 6: Get current chapter information
     */
    fun getCurrentChapterInfo(fragment: VideoFragment5To10): Pair<String?, Int?> {
        return if (fragment.isAdded) {
            fragment.getCurrentChapterInfo()
        } else {
            Pair(null, null)
        }
    }
}

/**
 * Example Activity showing how to use VideoFragment5To10 with chapter selection
 */
class VideoFragment5To10ChapterActivity : AppCompatActivity() {

    private var videoFragment: VideoFragment5To10? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Example 1: Create fragment for specific chapter
        createFragmentForSpecificChapter()
        
        // Example 2: Handle chapter selection from intent
        handleChapterFromIntent()
    }

    private fun createFragmentForSpecificChapter() {
        Log.i("VideoFragment5To10ChapterActivity", "Creating fragment for specific chapter")
        
        val subject = "Mathematics"
        val chapterName = "Chapter 1: Numbers"
        
        videoFragment = VideoFragment5To10.newInstance(subject, chapterName, null)
        
        supportFragmentManager.beginTransaction()
            .replace(android.R.id.content, videoFragment!!)
            .commit()
    }

    private fun handleChapterFromIntent() {
        val subject = intent.getStringExtra("subject")
        val chapterName = intent.getStringExtra("chapter")
        val chapterIndex = intent.getIntExtra("chapterIndex", -1)
        
        Log.i("VideoFragment5To10ChapterActivity", "Intent data - subject: $subject, chapter: $chapterName, index: $chapterIndex")
        
        if (!subject.isNullOrEmpty()) {
            videoFragment = when {
                !chapterName.isNullOrEmpty() -> {
                    Log.i("VideoFragment5To10ChapterActivity", "Creating fragment with chapter name")
                    VideoFragment5To10.newInstance(subject, chapterName, null)
                }
                chapterIndex >= 0 -> {
                    Log.i("VideoFragment5To10ChapterActivity", "Creating fragment with chapter index")
                    VideoFragment5To10.newInstance(subject, null, chapterIndex)
                }
                else -> {
                    Log.i("VideoFragment5To10ChapterActivity", "Creating fragment with subject only")
                    VideoFragment5To10.newInstance(subject)
                }
            }
            
            supportFragmentManager.beginTransaction()
                .replace(android.R.id.content, videoFragment!!)
                .commit()
        }
    }

    /**
     * Method to change chapter selection
     */
    fun changeChapter(chapterName: String) {
        Log.i("VideoFragment5To10ChapterActivity", "Changing chapter to: $chapterName")
        videoFragment?.updateSelectedChapter(chapterName)
    }

    /**
     * Method to change chapter selection by index
     */
    fun changeChapterByIndex(chapterIndex: Int) {
        Log.i("VideoFragment5To10ChapterActivity", "Changing chapter to index: $chapterIndex")
        videoFragment?.updateSelectedChapterByIndex(chapterIndex)
    }

    /**
     * Method to get current chapter info
     */
    fun getCurrentChapter(): Pair<String?, Int?> {
        return videoFragment?.getCurrentChapterInfo() ?: Pair(null, null)
    }
}

/**
 * Example showing integration with chapter selection from IndexPageFragment
 */
object ChapterSelectionIntegration {

    /**
     * Handle chapter selection from IndexPageFragment
     */
    fun onChapterSelected(
        subject: String,
        chapterName: String,
        chapterIndex: Int,
        videoFragment: VideoFragment5To10?
    ) {
        Log.i("ChapterSelectionIntegration", "Chapter selected - subject: $subject, chapter: $chapterName, index: $chapterIndex")
        
        videoFragment?.let { fragment ->
            if (fragment.isAdded) {
                // Update existing fragment
                fragment.updateSelectedChapter(chapterName)
            }
        } ?: run {
            // Create new fragment with chapter selection
            Log.i("ChapterSelectionIntegration", "Creating new fragment with chapter selection")
            // This would be handled by the parent activity/fragment
        }
    }

    /**
     * Example of how to pass chapter information from HomeActivity
     */
    fun updateHomeActivityForChapterSelection() {
        Log.i("ChapterSelectionIntegration", """
            To integrate chapter selection with HomeActivity:
            
            1. Update HomeActivity to track selected chapter:
               private var selectedChapter: String? = null
               private var selectedChapterIndex: Int? = null
            
            2. Update ChapterContentPagerAdapter creation:
               val pagerAdapter = ChapterContentPagerAdapter(
                   fragmentActivity = this,
                   listener = this,
                   selectedGrade = selectedGrade,
                   selectedSubject = selectedSubject,
                   chapterName = selectedChapter  // Pass selected chapter
               )
            
            3. Handle chapter selection from IndexPageFragment:
               fun onChapterSelected(chapterName: String, chapterIndex: Int) {
                   selectedChapter = chapterName
                   selectedChapterIndex = chapterIndex
                   // Refresh the ViewPager to update VideoFragment5To10
                   setupViewPagerWithTabs()
               }
        """.trimIndent())
    }
}

/**
 * Common use cases and solutions
 */
object VideoFragment5To10UseCases {

    /**
     * Use Case 1: Show videos only for "Chapter 1: Introduction"
     */
    fun showChapter1Videos(subject: String): VideoFragment5To10 {
        return VideoFragment5To10.newInstance(subject, "Chapter 1: Introduction", 0)
    }

    /**
     * Use Case 2: Show videos for the 3rd chapter (index 2)
     */
    fun showThirdChapterVideos(subject: String): VideoFragment5To10 {
        return VideoFragment5To10.newInstance(subject, null, 2)
    }

    /**
     * Use Case 3: Dynamic chapter selection based on user progress
     */
    fun showChapterBasedOnProgress(subject: String, userProgress: Int): VideoFragment5To10 {
        val chapterIndex = userProgress.coerceAtLeast(0) // Ensure non-negative
        return VideoFragment5To10.newInstance(subject, null, chapterIndex)
    }

    /**
     * Use Case 4: Show chapter based on search/filter
     */
    fun showChapterFromSearch(subject: String, searchQuery: String): VideoFragment5To10 {
        // In real implementation, you'd search for matching chapter
        val matchingChapter = "Chapter containing: $searchQuery"
        return VideoFragment5To10.newInstance(subject, matchingChapter, null)
    }
}
