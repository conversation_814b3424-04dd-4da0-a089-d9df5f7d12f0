package clt.india.classroom.data.api.response.egl

import clt.india.classroom.data.api.request.UsbDriveContents
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/*{"usbDriveContents":
  [{"name":"CLT India - Early Grade Reading","type":"DIR","size":32768,"children":
    [{"name":"SERIES 1","type":"DIR","size":32768,"children":
      [{"name":"1. Listening Comprehension","type":"DIR","size":32768,"children":
        [{"name":"1. Introduction Listening Comprehension.mp4","type":"FILE","size":4647223}]
      }]
    }]

  }]
}*/

@Serializable
data class EGLData (

  @SerialName("usbDriveContents" ) var usbDriveContents : ArrayList<UsbDriveContents> = arrayListOf()

)