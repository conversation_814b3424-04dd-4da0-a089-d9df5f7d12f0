package clt.india.classroom.data.api.request

import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName


@Serializable
data class DeviceLoginResponse (

  @SerialName("status"       ) var status       : Int?                 = null,
  @SerialName("message"      ) var message      : String?              = null,
  @SerialName("subjects"     ) var subjects     : String?              = null,
  @SerialName("expiry_date"  ) var expiryDate   : String?              = null,
  @SerialName("validthrough" ) var validthrough : String?              = null,
  @SerialName("logInType"    ) var logInType    : String?              = null,
  @SerialName("languages"    ) var languages    : ArrayList<Languages> = arrayListOf(),
  @SerialName("partner"      ) var partner      : String?              = null

)