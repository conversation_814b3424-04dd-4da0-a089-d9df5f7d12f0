package clt.india.classroom.data.api.request

import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName


@Serializable
data class Children (

  @SerialName("name"     ) var name     : String?             = null,
  @SerialName("type"     ) var type     : String?             = null,
  @SerialName("size"     ) var size     : Int?                = null,
@SerialName("absolute_path") var absolute_path  : String?  = null,
@SerialName("children" ) var children : ArrayList<Children> = arrayListOf()

)