package clt.india.classroom.data.api.methods

import clt.india.classroom.data.api.ApiClient
import clt.india.classroom.data.api.request.DeviceLoginRequest
import clt.india.classroom.data.api.request.DeviceLoginResponse
import clt.india.classroom.data.api.request.LoginRequest
import clt.india.classroom.data.api.response.LoginResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

interface UserApi {

    @POST("/api/authaccount/login")
    suspend fun loginUser(@Body loginRequest: LoginRequest): Response<LoginResponse>

    @POST("/api/v3/login/")
    suspend fun deviceLogin(@Body deviceLoginRequest: DeviceLoginRequest): Response<DeviceLoginResponse>

    companion object {
        fun getApi(): UserApi? {
            return ApiClient.client?.create(UserApi::class.java)
        }
    }
}