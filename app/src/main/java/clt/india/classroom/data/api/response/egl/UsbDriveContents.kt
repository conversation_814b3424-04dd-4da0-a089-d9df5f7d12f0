package clt.india.classroom.data.api.request

import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName


@Serializable
data class UsbDriveContents (

  @SerialName("name"     ) var name     : String?             = null,
  @SerialName("type"     ) var type     : String?             = null,
  @SerialName("size"     ) var size     : Int?                = null,
  @SerialName("children" ) var children : ArrayList<Children> = arrayListOf()

)