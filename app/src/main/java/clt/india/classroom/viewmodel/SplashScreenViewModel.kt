package clt.india.classroom.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.*
import clt.india.classroom.data.api.ApiClient
import clt.india.classroom.data.api.methods.UserApi
import clt.india.classroom.data.api.request.DeviceLoginRequest
import clt.india.classroom.data.api.request.DeviceLoginResponse
import clt.india.classroom.data.api.request.LoginRequest
import clt.india.classroom.data.api.response.BaseResponse
import clt.india.classroom.data.api.response.LoginResponse
import clt.india.classroom.repository.UserRepository
import clt.india.classroom.utils.NetworkUtils
import kotlinx.coroutines.launch

class SplashScreenViewModel(application: Application) : AndroidViewModel(application) {
    val TAG: String = "SplashScreen "
    val userRepo = UserRepository()
    val deviceLoginResult: MutableLiveData<BaseResponse<DeviceLoginResponse>> = MutableLiveData()

    fun deviceIdVerification(deviceId: String, appVersion: String) {

        deviceLoginResult.value = BaseResponse.Loading()
        viewModelScope.launch {
            try {

                if (NetworkUtils.isConnected(getApplication())) {
                    val deviceLoginRequest = DeviceLoginRequest(
                        deviceId = deviceId,
                        av = appVersion
                    )
                    val response = userRepo.deviceLogin(deviceLoginRequest = deviceLoginRequest)
                    if (response?.code() == 200) {
                        deviceLoginResult.value = BaseResponse.Success(response.body())
                    } else {
                        deviceLoginResult.value = BaseResponse.Error(response?.message())
                    }
                }else{
                    Log.d(TAG, "555555" + NetworkUtils.isConnected(getApplication()))
                }

            } catch (ex: Exception) {
                deviceLoginResult.value = BaseResponse.Error(ex.message)
            }
        }
    }
}