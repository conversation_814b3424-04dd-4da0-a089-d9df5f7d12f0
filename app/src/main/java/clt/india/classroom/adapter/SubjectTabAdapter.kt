package clt.india.classroom.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemSubjectTabBinding

class SubjectTabAdapter(
    private val subjects: List<String>?,
    private var selectedPosition: Int = 0,
    private val onTabClick: (String) -> Unit
) : RecyclerView.Adapter<SubjectTabAdapter.TabViewHolder>() {

    inner class TabViewHolder(val binding: ItemSubjectTabBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemSubjectTabBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>old<PERSON>, position: Int) {
        val subject = subjects?.get(position)
        val textView = holder.binding.subjectTabText

        textView.text = subject

        textView.isSelected = position == selectedPosition

        textView.setOnClickListener {
            val previous = selectedPosition
            selectedPosition = position
            notifyItemChanged(previous)
            notifyItemChanged(position)
            onTabClick(subject.toString())
        }
    }

    override fun getItemCount(): Int = subjects?.size ?: 0
}
