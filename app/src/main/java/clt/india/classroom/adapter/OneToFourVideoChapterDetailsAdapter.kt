package clt.india.classroom.adapter

import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.data.api.request.Children
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.databinding.OnetofourItemChapterDetailsBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity
import clt.india.classroom.ui.resources.VideoPlayingActivityOld


class OneToFourVideoChapterDetailsAdapter(
    private val chaptersTitle: ArrayList<Children>,
    private val onChapterClick: (ChapterNew) -> Unit,
    private var selectedPosition: Int = 0
) : RecyclerView.Adapter<OneToFourVideoChapterDetailsAdapter.TabViewHolder>() {

    inner class TabViewHolder(val binding: OnetofourItemChapterDetailsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = OnetofourItemChapterDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun getItemCount() = chaptersTitle.size


    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        //val imageRes = chaptersImage[position]
        val title = chaptersTitle[position]

        val imageView = holder.binding.cardImage
        val textView = holder.binding.cardTitle

        textView.text = title.name
        textView.isSelected = position == selectedPosition

        holder.binding.root.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, VideoPlayingActivityOld::class.java)
            intent.putExtra("chapter_vid_absolute_path", title.absolute_path)
            intent.putExtra("title", title.name)
//            intent.putExtra("chapter_title", "/mnt/media_rw\/B459-18A6\/CLT India - Early Grade Reading\/SERIES 1\/1. Listening Comprehension\/1. Introduction Listening Comprehension.mp4")
            context.startActivity(intent)
        }
//        holder.binding.root.setOnClickListener {
//            onChapterClick(title.name)
//        }
    }
  //override fun getItemCount(): Int = chaptersImage.size
}