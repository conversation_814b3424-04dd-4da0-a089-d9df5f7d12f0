package clt.india.classroom.adapter

import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.databinding.ItemChapterDetailsBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity

class ChapterDetailsAdapter(
    private val chaptersTitle: List<Child>,
    private var selectedPosition: Int = 0
) : RecyclerView.Adapter<ChapterDetailsAdapter.TabViewHolder>() {

    inner class TabViewHolder(val binding: ItemChapterDetailsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemChapterDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
//        val imageRes = chaptersImage[position]
        val title = chaptersTitle[position]

        val imageView = holder.binding.cardImage
        val textView = holder.binding.cardTitle

//        imageView.setImageResource(imageRes)
        textView.text = title.name
        textView.isSelected = position == selectedPosition

        // Set the click listener on the root view to navigate
        holder.binding.root.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, VideoPlayingActivity::class.java)
            intent.putExtra("chapter_title", title.name)
            intent.putExtra("chapter_vid_absolute_path", title.absolute_path)
            context.startActivity(intent)
        }
    }

    override fun getItemCount(): Int = chaptersTitle.size
}
