package clt.india.classroom.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemVideoListBinding

class VideoListAdapter(
    private val videos: List<String>,
    private val onVideoClick: (String) -> Unit
) : RecyclerView.Adapter<VideoListAdapter.VideoViewHolder>() {

    inner class VideoViewHolder(val binding: ItemVideoListBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val binding = ItemVideoListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return VideoViewHolder(binding)
    }

    override fun getItemCount(): Int = videos.size

    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        val title = videos[position]
        holder.binding.cardTitle.text = title
        holder.binding.root.setOnClickListener {
            onVideoClick(title)
        }
    }
}
