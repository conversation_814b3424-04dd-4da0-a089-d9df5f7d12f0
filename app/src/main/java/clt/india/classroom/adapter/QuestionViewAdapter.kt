package clt.india.classroom.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemChapterDetailsBinding
import clt.india.classroom.intrface.OnChapterClickListener

class QuestionViewAdapter(
    private val chaptersImage: List<Int>,
    private val chaptersTitle: List<String>,
    private var selectedPosition: Int = 0,
    private val clickListener: OnChapterClickListener
) : RecyclerView.Adapter<QuestionViewAdapter.TabViewHolder>() {

    inner class TabViewHolder(val binding: ItemChapterDetailsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemChapterDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        val imageRes = chaptersImage[position]
        val title = chaptersTitle[position]

        holder.binding.cardImage.setImageResource(imageRes)
        holder.binding.cardTitle.text = title
        holder.binding.cardTitle.isSelected = position == selectedPosition

        holder.binding.root.setOnClickListener {
            clickListener.onChapterClicked(position)
        }

    }

    override fun getItemCount(): Int = chaptersImage.size
}
