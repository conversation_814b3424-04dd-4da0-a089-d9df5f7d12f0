package clt.india.classroom.adapter

import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.R
import clt.india.classroom.databinding.ItemQuestionResultsBinding
import clt.india.classroom.data.api.response.Question

class QuestionResultsAdapter(
    private val questions: List<Question>
) : RecyclerView.Adapter<QuestionResultsAdapter.QuestionResultsViewHolder>() {

    inner class QuestionResultsViewHolder(val binding: ItemQuestionResultsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): QuestionResultsViewHolder {
        val binding = ItemQuestionResultsBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return QuestionResultsViewHolder(binding)
    }



    override fun onBindViewHolder(holder: QuestionResultsViewHolder, position: Int) {
        val question = questions[position]
        val context = holder.binding.root.context

        holder.binding.questionText.text = "${position + 1}. ${question.questionText}"
        if (question.options.size >= 4) {
            holder.binding.option1.text = question.options[0]
            holder.binding.option2.text = question.options[1]
            holder.binding.option3.text = question.options[2]
            holder.binding.option4.text = question.options[3]
        }

        holder.binding.optionsRadioGroup.clearCheck()
        resetOptionColors(holder)

        val selected = question.selectedOption
        val correct = question.correctAnswer

        val options = listOf(
            holder.binding.option1,
            holder.binding.option2,
            holder.binding.option3,
            holder.binding.option4
        )

        selected?.let { selectedIndex ->
            val selectedRadio = options[selectedIndex]
            selectedRadio.isChecked = true

            val color = if (selected == correct) {
                R.color.green
            } else {
                R.color.red
            }

            selectedRadio.setTextColor(ContextCompat.getColor(context, color))
            selectedRadio.buttonTintList = if (selected == correct) {
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.green))
            } else {
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.red))
            }
        }

        holder.binding.correctAnswerText.apply {
            visibility = View.VISIBLE
            correct?.let { correctIndex ->
                text = "Correct Answer: ${question.options[correctIndex]}"

                if (selected == null) {
                    options[correctIndex].setTextColor(ContextCompat.getColor(context, R.color.green))
                }
            }
        }
    }



    private fun resetOptionColors(holder: QuestionResultsViewHolder) {
        val defaultColor = ContextCompat.getColor(holder.binding.root.context, R.color.black)
        holder.binding.option1.setTextColor(defaultColor)
        holder.binding.option2.setTextColor(defaultColor)
        holder.binding.option3.setTextColor(defaultColor)
        holder.binding.option4.setTextColor(defaultColor)
    }

    override fun getItemCount(): Int = questions.size
}