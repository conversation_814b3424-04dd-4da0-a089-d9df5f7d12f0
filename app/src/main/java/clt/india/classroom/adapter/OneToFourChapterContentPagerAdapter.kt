package clt.india.classroom.adapter

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import clt.india.classroom.ui.egr.OneToFourVideoFragment
import clt.india.classroom.ui.egr.OneToFourWorkSheetsFragment


class OneToFourChapterContentPagerAdapter(
    fragmentActivity: FragmentActivity,
    private val subject: String,
    private val positionOfChap: Int?
) : FragmentStateAdapter(fragmentActivity) {

    companion object {
        val TAB_TITLES = arrayOf("Videos", "Worksheets")
    }

    override fun getItemCount(): Int = TAB_TITLES.size

    override fun createFragment(position: Int): Fragment {
        Log.i("OneToFourChapterContentPagerAdapter", "createFragment: "+positionOfChap)
        return when (position) {
            0 -> OneToFourVideoFragment.newInstance(subject, positionOfChap)
            1 -> OneToFourWorkSheetsFragment.newInstance(subject, positionOfChap)
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}
