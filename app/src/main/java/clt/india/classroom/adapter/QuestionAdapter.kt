package clt.india.classroom.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemQuestionsBinding
import clt.india.classroom.data.api.response.Question

class QuestionAdapter(
    private val questions: List<Question>
) : RecyclerView.Adapter<QuestionAdapter.QuestionViewHolder>() {

    inner class QuestionViewHolder(val binding: ItemQuestionsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): QuestionViewHolder {
        val binding = ItemQuestionsBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return QuestionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: QuestionViewHolder, position: Int) {
        val question = questions[position]

        // Set question text
        holder.binding.questionText.text = "${position + 1}. ${question.questionText}"

        // Set options text
        if (question.options.size >= 4) {
            holder.binding.option1.text = question.options[0]
            holder.binding.option2.text = question.options[1]
            holder.binding.option3.text = question.options[2]
            holder.binding.option4.text = question.options[3]
        }

        // Clear any previous selection
        holder.binding.optionsRadioGroup.setOnCheckedChangeListener(null)
        holder.binding.optionsRadioGroup.clearCheck()

        // Set previously selected option if any
        question.selectedOption?.let { selected ->
            when (selected) {
                0 -> holder.binding.option1.isChecked = true
                1 -> holder.binding.option2.isChecked = true
                2 -> holder.binding.option3.isChecked = true
                3 -> holder.binding.option4.isChecked = true
            }
        }

        // Handle selection change
        holder.binding.optionsRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            question.selectedOption = when (checkedId) {
                holder.binding.option1.id -> 0
                holder.binding.option2.id -> 1
                holder.binding.option3.id -> 2
                holder.binding.option4.id -> 3
                else -> null
            }
        }
    }

    override fun getItemCount(): Int = questions.size
}
