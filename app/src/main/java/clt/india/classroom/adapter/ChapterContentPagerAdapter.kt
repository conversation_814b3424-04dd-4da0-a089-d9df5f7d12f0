package clt.india.classroom.adapter

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import clt.india.classroom.intrface.OnAssessmentClickListener
import clt.india.classroom.ui.assesment.AssesmentsFragment
import clt.india.classroom.ui.resources.TextBooksFragment
import clt.india.classroom.ui.resources.VideoFragment
import clt.india.classroom.ui.resources.VideoFragment5To10

class ChapterContentPagerAdapter(
    fragmentActivity: FragmentActivity,
    private val listener: OnAssessmentClickListener,
    private val selectedGrade: String?,
    private val selectedSubject: String? = null,   // add this
    private val chapterName: String? = null   // add this
) : FragmentStateAdapter(fragmentActivity) {

    companion object {
        val TAB_TITLES = arrayOf("Video", "Textbook", "Assesment")
    }

    override fun getItemCount(): Int = TAB_TITLES.size

    override fun createFragment(position: Int): Fragment {
        Log.i("ChapterContentPagerAdapter", "createFragment:1111 "+selectedSubject)

        return when (position) {
            0 -> VideoFragment5To10.newInstance(selectedSubject)
            1 -> TextBooksFragment.newInstance(selectedSubject)
            2 -> AssesmentsFragment.newInstance(selectedSubject)
            else -> throw IllegalArgumentException("Invalid position")
        }
    }
}


