package clt.india.classroom.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemWorksheetListBinding

class WorksheetListAdapter(
    private val worksheets: List<String>,
    private val onWorksheetClick: (String) -> Unit
) : RecyclerView.Adapter<WorksheetListAdapter.WorksheetViewHolder>() {

    inner class WorksheetViewHolder(val binding: ItemWorksheetListBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WorksheetViewHolder {
        val binding = ItemWorksheetListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return WorksheetViewHolder(binding)
    }

    override fun getItemCount(): Int = worksheets.size

    override fun onBindViewHolder(holder: WorksheetViewHolder, position: Int) {
        val title = worksheets[position]
        holder.binding.cardTitle.text = title
        holder.binding.root.setOnClickListener {
            onWorksheetClick(title)
        }
    }
}
