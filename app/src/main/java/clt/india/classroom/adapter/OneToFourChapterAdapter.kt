package clt.india.classroom.adapter

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemChaptersOnetofourBinding
import clt.india.classroom.data.api.response.Chapter
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.ui.egr.OneToFourHomeActivity

class OneToFourChapterAdapter(private var chapters: List<ChapterNew>) :
    RecyclerView.Adapter<OneToFourChapterAdapter.ChapterViewHolder>() {

    inner class ChapterViewHolder(val binding: ItemChaptersOnetofourBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
        val binding = ItemChaptersOnetofourBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
        Log.i("TAG", "onBindViewHolder: 22" +chapters)
        val chapter = chapters[position]
        holder.binding.chapterNumber.text = chapter.chapterNumber
        holder.binding.chapterTitle.text = chapter.chapterTitle
        holder.binding.viewButton.setOnClickListener{
            val context = holder.itemView.context
            val intent = Intent(context, OneToFourHomeActivity::class.java)
            intent.putExtra("chapterNumber", chapter.chapterNumber)
            intent.putExtra("chap_details",chapters.toString())
            intent.putExtra("chapterTitle", chapter.chapterTitle)
            intent.putExtra("pos", position)
            context.startActivity(intent)
        }
    }

    override fun getItemCount(): Int = chapters.size

    fun updateChapters(newChapters: List<ChapterNew>) {
        chapters = newChapters
        notifyDataSetChanged()
    }
}
