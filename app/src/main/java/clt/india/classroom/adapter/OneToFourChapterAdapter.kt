package clt.india.classroom.adapter

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemChaptersOnetofourBinding
import clt.india.classroom.data.api.response.Chapter
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.ui.egr.OneToFourHomeActivity

class OneToFourChapterAdapter(
    private var chapters: List<ChapterNew>,
    private var originalChapters: List<ChapterNew> = chapters
) : RecyclerView.Adapter<OneToFourChapterAdapter.ChapterViewHolder>() {

    inner class ChapterViewHolder(val binding: ItemChaptersOnetofourBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
        val binding = ItemChaptersOnetofourBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
        Log.i("OneToFourChapterAdapter", "onBindViewHolder: position=$position, chapters size=${chapters.size}")
        val chapter = chapters[position]

        // Find the original position of this chapter in the unfiltered list
        val originalPosition = originalChapters.indexOfFirst {
            it.chapterNumber == chapter.chapterNumber && it.chapterTitle == chapter.chapterTitle
        }

        Log.i("OneToFourChapterAdapter", "Chapter: ${chapter.chapterTitle}, filtered position: $position, original position: $originalPosition")

        holder.binding.chapterTitle.text = chapter.chapterTitle
        holder.binding.viewButton.setOnClickListener{
            val context = holder.itemView.context
            val intent = Intent(context, OneToFourHomeActivity::class.java)
            intent.putExtra("chapterNumber", chapter.chapterNumber)
            intent.putExtra("chap_details", originalChapters.toString()) // Use original chapters list
            intent.putExtra("chapterTitle", chapter.chapterTitle)
            intent.putExtra("pos", originalPosition) // Use original position, not filtered position
            intent.putExtra("original_pos", originalPosition) // Add explicit original position
            intent.putExtra("filtered_pos", position) // Add filtered position for debugging

            Log.i("OneToFourChapterAdapter", "Starting activity with original position: $originalPosition")
            context.startActivity(intent)
        }
    }

    override fun getItemCount(): Int = chapters.size

    fun updateChapters(newChapters: List<ChapterNew>) {
        chapters = newChapters
        notifyDataSetChanged()
    }

    /**
     * Update chapters with filtered results (keeps original chapters intact)
     */
    fun updateFilteredChapters(filteredChapters: List<ChapterNew>) {
        Log.i("OneToFourChapterAdapter", "updateFilteredChapters: ${filteredChapters.size} filtered chapters")
        chapters = filteredChapters
        notifyDataSetChanged()
    }

    /**
     * Update both original and current chapters (for new data)
     */
    fun updateAllChapters(newChapters: List<ChapterNew>) {
        Log.i("OneToFourChapterAdapter", "updateAllChapters: ${newChapters.size} new chapters")
        originalChapters = newChapters
        chapters = newChapters
        notifyDataSetChanged()
    }

    /**
     * Reset to show all original chapters (clear search)
     */
    fun resetToOriginalChapters() {
        Log.i("OneToFourChapterAdapter", "resetToOriginalChapters: ${originalChapters.size} chapters")
        chapters = originalChapters
        notifyDataSetChanged()
    }
}
