package clt.india.classroom.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemSubjectTabBinding

class SubjectTabAdapterNew(
    private val subjects: List<String>?,
    private var selectedPosition: Int = 0,
    private val onTabClick: (String) -> Unit
) : RecyclerView.Adapter<SubjectTabAdapterNew.TabViewHolder>() {

    inner class TabViewHolder(val binding: ItemSubjectTabBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemSubjectTabBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: <PERSON>bViewHolder, position: Int) {
        val subject = subjects?.get(position)
        val textView = holder.binding.subjectTabText

        textView.text = subject

        textView.isSelected = position == selectedPosition

        textView.setOnClickListener {
            if (selectedPosition != position) {
                val previous = selectedPosition
                selectedPosition = position
                notifyItemChanged(previous)
                notifyItemChanged(position)
                if (subject != null) {
                    onTabClick(subject)
                }
            }
        }
    }

    override fun getItemCount(): Int = subjects?.size ?: 0

    /**
     * Method to update selected position programmatically
     */
    fun updateSelectedPosition(newPosition: Int) {
        if (newPosition != selectedPosition && newPosition in 0 until itemCount) {
            val previous = selectedPosition
            selectedPosition = newPosition
            notifyItemChanged(previous)
            notifyItemChanged(newPosition)
        }
    }

    /**
     * Get currently selected position
     */
    fun getSelectedPosition(): Int = selectedPosition
}
