package clt.india.classroom.adapter

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.databinding.ItemChapterDetailsBinding
import java.io.File
import clt.india.classroom.ui.resources.TextBookViewActivity

class TextBookAdapter(
    private val chaptersTitle: List<Child>,
    private var selectedPosition: Int = 0
) : RecyclerView.Adapter<TextBookAdapter.TabViewHolder>() {

    inner class TabViewHolder(val binding: ItemChapterDetailsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemChapterDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
//        val imageRes = chaptersImage[position]
        val title = chaptersTitle[position]

        val imageView = holder.binding.cardImage
        val textView = holder.binding.cardTitle

//        imageView.setImageResource(imageRes)
        textView.text = title.name
        textView.isSelected = position == selectedPosition

        // Set the click listener on the root view to navigate
        holder.binding.root.setOnClickListener {
            val context = holder.itemView.context
//            val intent = Intent(context, TextBookViewActivity::class.java)
//            intent.putExtra("chapter_title", title.name)
//            intent.putExtra("chapter_pdf_absolute_path", title.absolute_path)
//            Log.i("TextBookAdapter", "onBindViewHolder:.. "+title.absolute_path)
//            val pdfFile = File(title.absolute_path)
//            Log.i("TextBookAdapter", "onBindViewHolder:.... "+pdfFile.toUri())
//            val intent = Intent(Intent.ACTION_VIEW)
//            intent.setDataAndType(pdfFile.toUri(), "application/pdf")
//            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION
//
//            try {
//                context.startActivity(intent)
//            } catch (e: ActivityNotFoundException) {
//                Toast.makeText(context, "No PDF viewer found", Toast.LENGTH_SHORT).show()
//            }
            openPdfWithAbsolutePath(context,title.absolute_path)
        }
    }

    fun openPdfWithAbsolutePath(context: Context, absoluteFilePath: String) {
        val file = File(absoluteFilePath)

        if (!file.exists()) {
            Toast.makeText(context, "Error: File not found at $absoluteFilePath", Toast.LENGTH_LONG).show()
            return
        }

        val uri: Uri
        try {
            // Authority must match what's in your AndroidManifest.xml
            val authority = "clt.india.classroom.provider"
            uri = FileProvider.getUriForFile(context, authority, file)
        } catch (e: IllegalArgumentException) {
            // This can happen if the file path is not covered by your file_paths.xml
            Toast.makeText(context, "Error: Cannot get URI for file. Check FileProvider paths.", Toast.LENGTH_LONG).show()
            e.printStackTrace()
            return
        }


        val intent = Intent(Intent.ACTION_VIEW)
        intent.setDataAndType(uri, "application/pdf")
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION) // Important: Grant read permission

        // Optional: Add flags to ensure the PDF viewer opens in a new task,
        // and to clear the top of the activity stack if it's already running.
        // intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        // intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)


        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(context, "No PDF viewer application found.", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(context, "Error opening PDF: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
            e.printStackTrace()
        }
    }

// --- Example Usage (e.g., in your Activity or Fragment) ---
// val pdfAbsolutePath = "/storage/emulated/0/Download/mydocument.pdf"
// openPdfWithAbsolutePath(this, pdfAbsolutePath)

// Or, if it's from your TextBookAdapter:
// val pdfAbsolutePath = title.absolute_path // Assuming title.absolute_path holds the correct string
// openPdfWithAbsolutePath(holder.itemView.context, pdfAbsolutePath)


    override fun getItemCount(): Int = chaptersTitle.size
}
