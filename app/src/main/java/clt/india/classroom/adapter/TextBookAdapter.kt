package clt.india.classroom.adapter

import android.content.ActivityNotFoundException
import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.net.toUri
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.databinding.ItemChapterDetailsBinding
import clt.india.classroom.ui.resources.TextBookViewActivity

class TextBookAdapter(
    private val chaptersTitle: List<Child>,
    private var selectedPosition: Int = 0
) : RecyclerView.Adapter<TextBookAdapter.TabViewHolder>() {

    inner class TabViewHolder(val binding: ItemChapterDetailsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemChapterDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
//        val imageRes = chaptersImage[position]
        val title = chaptersTitle[position]

        val imageView = holder.binding.cardImage
        val textView = holder.binding.cardTitle

//        imageView.setImageResource(imageRes)
        textView.text = title.name
        textView.isSelected = position == selectedPosition

        // Set the click listener on the root view to navigate
        holder.binding.root.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, TextBookViewActivity::class.java)
            intent.putExtra("chapter_title", title.name)
            intent.putExtra("chapter_pdf_absolute_path", title.absolute_path)
            Log.i("TextBookAdapter", "onBindViewHolder:.. "+title.absolute_path)
            Log.i("TextBookAdapter", "onBindViewHolder:.... "+title.absolute_path.toUri())
//            val intent = Intent(Intent.ACTION_VIEW)
//            intent.setDataAndType(title.absolute_path.toUri(), "application/pdf")
//            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION

            try {
                context.startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                Toast.makeText(context, "No PDF viewer found", Toast.LENGTH_SHORT).show()
            }
        }
    }




    override fun getItemCount(): Int = chaptersTitle.size
}
