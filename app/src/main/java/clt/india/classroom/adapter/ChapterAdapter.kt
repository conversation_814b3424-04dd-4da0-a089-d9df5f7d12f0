package clt.india.classroom.adapter

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemChapterBinding
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.ui.gradeselection.HomeActivity

class ChapterAdapter(private var chapters: List<Child>, private val selectedGrade: String?, private var subjectNames: List<String>,private val selectedSubjectPosition: Int?) :
    RecyclerView.Adapter<ChapterAdapter.ChapterViewHolder>() {
    private var selectedPosition = 0

    inner class ChapterViewHolder(val binding: ItemChapterBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
        val binding = ItemChapterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
        val chapter = chapters[position]
//        holder.binding.chapterNumber.text = position.toString()
        holder.binding.chapterTitle.text = chapter.name
        holder.binding.viewButton.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, HomeActivity::class.java)
            intent.putExtra("chapterNumber", position)
            intent.putExtra("selectedGrade", selectedGrade)
            intent.putExtra("chapterTitle", chapter.name)
            intent.putExtra("chapterChild", chapter.children.toString())
            intent.putExtra("selectedSubjectPosition", selectedPosition)
            Log.i("TAG", "onBindViewHolder: chapterChild-1 "+chapter.name)
//            Log.i("TAG", "onBindViewHolder: chapterChild0 "+chapter.children.size)
//            Log.i("TAG", "onBindViewHolder: chapterChild1 "+chapter.children.get(0).name)
//            Log.i("TAG", "onBindViewHolder: chapterChild1 "+chapter.children.get(1).name)
//            Log.i("TAG", "onBindViewHolder: chapterChild1 "+chapter.children.get(2).name)
//            Log.i("TAG", "onBindViewHolder: chapterChild1 "+chapter.children.get(3).name)

            Log.i("TAG", "onBindViewHolder: chapterChild2 "+chapter.name+"...... "+selectedSubjectPosition)
            Log.i("TAG", "onBindViewHolder: chapterChild22 "+chapter.name+"...... "+selectedSubjectPosition)


            intent.putStringArrayListExtra("subjectNames", ArrayList(subjectNames))
//            intent.putExtra("subjectName", subjectNames.get(selectedSubjectPosition))
            context.startActivity(intent)
        }
    }
    override fun getItemCount(): Int = chapters.size

    fun updateChapters(newChapters: List<Child>) {
        chapters = newChapters
        notifyDataSetChanged()
    }

    fun updateSelectedPosition(newPosition: Int) {
        Log.i("TAG", "updateSelectedPositionNew: "+newPosition)
        selectedPosition = newPosition
        notifyDataSetChanged()
    }

}
