package clt.india.classroom.adapter

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemChapterBinding
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.ui.gradeselection.HomeActivity
import clt.india.classroom.utils.TvFocusHelper

class ChapterAdapter(
    private var chapters: List<Child>,
    private val selectedGrade: String?,
    private var subjectNames: List<String>,
    private val selectedSubjectPosition: Int?,
    private var originalChapters: List<Child> = chapters
) : RecyclerView.Adapter<ChapterAdapter.ChapterViewHolder>() {
    private var selectedPosition = 0

    inner class ChapterViewHolder(val binding: ItemChapterBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: <PERSON>G<PERSON>, viewType: Int): ChapterViewHolder {
        val binding = ItemChapterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
        val chapter = chapters[position]

        // Find the original position of this chapter in the unfiltered list
        val originalPosition = originalChapters.indexOfFirst {
            it.name == chapter.name && it.type == chapter.type
        }

        Log.i("ChapterAdapter", "Chapter: ${chapter.name}, filtered position: $position, original position: $originalPosition")

        holder.binding.chapterTitle.text = chapter.name
        holder.binding.viewButton.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, HomeActivity::class.java)

            // Use original position, not filtered position
            intent.putExtra("chapterNumber", originalPosition)
            intent.putExtra("selectedGrade", selectedGrade)
            intent.putExtra("chapterTitle", chapter.name)
            intent.putExtra("chapterChild", chapter.children.toString())
            intent.putExtra("selectedSubjectPosition", selectedPosition)
            intent.putExtra("original_chapter_position", originalPosition) // Add explicit original position
            intent.putExtra("filtered_chapter_position", position) // Add filtered position for debugging

            Log.i("ChapterAdapter", "Starting HomeActivity with original position: $originalPosition")
            Log.i("ChapterAdapter", "Chapter: ${chapter.name}, selectedSubjectPosition: $selectedSubjectPosition")

            intent.putStringArrayListExtra("subjectNames", ArrayList(subjectNames))
            context.startActivity(intent)
        }
    }
    override fun getItemCount(): Int = chapters.size

    fun updateChapters(newChapters: List<Child>) {
        chapters = newChapters
        notifyDataSetChanged()
    }

    /**
     * Update chapters with filtered results (keeps original chapters intact)
     */
    fun updateFilteredChapters(filteredChapters: List<Child>) {
        Log.i("ChapterAdapter", "updateFilteredChapters: ${filteredChapters.size} filtered chapters")
        chapters = filteredChapters
        notifyDataSetChanged()
    }

    /**
     * Update both original and current chapters (for new data)
     */
    fun updateAllChapters(newChapters: List<Child>) {
        Log.i("ChapterAdapter", "updateAllChapters: ${newChapters.size} new chapters")
        originalChapters = newChapters
        chapters = newChapters
        notifyDataSetChanged()
    }

    /**
     * Reset to show all original chapters (clear search)
     */
    fun resetToOriginalChapters() {
        Log.i("ChapterAdapter", "resetToOriginalChapters: ${originalChapters.size} chapters")
        chapters = originalChapters
        notifyDataSetChanged()
    }

    fun updateSelectedPosition(newPosition: Int) {
        Log.i("ChapterAdapter", "updateSelectedPositionNew: $newPosition")
        selectedPosition = newPosition
        notifyDataSetChanged()
    }

}
