package clt.india.classroom.adapter

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.data.api.response.dlp.DlpVideo
import clt.india.classroom.databinding.ItemDigitalLiteracyBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity
import clt.india.classroom.ui.resources.VideoPlayingActivityOld

class VideoChapterDetailsAdapter(
    private val chaptersTitle: List<DlpVideo>,
    private var selectedPosition: Int = 0
) : RecyclerView.Adapter<VideoChapterDetailsAdapter.ChapterViewHolder>() {

    inner class ChapterViewHolder(val binding: ItemDigitalLiteracyBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
        val binding = ItemDigitalLiteracyBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ChapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
        val video = chaptersTitle[position]
        // Fixed: Use absolutePath (camelCase) instead of absolute_path
        Log.d("VideoAdapter", "Binding video: ${video.name}, path: ${video.absolutePath}")
        holder.binding.chapterTitle.text = video.name

        holder.binding.root.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, VideoPlayingActivity::class.java)
            intent.putExtra("title", video.name)
            intent.putExtra("chapter_vid_absolute_path", video.absolutePath)
            context.startActivity(intent)
            Log.d("VideoAdapter", "Title: ${video.name}")
            Log.d("VideoAdapter", "Path: ${video.absolutePath}")
        }
    }

    override fun getItemCount(): Int {
        Log.d("VideoAdapter", "getItemCount: ${chaptersTitle.size}")
        return chaptersTitle.size
    }
}

