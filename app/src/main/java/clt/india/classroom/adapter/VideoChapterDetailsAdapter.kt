package clt.india.classroom.adapter

import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemDigitalLiteracyBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity

class VideoChapterDetailsAdapter(
    private val chaptersTitle: List<String>,
    private var selectedPosition: Int = 0
) : RecyclerView.Adapter<VideoChapterDetailsAdapter.ChapterViewHolder>() {

    inner class ChapterViewHolder(val binding: ItemDigitalLiteracyBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
        val binding = ItemDigitalLiteracyBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ChapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChapterViewHolder, position: Int) {
        val title = chaptersTitle[position]
        holder.binding.chapterTitle.text = title
        holder.binding.chapterTitle.isSelected = position == selectedPosition

        holder.binding.root.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context, VideoPlayingActivity::class.java)
            intent.putExtra("chapter_title", title)
            context.startActivity(intent)
        }
    }


    override fun getItemCount(): Int = chaptersTitle.size
}
