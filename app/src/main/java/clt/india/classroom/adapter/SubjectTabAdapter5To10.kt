package clt.india.classroom.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemSubjectTabBinding

class SubjectTabAdapter5To10(
    private val subjects: List<String>?,
    private var selectedPosition: Int = 0,
    private val onTabClick: (String) -> Unit
) : RecyclerView.Adapter<SubjectTabAdapter5To10.TabViewHolder>() {
    private lateinit var listener: OnItemClickListener
    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    inner class TabViewHolder(val binding: ItemSubjectTabBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemSubjectTabBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>old<PERSON>, position: Int) {
        val subject = subjects?.get(position)
        val textView = holder.binding.subjectTabText

        textView.text = subject

        textView.isSelected = position == selectedPosition

        textView.setOnClickListener {
            Log.d("SubjectTabAdapter5To10", "Tab clicked: $subject, Position: $position")
            val previous = selectedPosition
            selectedPosition = position
            notifyItemChanged(previous)
            notifyItemChanged(position)
            onTabClick(subject.toString())
            val currentPosition = holder.adapterPosition // Use adapterPosition for safety
            if (currentPosition != RecyclerView.NO_POSITION) { // Check for valid position
                if (::listener.isInitialized) {
                    listener.onItemClick(currentPosition)
                    Log.i("TAG", "onBindViewHolder: pos.."+currentPosition)
                }
            }
        }
    }

    override fun getItemCount(): Int = subjects?.size ?: 0

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }
}
