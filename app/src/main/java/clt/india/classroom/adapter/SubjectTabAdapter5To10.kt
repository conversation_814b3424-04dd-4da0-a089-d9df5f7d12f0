package clt.india.classroom.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import clt.india.classroom.databinding.ItemSubjectTabBinding

class SubjectTabAdapter5To10(
    private val subjects: List<String>?,
    private var selectedPosition: Int? = 0,
    private val onTabClick: (String) -> Unit
) : RecyclerView.Adapter<SubjectTabAdapter5To10.TabViewHolder>() {

    // Make listener nullable to avoid lateinit issues
    private var listener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    // Method to initialize the listener
    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    // Method to clear the listener (useful for preventing memory leaks)
    fun clearOnItemClickListener() {
        this.listener = null
    }

    // Method to update selected position from outside
    fun updateSelectedPosition(newPosition: Int) {
        if (newPosition != selectedPosition && newPosition in 0 until itemCount) {
            val previous = selectedPosition
            selectedPosition = newPosition
            notifyItemChanged(previous)
            notifyItemChanged(newPosition)
        }
    }

    // Method to update selected subject position from outside
    fun updateSelectedSubjectPosition(subject: String) {
        val newPosition = subjects?.indexOf(subject) ?: -1
        if (newPosition != -1 && newPosition != selectedPosition && newPosition in 0 until itemCount) {
            val previous = selectedPosition
            selectedPosition = newPosition
            notifyItemChanged(previous)
            notifyItemChanged(newPosition)
        }
    }
    inner class TabViewHolder(val binding: ItemSubjectTabBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        val binding = ItemSubjectTabBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TabViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        val subject = subjects?.get(position)
        val textView = holder.binding.subjectTabText

        textView.text = subject

        textView.isSelected = position == selectedPosition
        textView.setOnClickListener {
            Log.d("SubjectTabAdapter5To10", "Tab clicked: $subject, Position: $position")
            val previous = selectedPosition
            selectedPosition = position
            notifyItemChanged(previous)
            notifyItemChanged(position)
            onTabClick(subject.toString())

            val currentPosition = holder.adapterPosition // Use adapterPosition for safety
            if (currentPosition != RecyclerView.NO_POSITION) { // Check for valid position
                // Call listener if it's set
                listener?.let {
                    it.onItemClick(currentPosition)
                    Log.i("TAG", "onBindViewHolder: pos..$currentPosition")
                } ?: run {
                    Log.w("SubjectTabAdapter5To10", "Listener not set for position: $currentPosition")
                }
            }
        }
    }

    override fun getItemCount(): Int = subjects?.size ?: 0
}
