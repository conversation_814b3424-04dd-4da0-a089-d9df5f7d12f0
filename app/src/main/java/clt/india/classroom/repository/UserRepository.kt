package clt.india.classroom.repository

import clt.india.classroom.data.api.methods.UserApi
import clt.india.classroom.data.api.request.DeviceLoginRequest
import clt.india.classroom.data.api.request.DeviceLoginResponse
import clt.india.classroom.data.api.request.LoginRequest
import clt.india.classroom.data.api.response.LoginResponse
import retrofit2.Response

class UserRepository {

   suspend fun loginUser(loginRequest:LoginRequest): Response<LoginResponse>? {
      return  UserApi.getApi()?.loginUser(loginRequest = loginRequest)
    }

    suspend fun deviceLogin(deviceLoginRequest:DeviceLoginRequest): Response<DeviceLoginResponse>? {
        return  UserApi.getApi()?.deviceLogin(deviceLoginRequest = deviceLoginRequest)
    }
}