package clt.india.classroom.utils

import android.util.Log
import clt.india.classroom.data.api.request.Children

object FilterTestUtils {

    /**
     * Test the filtering functionality with sample data
     */
    fun testFiltering() {
        Log.i("FilterTestUtils", "=== TESTING CONTENT FILTERING ===")
        
        // Create sample children data
        val sampleChildren = createSampleChildren()
        
        Log.i("FilterTestUtils", "Original children count: ${sampleChildren.size}")
        sampleChildren.forEachIndexed { index, child ->
            Log.i("FilterTestUtils", "Original[$index]: ${child.name}")
        }
        
        // Test 1: Video filtering (exclude worksheets)
        Log.i("FilterTestUtils", "\n--- TEST 1: Video Filtering ---")
        val videoFiltered = ContentFilterUtils.filterChildren(
            children = sampleChildren,
            exclusionPatterns = listOf("worksheet", "listening comprehension - activity.mp4"),
            logTag = "FilterTestUtils"
        )
        Log.i("FilterTestUtils", "Video filtered count: ${videoFiltered.size}")
        
        // Test 2: Worksheet filtering (include only worksheets and PDFs)
        Log.i("FilterTestUtils", "\n--- TEST 2: Worksheet Filtering ---")
        val worksheetFiltered = ContentFilterUtils.filterCustom(
            children = sampleChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                name.contains("worksheet") || name.endsWith(".pdf")
            },
            logTag = "FilterTestUtils"
        )
        Log.i("FilterTestUtils", "Worksheet filtered count: ${worksheetFiltered.size}")
        
        // Test 3: File type filtering
        Log.i("FilterTestUtils", "\n--- TEST 3: File Type Filtering ---")
        val mp4Filtered = ContentFilterUtils.filterByFileType(
            children = sampleChildren,
            allowedTypes = listOf("mp4"),
            logTag = "FilterTestUtils"
        )
        Log.i("FilterTestUtils", "MP4 filtered count: ${mp4Filtered.size}")
        
        Log.i("FilterTestUtils", "=== FILTERING TESTS COMPLETE ===")
    }
    
    private fun createSampleChildren(): ArrayList<Children> {
        return arrayListOf(
            Children().apply { name = "1. Introduction Video.mp4" },
            Children().apply { name = "2. Listening Comprehension - Activity.mp4" },
            Children().apply { name = "3. Reading Exercise.mp4" },
            Children().apply { name = "4. Worksheet - Practice.pdf" },
            Children().apply { name = "5. Worksheet - Assessment.pdf" },
            Children().apply { name = "6. Story Video.mp4" },
            Children().apply { name = "7. Worksheet - Homework.doc" },
            Children().apply { name = "8. Summary Video.mp4" },
            Children().apply { name = "9. Activity Instructions.txt" },
            Children().apply { name = "10. Final Assessment Worksheet.pdf" }
        )
    }
    
    /**
     * Test specific filtering scenarios
     */
    fun testSpecificScenarios() {
        Log.i("FilterTestUtils", "=== TESTING SPECIFIC SCENARIOS ===")
        
        val sampleChildren = createSampleChildren()
        
        // Scenario 1: OneToFourVideoFragment filtering
        Log.i("FilterTestUtils", "\n--- Scenario 1: Video Fragment ---")
        val videoFragmentResult = ContentFilterUtils.filterChildren(
            children = sampleChildren,
            exclusionPatterns = listOf(
                "listening comprehension - activity.mp4",
                "worksheet"
            ),
            logTag = "VideoFragmentTest"
        )
        Log.i("FilterTestUtils", "Video fragment result: ${videoFragmentResult.size} items")
        videoFragmentResult.forEach { child ->
            Log.i("FilterTestUtils", "Video result: ${child.name}")
        }
        
        // Scenario 2: OneToFourWorkSheetsFragment filtering
        Log.i("FilterTestUtils", "\n--- Scenario 2: Worksheet Fragment ---")
        val worksheetFragmentResult = ContentFilterUtils.filterCustom(
            children = sampleChildren,
            filterFunction = { child ->
                val name = child.name?.lowercase() ?: ""
                name.contains("worksheet") || name.endsWith(".pdf")
            },
            logTag = "WorksheetFragmentTest"
        )
        Log.i("FilterTestUtils", "Worksheet fragment result: ${worksheetFragmentResult.size} items")
        worksheetFragmentResult.forEach { child ->
            Log.i("FilterTestUtils", "Worksheet result: ${child.name}")
        }
        
        Log.i("FilterTestUtils", "=== SPECIFIC SCENARIOS COMPLETE ===")
    }
}
