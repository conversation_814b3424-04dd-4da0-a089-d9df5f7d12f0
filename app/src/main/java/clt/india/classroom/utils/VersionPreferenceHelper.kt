package clt.india.classroom.utils

import android.content.Context
import android.content.SharedPreferences

object VersionPreferenceHelper {
    private const val PREF_NAME = "version_pref"
    private const val SELECTED_VERSION_KEY = "selected_version"

    fun saveSelectedGrade(context: Context, version: String) {
        val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        prefs.edit().putString(SELECTED_VERSION_KEY, version).apply()
    }

    fun getSelectedGrade(context: Context): String? {
        val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        return prefs.getString(SELECTED_VERSION_KEY, null)
    }
}