package clt.india.classroom.utils

import android.content.Context
import android.content.SharedPreferences

object GradePreferenceHelper {
    private const val PREF_NAME = "selection_prefs"

    enum class SelectionType(val key: String) {
        GRADE("selected_grade"),
        VERSION("selected_version")
    }

    fun saveSelection(context: Context, value: String, type: SelectionType) {
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            .edit()
            .putString(type.key, value)
            .apply()
    }

    fun getSelection(context: Context, type: SelectionType): String? {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            .getString(type.key, null)
    }
}
