package clt.india.classroom.utils

import android.util.Log

object IndexBoundsChecker {

    /**
     * Safe array/list access with bounds checking
     */
    fun <T> safeGet(list: List<T>?, index: Int?, defaultIndex: Int = 0, logTag: String = "IndexBoundsChecker"): T? {
        Log.i(logTag, "safeGet: list size = ${list?.size}, requested index = $index, defaultIndex = $defaultIndex")
        
        if (list.isNullOrEmpty()) {
            Log.w(logTag, "safeGet: List is null or empty")
            return null
        }
        
        val safeIndex = when {
            index == null -> {
                Log.i(logTag, "safeGet: Index is null, using defaultIndex $defaultIndex")
                defaultIndex
            }
            index < 0 -> {
                Log.w(logTag, "safeGet: Index $index is negative, using defaultIndex $defaultIndex")
                defaultIndex
            }
            index >= list.size -> {
                Log.w(logTag, "safeGet: Index $index is out of bounds (size: ${list.size}), using defaultIndex $defaultIndex")
                defaultIndex
            }
            else -> {
                Log.i(logTag, "safeGet: Index $index is valid")
                index
            }
        }
        
        // Final bounds check for the safe index
        return if (safeIndex >= 0 && safeIndex < list.size) {
            val result = list[safeIndex]
            Log.i(logTag, "safeGet: Returning item at index $safeIndex: $result")
            result
        } else {
            Log.e(logTag, "safeGet: Even safeIndex $safeIndex is out of bounds for list size ${list.size}")
            null
        }
    }
    
    /**
     * Validate index bounds
     */
    fun isValidIndex(list: List<*>?, index: Int?): Boolean {
        return list != null && index != null && index >= 0 && index < list.size
    }
    
    /**
     * Get safe index within bounds
     */
    fun getSafeIndex(listSize: Int, requestedIndex: Int?, defaultIndex: Int = 0): Int {
        return when {
            requestedIndex == null -> defaultIndex
            requestedIndex < 0 -> defaultIndex
            requestedIndex >= listSize -> defaultIndex
            else -> requestedIndex
        }
    }
    
    /**
     * Debug array/list access issues
     */
    fun debugArrayAccess(
        arrayName: String,
        arraySize: Int?,
        requestedIndex: Int?,
        logTag: String = "IndexBoundsChecker"
    ) {
        Log.i(logTag, "=== DEBUGGING ARRAY ACCESS ===")
        Log.i(logTag, "Array name: $arrayName")
        Log.i(logTag, "Array size: $arraySize")
        Log.i(logTag, "Requested index: $requestedIndex")
        
        if (arraySize == null) {
            Log.e(logTag, "ERROR: Array size is null")
        } else if (arraySize == 0) {
            Log.e(logTag, "ERROR: Array is empty")
        } else if (requestedIndex == null) {
            Log.w(logTag, "WARNING: Requested index is null")
        } else if (requestedIndex < 0) {
            Log.e(logTag, "ERROR: Requested index is negative")
        } else if (requestedIndex >= arraySize) {
            Log.e(logTag, "ERROR: Requested index is out of bounds")
        } else {
            Log.i(logTag, "SUCCESS: Index is valid")
        }
        
        Log.i(logTag, "=== END ARRAY ACCESS DEBUG ===")
    }
    
    /**
     * Safe subject selection helper
     */
    fun safeSelectSubject(
        subjectNames: List<String>?,
        selectedPosition: Int?,
        logTag: String = "IndexBoundsChecker"
    ): Pair<String?, Int> {
        Log.i(logTag, "safeSelectSubject: subjectNames = $subjectNames, selectedPosition = $selectedPosition")
        
        if (subjectNames.isNullOrEmpty()) {
            Log.e(logTag, "safeSelectSubject: No subjects available")
            return Pair(null, 0)
        }
        
        val safePosition = getSafeIndex(subjectNames.size, selectedPosition, 0)
        val selectedSubject = subjectNames[safePosition]
        
        Log.i(logTag, "safeSelectSubject: Selected '$selectedSubject' at position $safePosition")
        return Pair(selectedSubject, safePosition)
    }
}
