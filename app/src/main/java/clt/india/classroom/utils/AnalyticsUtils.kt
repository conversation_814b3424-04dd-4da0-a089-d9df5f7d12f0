package clt.india.classroom.utils

import android.os.Bundle
import android.util.Log
import clt.india.classroom.CLTApplication
import com.google.firebase.analytics.FirebaseAnalytics

object AnalyticsUtils {

    fun logLargeString(str: String) {
        if (str.length > 3000) {
            Log.i("long ", str.substring(0, 3000))
            logLargeString(str.substring(3000))
        } else {
            Log.i("long ", str) // continuation
        }
    }

    // Track screen views
    fun logScreenView(screenName: String) {
        val bundle = Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
            putString(FirebaseAnalytics.Param.SCREEN_CLASS, screenName)
        }
        CLTApplication.analytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

    // Track login events
    fun logLogin(method: String) {
        val bundle = Bundle().apply {
            putString(FirebaseAnalytics.Param.METHOD, method)
        }
        CLTApplication.analytics.logEvent(FirebaseAnalytics.Event.LOGIN, bundle)
    }

    // Track custom events
    fun logCustomEvent(eventName: String, params: Bundle? = null) {
        CLTApplication.analytics.logEvent(eventName, params)
    }

    // Set user properties
    fun setUserProperty(name: String, value: String) {
        CLTApplication.analytics.setUserProperty(name, value)
    }
}
