package clt.india.classroom.utils

/**
 * Utility class for validating user input
 */
object ValidationUtils {
    
    /**
     * Validates an email address
     * @param email The email address to validate
     * @return true if the email is valid, false otherwise
     */
    fun isValidEmail(email: String): Bo<PERSON>an {
        val emailRegex = Regex("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")
        return email.isNotBlank() && emailRegex.matches(email)
    }
    
    /**
     * Validates a password
     * @param password The password to validate
     * @return true if the password is valid, false otherwise
     * 
     * Password requirements:
     * - At least 8 characters long
     * - Contains at least one uppercase letter
     * - Contains at least one lowercase letter
     * - Contains at least one digit
     * - Contains at least one special character
     */
    fun isValidPassword(password: String): <PERSON><PERSON><PERSON> {
        if (password.length < 8) return false
        
        val hasUppercase = password.any { it.isUpperCase() }
        val hasLowercase = password.any { it.isLowerCase() }
        val hasDigit = password.any { it.isDigit() }
        val hasSpecialChar = password.any { !it.isLetterOrDigit() }
        
        return hasUppercase && hasLowercase && hasDigit && hasSpecialChar
    }
    
    /**
     * Validates a phone number
     * @param phoneNumber The phone number to validate
     * @return true if the phone number is valid, false otherwise
     * 
     * Valid phone number formats:
     * - 10 digits (e.g., 1234567890)
     * - With optional country code (e.g., +91 1234567890)
     * - With optional separators (e.g., ************)
     */
    fun isValidPhoneNumber(phoneNumber: String): Boolean {
        // Remove all non-digit characters except the + sign at the beginning
        val digitsOnly = phoneNumber.replace(Regex("[^+\\d]"), "")
        
        // Check if it's a valid format
        return when {
            // Format: 10 digits
            digitsOnly.matches(Regex("\\d{10}")) -> true
            
            // Format: +[country code][phone number] (e.g., +911234567890)
            digitsOnly.matches(Regex("\\+\\d{1,4}\\d{10}")) -> true
            
            else -> false
        }
    }
    
    /**
     * Validates a name
     * @param name The name to validate
     * @return true if the name is valid, false otherwise
     * 
     * Name requirements:
     * - At least 2 characters long
     * - Contains only letters, spaces, hyphens, and apostrophes
     */
    fun isValidName(name: String): Boolean {
        if (name.length < 2) return false
        
        // Allow letters, spaces, hyphens, and apostrophes
        val nameRegex = Regex("^[A-Za-z\\s'-]+$")
        return nameRegex.matches(name)
    }
}
