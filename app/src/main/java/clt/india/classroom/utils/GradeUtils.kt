package clt.india.classroom.utils

object GradeUtils {
    fun getOrdinalGrade(grade: String): String {
        val number = grade.filter { it.isDigit() }.toIntOrNull() ?: return "Grade not found"
        val suffix = when {
            number in 11..13 -> "th"
            number % 10 == 1 -> "st"
            number % 10 == 2 -> "nd"
            number % 10 == 3 -> "rd"
            else -> "th"
        }
        return "${number}${suffix} Grade (English)"
    }

}