package clt.india.classroom.utils

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken

/**
 * Utility class to handle JSON parsing errors gracefully
 */
object JsonErrorHandler {

    /**
     * Safe JSON parsing with detailed error logging
     */
    inline fun <reified T> safeParseJson(
        jsonString: String?,
        className: String = "Unknown",
        fallback: T? = null
    ): T? {
        if (jsonString.isNullOrEmpty()) {
            Log.w("JsonErrorHandler", "JSON string is null or empty for $className")
            return fallback
        }

        return try {
            val gson = Gson()
            Log.d("JsonErrorHandler", "Parsing JSON for $className, length: ${jsonString.length}")
            Log.d("JsonErrorHandler", "JSON preview: ${jsonString.take(100)}...")
            
            gson.fromJson(jsonString, T::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e("JsonErrorHandler", "JSON syntax error for $className: ${e.message}")
            Log.e("JsonErrorHandler", "Problematic JSON: ${jsonString.take(500)}")
            handleJsonSyntaxError(jsonString, e)
            fallback
        } catch (e: Exception) {
            Log.e("JsonErrorHandler", "Unexpected error parsing JSON for $className: ${e.message}")
            fallback
        }
    }

    /**
     * Safe JSON array parsing
     */
    inline fun <reified T> safeParseJsonArray(
        jsonString: String?,
        className: String = "Unknown",
        fallback: List<T> = emptyList()
    ): List<T> {
        if (jsonString.isNullOrEmpty()) {
            Log.w("JsonErrorHandler", "JSON string is null or empty for $className array")
            return fallback
        }

        return try {
            val gson = Gson()
            Log.d("JsonErrorHandler", "Parsing JSON array for $className, length: ${jsonString.length}")
            
            // Check if it's actually an array
            if (!jsonString.trim().startsWith("[")) {
                Log.w("JsonErrorHandler", "JSON string is not an array for $className: ${jsonString.take(50)}")
                return fallback
            }
            
            val type = object : TypeToken<List<T>>() {}.type
            gson.fromJson<List<T>>(jsonString, type) ?: fallback
        } catch (e: JsonSyntaxException) {
            Log.e("JsonErrorHandler", "JSON syntax error for $className array: ${e.message}")
            Log.e("JsonErrorHandler", "Problematic JSON: ${jsonString.take(500)}")
            handleJsonSyntaxError(jsonString, e)
            fallback
        } catch (e: Exception) {
            Log.e("JsonErrorHandler", "Unexpected error parsing JSON array for $className: ${e.message}")
            fallback
        }
    }

    /**
     * Handle specific JSON syntax errors and provide helpful debugging info
     */
    private fun handleJsonSyntaxError(jsonString: String, error: JsonSyntaxException) {
        val message = error.message ?: "Unknown JSON error"
        
        when {
            message.contains("Expected BEGIN_ARRAY but was NUMBER") -> {
                Log.e("JsonErrorHandler", "ERROR: Expected JSON array but got a number")
                Log.e("JsonErrorHandler", "SOLUTION: Check if the data source is returning the correct format")
                Log.e("JsonErrorHandler", "ACTUAL DATA: ${jsonString.take(100)}")
            }
            message.contains("Expected BEGIN_OBJECT but was STRING") -> {
                Log.e("JsonErrorHandler", "ERROR: Expected JSON object but got a string")
                Log.e("JsonErrorHandler", "SOLUTION: Check if the string needs to be parsed as JSON first")
            }
            message.contains("Expected BEGIN_ARRAY but was STRING") -> {
                Log.e("JsonErrorHandler", "ERROR: Expected JSON array but got a string")
                Log.e("JsonErrorHandler", "SOLUTION: Check if the string contains escaped JSON")
            }
            message.contains("Unterminated object") -> {
                Log.e("JsonErrorHandler", "ERROR: JSON object is not properly closed")
                Log.e("JsonErrorHandler", "SOLUTION: Check for missing closing braces")
            }
            message.contains("Unterminated array") -> {
                Log.e("JsonErrorHandler", "ERROR: JSON array is not properly closed")
                Log.e("JsonErrorHandler", "SOLUTION: Check for missing closing brackets")
            }
            else -> {
                Log.e("JsonErrorHandler", "ERROR: Unknown JSON syntax error: $message")
            }
        }
    }

    /**
     * Validate JSON format before parsing
     */
    fun validateJsonFormat(jsonString: String?): JsonValidationResult {
        if (jsonString.isNullOrEmpty()) {
            return JsonValidationResult(false, "JSON string is null or empty")
        }

        val trimmed = jsonString.trim()
        
        return when {
            trimmed.startsWith("{") && trimmed.endsWith("}") -> {
                JsonValidationResult(true, "Valid JSON object format", JsonType.OBJECT)
            }
            trimmed.startsWith("[") && trimmed.endsWith("]") -> {
                JsonValidationResult(true, "Valid JSON array format", JsonType.ARRAY)
            }
            trimmed.startsWith("\"") && trimmed.endsWith("\"") -> {
                JsonValidationResult(false, "JSON string appears to be a quoted string", JsonType.STRING)
            }
            trimmed.matches(Regex("^\\d+$")) -> {
                JsonValidationResult(false, "JSON string is a number", JsonType.NUMBER)
            }
            trimmed.matches(Regex("^(true|false)$")) -> {
                JsonValidationResult(false, "JSON string is a boolean", JsonType.BOOLEAN)
            }
            else -> {
                JsonValidationResult(false, "Unknown JSON format", JsonType.UNKNOWN)
            }
        }
    }

    /**
     * Data class for JSON validation results
     */
    data class JsonValidationResult(
        val isValid: Boolean,
        val message: String,
        val type: JsonType = JsonType.UNKNOWN
    )

    /**
     * Enum for JSON types
     */
    enum class JsonType {
        OBJECT, ARRAY, STRING, NUMBER, BOOLEAN, UNKNOWN
    }
}

/**
 * Extension functions for safe JSON parsing
 */
inline fun <reified T> String?.safeParseJson(fallback: T? = null): T? {
    return JsonErrorHandler.safeParseJson<T>(this, T::class.java.simpleName, fallback)
}

inline fun <reified T> String?.safeParseJsonArray(fallback: List<T> = emptyList()): List<T> {
    return JsonErrorHandler.safeParseJsonArray<T>(this, T::class.java.simpleName, fallback)
}

/**
 * Specific error handlers for common JSON issues
 */
object JsonErrorSolutions {

    fun handleExpectedArrayButWasNumber(jsonString: String): String {
        return """
            ERROR: Expected JSON array but got number
            
            PROBLEM: Your code expects a JSON array like [1,2,3] but received a single number like 123
            
            POSSIBLE CAUSES:
            1. API endpoint changed response format
            2. Database query returning count instead of list
            3. Wrong field being accessed in JSON response
            
            SOLUTIONS:
            1. Check API documentation for correct endpoint
            2. Verify database query returns list, not count
            3. Update code to handle both single values and arrays
            4. Add fallback logic for different response formats
            
            ACTUAL DATA RECEIVED: ${jsonString.take(200)}
        """.trimIndent()
    }

    fun handleExpectedObjectButWasString(jsonString: String): String {
        return """
            ERROR: Expected JSON object but got string
            
            PROBLEM: Your code expects a JSON object like {"key":"value"} but received a plain string
            
            POSSIBLE CAUSES:
            1. Double-encoded JSON (JSON string inside JSON string)
            2. API returning error message as string instead of JSON
            3. Wrong content-type in API response
            
            SOLUTIONS:
            1. Parse the string as JSON first: gson.fromJson(gson.fromJson(response, String.class), YourClass.class)
            2. Check API response headers and status codes
            3. Add error handling for string responses
            
            ACTUAL DATA RECEIVED: ${jsonString.take(200)}
        """.trimIndent()
    }
}

/**
 * Debug utility for JSON issues
 */
object JsonDebugger {

    fun debugJsonIssue(jsonString: String?, expectedType: String) {
        Log.i("JsonDebugger", "=== JSON DEBUG SESSION ===")
        Log.i("JsonDebugger", "Expected type: $expectedType")
        
        if (jsonString == null) {
            Log.e("JsonDebugger", "JSON string is NULL")
            return
        }
        
        Log.i("JsonDebugger", "JSON string length: ${jsonString.length}")
        Log.i("JsonDebugger", "JSON string preview: ${jsonString.take(200)}")
        
        val validation = JsonErrorHandler.validateJsonFormat(jsonString)
        Log.i("JsonDebugger", "Validation result: ${validation.message}")
        Log.i("JsonDebugger", "Detected type: ${validation.type}")
        Log.i("JsonDebugger", "Is valid: ${validation.isValid}")
        
        // Character analysis
        val trimmed = jsonString.trim()
        Log.i("JsonDebugger", "First character: '${trimmed.firstOrNull()}'")
        Log.i("JsonDebugger", "Last character: '${trimmed.lastOrNull()}'")
        Log.i("JsonDebugger", "Contains quotes: ${trimmed.contains("\"")}")
        Log.i("JsonDebugger", "Contains braces: ${trimmed.contains("{") || trimmed.contains("}")}")
        Log.i("JsonDebugger", "Contains brackets: ${trimmed.contains("[") || trimmed.contains("]")}")
        
        Log.i("JsonDebugger", "=== END DEBUG SESSION ===")
    }
}
