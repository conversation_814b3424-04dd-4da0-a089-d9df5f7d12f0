package clt.india.classroom.utils

import android.os.Bundle
import android.util.Log

object FragmentArgumentsTest {

    /**
     * Test to verify fragment arguments are being passed correctly
     */
    fun testFragmentArguments(
        fragmentName: String,
        arguments: Bundle?,
        expectedSubject: String?
    ) {
        Log.i("FragmentArgumentsTest", "=== TESTING FRAGMENT ARGUMENTS FOR $fragmentName ===")
        
        // Test 1: Arguments bundle exists
        Log.i("FragmentArgumentsTest", "1. Arguments Bundle Test:")
        val argumentsExist = arguments != null
        Log.i("FragmentArgumentsTest", "   Arguments exist: $argumentsExist")
        
        if (arguments != null) {
            // Test 2: ARG_SUBJECT key exists
            Log.i("FragmentArgumentsTest", "2. ARG_SUBJECT Key Test:")
            val hasSubjectKey = arguments.containsKey("subject")
            Log.i("FragmentArgumentsTest", "   Has 'subject' key: $hasSubjectKey")
            
            // Test 3: Subject value
            Log.i("FragmentArgumentsTest", "3. Subject Value Test:")
            val actualSubject = arguments.getString("subject")
            Log.i("FragmentArgumentsTest", "   Expected subject: $expectedSubject")
            Log.i("FragmentArgumentsTest", "   Actual subject: $actualSubject")
            Log.i("FragmentArgumentsTest", "   Subject is null: ${actualSubject == null}")
            Log.i("FragmentArgumentsTest", "   Subject is empty: ${actualSubject?.isEmpty() ?: false}")
            
            // Test 4: All keys in bundle
            Log.i("FragmentArgumentsTest", "4. All Bundle Keys:")
            arguments.keySet().forEach { key ->
                val value = arguments.get(key)
                Log.i("FragmentArgumentsTest", "   Key: '$key' = '$value' (${value?.javaClass?.simpleName})")
            }
            
            // Overall result
            val testPassed = argumentsExist && hasSubjectKey && !actualSubject.isNullOrEmpty()
            Log.i("FragmentArgumentsTest", "=== TEST RESULT: ${if (testPassed) "PASS" else "FAIL"} ===")
            
            if (!testPassed) {
                Log.e("FragmentArgumentsTest", "ISSUE DETECTED:")
                if (!argumentsExist) {
                    Log.e("FragmentArgumentsTest", "- Arguments bundle is null")
                }
                if (!hasSubjectKey) {
                    Log.e("FragmentArgumentsTest", "- 'subject' key is missing from bundle")
                }
                if (actualSubject.isNullOrEmpty()) {
                    Log.e("FragmentArgumentsTest", "- Subject value is null or empty")
                }
            }
        } else {
            Log.e("FragmentArgumentsTest", "=== TEST RESULT: FAIL ===")
            Log.e("FragmentArgumentsTest", "ISSUE: Arguments bundle is null")
        }
        
        Log.i("FragmentArgumentsTest", "=== END FRAGMENT ARGUMENTS TEST ===")
    }
    
    /**
     * Test fragment creation process
     */
    fun testFragmentCreation(
        fragmentName: String,
        subjectPassedToNewInstance: String?,
        bundleCreated: Bundle?
    ) {
        Log.i("FragmentArgumentsTest", "=== TESTING FRAGMENT CREATION FOR $fragmentName ===")
        
        Log.i("FragmentArgumentsTest", "Subject passed to newInstance(): $subjectPassedToNewInstance")
        Log.i("FragmentArgumentsTest", "Bundle created: ${bundleCreated != null}")
        
        if (bundleCreated != null) {
            Log.i("FragmentArgumentsTest", "Bundle contents:")
            bundleCreated.keySet().forEach { key ->
                val value = bundleCreated.get(key)
                Log.i("FragmentArgumentsTest", "  $key = $value")
            }
        }
        
        Log.i("FragmentArgumentsTest", "=== END FRAGMENT CREATION TEST ===")
    }
    
    /**
     * Quick diagnostic for common argument issues
     */
    fun quickDiagnostic(fragmentName: String) {
        Log.i("FragmentArgumentsTest", "=== QUICK DIAGNOSTIC FOR $fragmentName ===")
        Log.i("FragmentArgumentsTest", "Common issues to check:")
        Log.i("FragmentArgumentsTest", "1. Is newInstance() being called with a non-null subject?")
        Log.i("FragmentArgumentsTest", "2. Is the Bundle being created properly in newInstance()?")
        Log.i("FragmentArgumentsTest", "3. Is the 'subject' key being added to the bundle?")
        Log.i("FragmentArgumentsTest", "4. Is the fragment.arguments being set?")
        Log.i("FragmentArgumentsTest", "5. Is onCreate() being called after the fragment is created?")
        Log.i("FragmentArgumentsTest", "6. Is the arguments?.let block being executed?")
        Log.i("FragmentArgumentsTest", "=== END QUICK DIAGNOSTIC ===")
    }
}
