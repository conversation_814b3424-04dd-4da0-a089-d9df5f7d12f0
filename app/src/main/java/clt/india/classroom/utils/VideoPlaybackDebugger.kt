package clt.india.classroom.utils

import android.os.Handler
import android.os.Looper
import android.util.Log
import java.io.File

/**
 * Debugging utility for VideoPlayingActivity ANR issues
 */
object VideoPlaybackDebugger {

    /**
     * Debug video file and decryption process
     */
    fun debugVideoFile(filePath: String?) {
        Log.i("VideoPlaybackDebugger", "=== VIDEO FILE DEBUG ===")
        
        if (filePath.isNullOrEmpty()) {
            Log.e("VideoPlaybackDebugger", "❌ Video file path is null or empty")
            return
        }
        
        try {
            val file = File(filePath)
            Log.i("VideoPlaybackDebugger", "File path: $filePath")
            Log.i("VideoPlaybackDebugger", "File exists: ${file.exists()}")
            Log.i("VideoPlaybackDebugger", "File size: ${file.length()} bytes")
            Log.i("VideoPlaybackDebugger", "File readable: ${file.canRead()}")
            Log.i("VideoPlaybackDebugger", "File extension: ${file.extension}")
            
            if (file.length() == 0L) {
                Log.e("VideoPlaybackDebugger", "❌ File is empty")
            } else if (file.length() > 100 * 1024 * 1024) { // 100MB
                Log.w("VideoPlaybackDebugger", "⚠️ Large file detected: ${file.length() / 1024 / 1024}MB")
            }
            
        } catch (e: Exception) {
            Log.e("VideoPlaybackDebugger", "❌ Error checking video file", e)
        }
    }

    /**
     * Monitor main thread blocking
     */
    fun monitorMainThreadBlocking() {
        val handler = Handler(Looper.getMainLooper())
        
        handler.post {
            val startTime = System.currentTimeMillis()
            
            // Check if main thread is blocked
            handler.postDelayed({
                val endTime = System.currentTimeMillis()
                val delay = endTime - startTime
                
                if (delay > 100) { // More than 100ms delay
                    Log.w("VideoPlaybackDebugger", "⚠️ Main thread blocked for ${delay}ms")
                } else {
                    Log.i("VideoPlaybackDebugger", "✅ Main thread responsive (${delay}ms)")
                }
            }, 50)
        }
    }

    /**
     * Debug decryption performance
     */
    fun debugDecryptionPerformance(filePath: String, startTime: Long, endTime: Long) {
        val duration = endTime - startTime
        val file = File(filePath)
        val fileSizeMB = file.length() / 1024.0 / 1024.0
        val speedMBps = if (duration > 0) fileSizeMB / (duration / 1000.0) else 0.0
        
        Log.i("VideoPlaybackDebugger", "=== DECRYPTION PERFORMANCE ===")
        Log.i("VideoPlaybackDebugger", "File size: ${String.format("%.2f", fileSizeMB)} MB")
        Log.i("VideoPlaybackDebugger", "Decryption time: ${duration}ms")
        Log.i("VideoPlaybackDebugger", "Decryption speed: ${String.format("%.2f", speedMBps)} MB/s")
        
        when {
            duration > 10000 -> Log.e("VideoPlaybackDebugger", "❌ Very slow decryption (>10s)")
            duration > 5000 -> Log.w("VideoPlaybackDebugger", "⚠️ Slow decryption (>5s)")
            else -> Log.i("VideoPlaybackDebugger", "✅ Good decryption speed")
        }
    }

    /**
     * Check memory usage
     */
    fun checkMemoryUsage() {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val usedPercentage = (usedMemory * 100 / maxMemory)
        
        Log.i("VideoPlaybackDebugger", "=== MEMORY USAGE ===")
        Log.i("VideoPlaybackDebugger", "Used memory: ${usedMemory / 1024 / 1024}MB")
        Log.i("VideoPlaybackDebugger", "Max memory: ${maxMemory / 1024 / 1024}MB")
        Log.i("VideoPlaybackDebugger", "Usage: $usedPercentage%")
        
        when {
            usedPercentage > 90 -> Log.e("VideoPlaybackDebugger", "❌ Critical memory usage")
            usedPercentage > 75 -> Log.w("VideoPlaybackDebugger", "⚠️ High memory usage")
            else -> Log.i("VideoPlaybackDebugger", "✅ Normal memory usage")
        }
    }

    /**
     * Debug video player state
     */
    fun debugVideoPlayerState(
        isPlaying: Boolean,
        currentPosition: Int,
        duration: Int,
        bufferPercentage: Int
    ) {
        Log.i("VideoPlaybackDebugger", "=== VIDEO PLAYER STATE ===")
        Log.i("VideoPlaybackDebugger", "Is playing: $isPlaying")
        Log.i("VideoPlaybackDebugger", "Current position: ${formatTime(currentPosition)}")
        Log.i("VideoPlaybackDebugger", "Duration: ${formatTime(duration)}")
        Log.i("VideoPlaybackDebugger", "Buffer percentage: $bufferPercentage%")
        
        if (duration > 0) {
            val progress = (currentPosition * 100 / duration)
            Log.i("VideoPlaybackDebugger", "Progress: $progress%")
        }
    }

    /**
     * Format time for logging
     */
    private fun formatTime(ms: Int): String {
        val seconds = ms / 1000 % 60
        val minutes = ms / 1000 / 60
        return String.format("%d:%02d", minutes, seconds)
    }

    /**
     * Log ANR prevention tips
     */
    fun logAnrPreventionTips() {
        Log.i("VideoPlaybackDebugger", """
            === ANR PREVENTION TIPS ===
            
            1. NEVER do file I/O on main thread
            2. Use coroutines for decryption
            3. Show loading indicators during long operations
            4. Implement proper error handling
            5. Clean up resources in onDestroy()
            6. Remove handler callbacks to prevent leaks
            7. Use background threads for heavy operations
            8. Monitor memory usage
            9. Implement timeouts for operations
            10. Test with large video files
        """.trimIndent())
    }
}

/**
 * Performance monitoring for video operations
 */
object VideoPerformanceMonitor {

    private val operationTimes = mutableMapOf<String, Long>()

    /**
     * Start timing an operation
     */
    fun startOperation(operationName: String) {
        operationTimes[operationName] = System.currentTimeMillis()
        Log.i("VideoPerformanceMonitor", "Started: $operationName")
    }

    /**
     * End timing an operation
     */
    fun endOperation(operationName: String) {
        val startTime = operationTimes[operationName]
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            Log.i("VideoPerformanceMonitor", "Completed: $operationName in ${duration}ms")
            
            // Warn about slow operations
            when {
                duration > 5000 -> Log.e("VideoPerformanceMonitor", "❌ Very slow operation: $operationName (${duration}ms)")
                duration > 2000 -> Log.w("VideoPerformanceMonitor", "⚠️ Slow operation: $operationName (${duration}ms)")
                else -> Log.i("VideoPerformanceMonitor", "✅ Good performance: $operationName (${duration}ms)")
            }
            
            operationTimes.remove(operationName)
        } else {
            Log.w("VideoPerformanceMonitor", "No start time found for: $operationName")
        }
    }

    /**
     * Monitor ongoing operations
     */
    fun checkOngoingOperations() {
        val currentTime = System.currentTimeMillis()
        operationTimes.forEach { (operation, startTime) ->
            val duration = currentTime - startTime
            if (duration > 10000) { // 10 seconds
                Log.e("VideoPerformanceMonitor", "❌ Long-running operation: $operation (${duration}ms)")
            }
        }
    }
}

/**
 * ANR detection and reporting
 */
object AnrDetector {

    private var lastMainThreadCheck = System.currentTimeMillis()
    private val handler = Handler(Looper.getMainLooper())

    /**
     * Start ANR monitoring
     */
    fun startMonitoring() {
        monitorMainThread()
    }

    private fun monitorMainThread() {
        val checkTime = System.currentTimeMillis()
        
        handler.post {
            val responseTime = System.currentTimeMillis() - checkTime
            
            if (responseTime > 500) { // 500ms threshold
                Log.e("AnrDetector", "❌ Potential ANR detected: Main thread blocked for ${responseTime}ms")
                logCurrentStackTrace()
            } else if (responseTime > 100) {
                Log.w("AnrDetector", "⚠️ Main thread slow: ${responseTime}ms")
            }
            
            lastMainThreadCheck = System.currentTimeMillis()
            
            // Schedule next check
            handler.postDelayed({ monitorMainThread() }, 1000)
        }
    }

    private fun logCurrentStackTrace() {
        val stackTrace = Thread.currentThread().stackTrace
        Log.e("AnrDetector", "Current stack trace:")
        stackTrace.take(10).forEach { element ->
            Log.e("AnrDetector", "  at $element")
        }
    }

    /**
     * Stop ANR monitoring
     */
    fun stopMonitoring() {
        handler.removeCallbacksAndMessages(null)
    }
}
