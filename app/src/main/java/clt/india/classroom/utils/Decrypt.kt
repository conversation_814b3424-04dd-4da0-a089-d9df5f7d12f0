import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.security.SecureRandom
import java.util.Calendar
import javax.crypto.Cipher
import javax.crypto.CipherOutputStream
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

class Decrypt {
    private var sbAl = ""
    var fileName: String? = null
    private var fileToDecrypt: File? = null
    private val M3 = 'E'
    var finalAsciiVal: Int = 0
    var finalVal: Int = 0
    private var fname: String? = null
    private var sbkey: StringBuilder? = null
    private var mFileTemp: File? = null
    internal var outPutPath: String? = null
    private var decryptedFile: File? = null
    private val encSym = "_E"
    private val chArr = 124
    private var builder: StringBuilder? = null
    var d1: Decrypt? = null

    constructor()

    constructor(var1: String, var2: String?) {
        this.fileName = var1
        this.outPutPath = var2
    }

    val ascii: Unit
        get() {
            try {
                this.sbAl = 'A'.toString()
                this.finalVal = 0
                this.finalAsciiVal = 0
                println(" Input FilePath : " + this.fileName)
                this.fileToDecrypt = File(this.fileName)
                val var1 = this.getFileNameWOExtension(this.fileToDecrypt!!.getName()).toCharArray()
                val var2 = var1.size
                val var3 = IntArray(var2)

                var var4: Int
                var4 = 0
                while (var4 < var2) {
                    var3[var4] = var1[var4].code
                    ++var4
                }

                var4 = 0
                while (var4 < var2) {
                    this.finalAsciiVal += var1[var4].code
                    ++var4
                }

                val var7 = this.finalAsciiVal.toString().toCharArray()

                for (var5 in var7.indices) {
                    this.finalVal += var7[var5].toString().toInt()
                }

                this.alphaNumeric
                this.buildFinal()
            } catch (var6: Exception) {
                var6.printStackTrace()
            }
        }

    fun getFileNameWOExtension(var1: String): String {
        var var1 = var1
        val var2 = var1.lastIndexOf(".")
        if (var2 != -1) {
            var1 = var1.substring(0, var2)
            if (var1.endsWith(this.encSym)) {
                var1 = var1.substring(0, var2 - 2)
            }

            return var1
        } else {
            return var1
        }
    }

    private val alphaNumeric: Unit
        get() {
            val var1 = "UVWXYZzyxwvutsrqponm"
            val var2 = "RQPONMLKJIHGFEDCBAab"
            val var3 = "wxyz0123456789987654"
            this.builder = StringBuilder()
            this.builder!!.append("3210")
            this.builder!!.append(var3)
            this.builder!!.append("cdefghijklmnopqrstuv")
            this.builder!!.append(var2)
            this.builder!!.append("lkjihgfedcbaZYXWVUTS")
            this.builder!!.append(var1)
            this.builder!!.append("ABCDEFGHIJKLMNOPQRST")
        }

    private fun buildFinal() {
        val var1: Byte = 0
        this.sbAl = this.sbAl + 'E'
        this.sbkey = StringBuilder()
        this.sbkey!!.append(this.builder!!.get(this.finalVal))
        var var6 = var1 + 1
        val var2 = this.finalVal % 10
        val var3 = this.finalVal / 10
        var var4 = this.finalVal

        for (var5 in 0..14) {
            if (var6 % 2 != 0) {
                if (var4 + var2 >= this.chArr) {
                    var4 = var2 - (124 - var4)
                } else {
                    var4 += var2
                }

                this.sbkey!!.append(this.builder!!.get(var4))
            } else {
                if (var4 + var3 >= this.chArr) {
                    var4 = var3 - (124 - var4)
                } else {
                    var4 += var3
                }

                this.sbkey!!.append(this.builder!!.get(var4))
            }

            ++var6
        }
    }

    val file: String?
        get() {
            try {
                val var1 = this.fileName
                if (var1 == null || var1.isEmpty()) {
                    return ""
                }

                val absoluteFilePath = this.fileName
                if (absoluteFilePath == null || absoluteFilePath.isEmpty()) {
                    return ""
                }
                this.mFileTemp = File(absoluteFilePath)
                this.fname = this.mFileTemp!!.getName()
                this.fname = this.remove_e(this.fname!!)
                this.outPutPath = this.outPutPath + File.separator + this.fname
                println(" Output file path :  " + this.outPutPath)
                println(" Please wait...")
                this.decryptedFile = File(this.outPutPath)
                if (!this.decryptedFile!!.exists()) {
                    this.decryptedFile!!.createNewFile()
                    Log.d("File created: ", this.decryptedFile!!.absolutePath)
                }

                this.decryptFile(this.sbkey.toString(), this.mFileTemp!!, this.decryptedFile!!)
            } catch (var2: Exception) {
                println("exception: " + var2.message)
            }

            return this.outPutPath
        }

    private fun remove_e(var1: String): String {
        var var1 = var1
        val var2 = var1.lastIndexOf(".")
        val var3 = var1.substring(var2, var1.length)
        var1 = var1.substring(0, var2)
        if (var1.endsWith(this.encSym)) {
            var1 = var1.substring(0, var2 - 2)
        }

        var1 = var1 + var3
        return var1
    }

    private fun decryptFile(var1: String, var2: File, var3: File) {
        this.sbAl = this.sbAl + this.lastInd()

        try {
            decrypt(var1, var2, var3)
        } catch (var5: Exception) {
            println(var5.message)
        }
    }

    private fun lastInd(): Char {
        val var1 = 'S'
        return var1
    }

    companion object {
        private const val TAG = " Decrypt "
        private const val AN_STR1 = "ABCDEFGHIJKLMNOPQRST"
        private const val AN_STR3 = "lkjihgfedcbaZYXWVUTS"
        private const val AN_STR5 = "cdefghijklmnopqrstuv"
        private const val AN_STR7 = "3210"
        private const val METHOD1 = 'A'

        @JvmStatic
        fun main(var0: Array<String>) {
        }

        @Throws(Exception::class)
        fun decrypt(var0: String, var1: File, var2: File) {
            val var4 = Calendar.getInstance().getTimeInMillis()
            SecureRandom()
            val var7 = SecretKeySpec(var0.toByteArray(), "AES")

            val var3: FileInputStream?
            try {
                var3 = FileInputStream(var1)
            } catch (var18: Exception) {
                println("Decrypt  Decrypt " + var18)
                throw var18
            }

            val var8 = ByteArray(16)
            val var9 = IvParameterSpec(var8)
            val var10 = Cipher.getInstance("AES/CTR/NoPadding")
            var10.init(2, var7, var9)
            if (!var2.exists()) {
                val var11 = var2.createNewFile()
            }

            val var12 = ByteArray(8192)
            val var13 = CipherOutputStream(FileOutputStream(var2), var10)

            while (true) {
                val var14 = var3.read(var12)
                if (var14 == -1) {
                    var3.close()
                    var13.close()
                    println(" File Decrypted")
                    val var19 = Calendar.getInstance().getTimeInMillis()
                    val var10000 = var19 - var4
                    return
                }

                var13.write(var12, 0, var14)
            }
        }
    }
}