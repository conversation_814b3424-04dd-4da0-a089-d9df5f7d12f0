package clt.india.classroom.utils

import android.content.Context
import android.graphics.Rect
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

/**
 * Helper class for TV remote focus handling in RecyclerViews
 */
object TvFocusHelper {

    /**
     * Setup TV focus for a RecyclerView item
     */
    fun setupTvFocus(view: View, position: Int) {
        view.apply {
            // Make the view focusable for TV remote
            isFocusable = true
            isClickable = true
            isFocusableInTouchMode = false
            
            // Add focus change listener for visual feedback
            setOnFocusChangeListener { v, hasFocus ->
                Log.i("TvFocusHelper", "Item $position focus changed: $hasFocus")
                
                if (hasFocus) {
                    // Scale up slightly when focused
                    animate()
                        .scaleX(1.05f)
                        .scaleY(1.05f)
                        .setDuration(200)
                        .start()
                    
                    // Ensure the focused item is visible
                    requestFocus()
                    
                } else {
                    // Scale back to normal when not focused
                    animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(200)
                        .start()
                }
            }
            
            // Add key listener for better navigation
            setOnKeyListener { v, keyCode, event ->
                Log.i("TvFocusHelper", "Key pressed on item $position: $keyCode")
                false // Let the system handle navigation
            }
        }
    }

    /**
     * Setup TV focus for a button within a RecyclerView item
     */
    fun setupButtonTvFocus(button: View, position: Int, buttonName: String = "button") {
        button.apply {
            // Make the button focusable for TV remote
            isFocusable = true
            isClickable = true
            isFocusableInTouchMode = false
            
            // Add focus change listener
            setOnFocusChangeListener { v, hasFocus ->
                Log.i("TvFocusHelper", "$buttonName $position focus changed: $hasFocus")
                
                if (hasFocus) {
                    // Add visual feedback when focused
                    animate()
                        .scaleX(1.1f)
                        .scaleY(1.1f)
                        .setDuration(150)
                        .start()
                    
                    // Change elevation for depth effect
                    elevation = 8f
                    
                } else {
                    // Remove visual feedback when not focused
                    animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(150)
                        .start()
                    
                    elevation = 2f
                }
            }
        }
    }

    /**
     * Setup RecyclerView for TV navigation
     */
    fun setupRecyclerViewForTv(recyclerView: RecyclerView) {
        recyclerView.apply {
            // Enable focus for the RecyclerView itself
            isFocusable = true
            isFocusableInTouchMode = false
            
            // Add item decoration for better focus visibility
            addItemDecoration(TvFocusItemDecoration())
            
            // Set focus change listener
            setOnFocusChangeListener { v, hasFocus ->
                Log.i("TvFocusHelper", "RecyclerView focus changed: $hasFocus")
                
                if (hasFocus && childCount > 0) {
                    // Focus the first visible item when RecyclerView gains focus
                    getChildAt(0)?.requestFocus()
                }
            }
        }
    }

    /**
     * Force focus on a specific item in RecyclerView
     */
    fun focusItem(recyclerView: RecyclerView, position: Int) {
        recyclerView.post {
            val viewHolder = recyclerView.findViewHolderForAdapterPosition(position)
            viewHolder?.itemView?.requestFocus()
            
            Log.i("TvFocusHelper", "Forced focus on item $position")
        }
    }

    /**
     * Get the currently focused item position in RecyclerView
     */
    fun getFocusedItemPosition(recyclerView: RecyclerView): Int {
        for (i in 0 until recyclerView.childCount) {
            val child = recyclerView.getChildAt(i)
            if (child.hasFocus() || child.findFocus() != null) {
                return recyclerView.getChildAdapterPosition(child)
            }
        }
        return RecyclerView.NO_POSITION
    }

    /**
     * Navigate to next focusable item
     */
    fun navigateToNextItem(recyclerView: RecyclerView): Boolean {
        val currentPosition = getFocusedItemPosition(recyclerView)
        if (currentPosition != RecyclerView.NO_POSITION) {
            val nextPosition = currentPosition + 1
            if (nextPosition < recyclerView.adapter?.itemCount ?: 0) {
                focusItem(recyclerView, nextPosition)
                return true
            }
        }
        return false
    }

    /**
     * Navigate to previous focusable item
     */
    fun navigateToPreviousItem(recyclerView: RecyclerView): Boolean {
        val currentPosition = getFocusedItemPosition(recyclerView)
        if (currentPosition != RecyclerView.NO_POSITION) {
            val previousPosition = currentPosition - 1
            if (previousPosition >= 0) {
                focusItem(recyclerView, previousPosition)
                return true
            }
        }
        return false
    }
}

/**
 * ItemDecoration to add visual focus indicators
 */
class TvFocusItemDecoration : RecyclerView.ItemDecoration() {
    
    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        
        // Add extra space around items for focus indicators
        outRect.set(4, 4, 4, 4)
    }
}

/**
 * Extension functions for easier TV focus setup
 */
fun View.setupForTvFocus(position: Int = 0) {
    TvFocusHelper.setupTvFocus(this, position)
}

fun View.setupButtonForTvFocus(position: Int = 0, buttonName: String = "button") {
    TvFocusHelper.setupButtonTvFocus(this, position, buttonName)
}

fun RecyclerView.setupForTvNavigation() {
    TvFocusHelper.setupRecyclerViewForTv(this)
}

/**
 * TV Focus debugging utilities
 */
object TvFocusDebugger {

    fun debugFocusState(view: View, tag: String = "TvFocusDebugger") {
        Log.i(tag, """
            Focus Debug for ${view.javaClass.simpleName}:
            - isFocusable: ${view.isFocusable}
            - isFocusableInTouchMode: ${view.isFocusableInTouchMode}
            - isClickable: ${view.isClickable}
            - hasFocus: ${view.hasFocus()}
            - findFocus: ${view.findFocus() != null}
            - visibility: ${view.visibility}
            - isEnabled: ${view.isEnabled}
        """.trimIndent())
    }

    fun debugRecyclerViewFocus(recyclerView: RecyclerView) {
        Log.i("TvFocusDebugger", "=== RecyclerView Focus Debug ===")
        Log.i("TvFocusDebugger", "RecyclerView childCount: ${recyclerView.childCount}")
        Log.i("TvFocusDebugger", "RecyclerView hasFocus: ${recyclerView.hasFocus()}")
        
        for (i in 0 until recyclerView.childCount) {
            val child = recyclerView.getChildAt(i)
            val position = recyclerView.getChildAdapterPosition(child)
            Log.i("TvFocusDebugger", "Child[$i] position=$position hasFocus=${child.hasFocus()}")
            
            // Check if any child views have focus
            val focusedChild = child.findFocus()
            if (focusedChild != null) {
                Log.i("TvFocusDebugger", "  -> Focused child: ${focusedChild.javaClass.simpleName}")
            }
        }
    }

    fun logFocusHierarchy(view: View, depth: Int = 0) {
        val indent = "  ".repeat(depth)
        Log.i("TvFocusDebugger", "$indent${view.javaClass.simpleName} - focusable:${view.isFocusable}, hasFocus:${view.hasFocus()}")
        
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                logFocusHierarchy(view.getChildAt(i), depth + 1)
            }
        }
    }
}
