package clt.india.classroom.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import clt.india.classroom.R

object PrefUtilsManager {

    const val USER_TOKEN = "user_token"
    const val LANG = "language"
    const val GRADE = "grade"




    /**
     * Function to save auth token
     */
    fun saveAuthToken(context: Context, token: String) {
        saveString(context, USER_TOKEN, token)
    }

    /**
     * Function to fetch auth token
     */
    fun getToken(context: Context): String? {
        return getString(context, USER_TOKEN)
    }

    fun saveString(context: Context, key: String, value: String) {
        val prefs: SharedPreferences =
            context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)
        val editor = prefs.edit()
        editor.putString(key, value)
        editor.apply()

    }

    fun getString(context: Context, key: String): String? {
        val prefs: SharedPreferences =
            context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)
        return prefs.getString(this.USER_TOKEN, null)
    }
    fun saveToPrefs(context: Context, key: String?, value: Any) {
        try {
            val prefs: SharedPreferences =
                context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)
            val editor = prefs.edit()
            if (value is Int) {
                editor.putInt(key, value)
            } else if (value is String) {
                editor.putString(key, value.toString())
            } else if (value is Boolean) {
                editor.putBoolean(key, value)
            } else if (value is Long) {
                editor.putLong(key, value)
            } else if (value is Float) {
                editor.putFloat(key, value)
            } else if (value is Double) {
                editor.putLong(key, java.lang.Double.doubleToRawLongBits(value))
            }
            editor.apply()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

//    fun getString(context: Context, key: String): String? {
//        val prefs: SharedPreferences =
//            context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)
//        return prefs.getString(this.USER_TOKEN, null)
//    }

    fun getFromPrefs(context: Context, key: String?, defaultValue: Any): Any? {
        val sharedPrefs: SharedPreferences =
            context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE)
        try {
            if (defaultValue is String) {
                return sharedPrefs.getString(key, defaultValue.toString())
            } else if (defaultValue is Int) {
                return sharedPrefs.getInt(key, defaultValue)
            } else if (defaultValue is Boolean) {
                return sharedPrefs.getBoolean(key, defaultValue)
            } else if (defaultValue is Long) {
                return sharedPrefs.getLong(key, defaultValue)
            } else if (defaultValue is Float) {
                return sharedPrefs.getFloat(key, defaultValue)
            } else if (defaultValue is Double) {
                return java.lang.Double.longBitsToDouble(
                    sharedPrefs.getLong(
                        key, java.lang.Double.doubleToLongBits(
                            defaultValue
                        )
                    )
                )
            }
        } catch (e: java.lang.Exception) {
            Log.e("Execption", e.message!!)
            return defaultValue
        }
        return defaultValue
    }

    fun clearData(context: Context){
        val editor = context.getSharedPreferences(context.getString(R.string.app_name), Context.MODE_PRIVATE).edit()
        editor.clear()
        editor.apply()
    }
}