package clt.india.classroom.utils

import android.util.Log
import clt.india.classroom.data.api.request.Children

object FilterTestVerification {

    /**
     * Test to verify that video and worksheet filters work differently
     */
    fun testFilterDifferences() {
        Log.i("FilterTestVerification", "=== TESTING FILTER DIFFERENCES ===")
        
        // Create sample data that should be filtered differently
        val sampleChildren = createMixedSampleData()
        
        Log.i("FilterTestVerification", "\n--- ORIGINAL DATA ---")
        sampleChildren.forEachIndexed { index, child ->
            Log.i("FilterTestVerification", "Original[$index]: ${child.name}")
        }
        
        // Test video filter
        Log.i("FilterTestVerification", "\n--- VIDEO FILTER RESULTS ---")
        val videoResults = ContentFilterUtils.filterForVideos(
            children = sampleChildren,
            logTag = "FilterTestVerification_Video"
        )
        Log.i("FilterTestVerification", "Video filter returned ${videoResults.size} items:")
        videoResults.forEachIndexed { index, child ->
            Log.i("FilterTestVerification", "Video[$index]: ${child.name}")
        }
        
        // Test worksheet filter
        Log.i("FilterTestVerification", "\n--- WORKSHEET FILTER RESULTS ---")
        val worksheetResults = ContentFilterUtils.filterForWorksheets(
            children = sampleChildren,
            logTag = "FilterTestVerification_Worksheet"
        )
        Log.i("FilterTestVerification", "Worksheet filter returned ${worksheetResults.size} items:")
        worksheetResults.forEachIndexed { index, child ->
            Log.i("FilterTestVerification", "Worksheet[$index]: ${child.name}")
        }
        
        // Verify they are different
        val areResultsDifferent = videoResults.size != worksheetResults.size ||
            videoResults.map { it.name } != worksheetResults.map { it.name }
        
        Log.i("FilterTestVerification", "\n--- VERIFICATION ---")
        Log.i("FilterTestVerification", "Video results count: ${videoResults.size}")
        Log.i("FilterTestVerification", "Worksheet results count: ${worksheetResults.size}")
        Log.i("FilterTestVerification", "Results are different: $areResultsDifferent")
        
        if (!areResultsDifferent) {
            Log.e("FilterTestVerification", "ERROR: Filters are returning the same results!")
        } else {
            Log.i("FilterTestVerification", "SUCCESS: Filters are working correctly!")
        }
        
        Log.i("FilterTestVerification", "=== END FILTER TEST ===")
    }
    
    private fun createMixedSampleData(): ArrayList<Children> {
        return arrayListOf(
            Children().apply { name = "1. Introduction Video.mp4" },
            Children().apply { name = "2. Listening Comprehension - Activity.mp4" },
            Children().apply { name = "3. Reading Exercise.mp4" },
            Children().apply { name = "4. Worksheet - Practice.pdf" },
            Children().apply { name = "5. Worksheet - Assessment.pdf" },
            Children().apply { name = "6. Story Video.mp4" },
            Children().apply { name = "7. Worksheet - Homework.doc" },
            Children().apply { name = "8. Summary Video.mp4" },
            Children().apply { name = "9. Activity Instructions.txt" },
            Children().apply { name = "10. Final Assessment Worksheet.pdf" },
            Children().apply { name = "11. Math Video.mp4" },
            Children().apply { name = "12. Science Worksheet.docx" }
        )
    }
    
    /**
     * Quick test to call from fragments
     */
    fun quickTest(originalChildren: ArrayList<Children>, fragmentName: String) {
        Log.i("FilterTestVerification", "=== QUICK TEST FOR $fragmentName ===")
        Log.i("FilterTestVerification", "Original count: ${originalChildren.size}")
        
        val videoFiltered = ContentFilterUtils.filterForVideos(originalChildren, "QuickTest_Video")
        val worksheetFiltered = ContentFilterUtils.filterForWorksheets(originalChildren, "QuickTest_Worksheet")
        
        Log.i("FilterTestVerification", "Video filtered count: ${videoFiltered.size}")
        Log.i("FilterTestVerification", "Worksheet filtered count: ${worksheetFiltered.size}")
        
        Log.i("FilterTestVerification", "Video items:")
        videoFiltered.forEach { Log.i("FilterTestVerification", "  - ${it.name}") }
        
        Log.i("FilterTestVerification", "Worksheet items:")
        worksheetFiltered.forEach { Log.i("FilterTestVerification", "  - ${it.name}") }
        
        Log.i("FilterTestVerification", "=== END QUICK TEST ===")
    }
}
