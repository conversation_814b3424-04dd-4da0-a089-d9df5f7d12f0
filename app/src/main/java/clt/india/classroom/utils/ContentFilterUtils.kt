package clt.india.classroom.utils

import android.util.Log
import clt.india.classroom.data.api.request.Children

object ContentFilterUtils {

    /**
     * Default exclusion patterns for filtering content
     */
    private val DEFAULT_EXCLUSION_PATTERNS = listOf(
        "listening comprehension - activity.mp4",
        "worksheet",
        "activity.mp4" // You can add more patterns here
    )

    /**
     * Filter children list by removing items that match exclusion patterns
     * @param children Original list of children
     * @param exclusionPatterns List of patterns to exclude (case-insensitive)
     * @param logTag Tag for logging (optional)
     * @return Filtered list of children
     */
    fun filterChildren(
        children: ArrayList<Children>,
        exclusionPatterns: List<String> = DEFAULT_EXCLUSION_PATTERNS,
        logTag: String = "ContentFilterUtils"
    ): ArrayList<Children> {
        
        Log.i(logTag, "Starting content filtering. Original count: ${children.size}")
        Log.i(logTag, "Exclusion patterns: $exclusionPatterns")
        
        val filteredList = ArrayList<Children>()
        
        for (child in children) {
            val childName = child.name?.lowercase() ?: ""
            
            // Check if the item should be excluded
            val matchedPattern = exclusionPatterns.find { pattern ->
                childName.contains(pattern.lowercase())
            }
            
            if (matchedPattern != null) {
                Log.i(logTag, "Excluding: '${child.name}' (matches pattern: '$matchedPattern')")
            } else {
                filteredList.add(child)
                Log.i(logTag, "Including: '${child.name}'")
            }
        }
        
        Log.i(logTag, "Filtering complete. Filtered count: ${filteredList.size}")
        return filteredList
    }

    /**
     * Filter children list using Kotlin's functional approach
     */
    fun filterChildrenFunctional(
        children: ArrayList<Children>,
        exclusionPatterns: List<String> = DEFAULT_EXCLUSION_PATTERNS,
        logTag: String = "ContentFilterUtils"
    ): ArrayList<Children> {
        
        Log.i(logTag, "Starting functional content filtering. Original count: ${children.size}")
        
        return ArrayList(children.filter { child ->
            val childName = child.name?.lowercase() ?: ""
            val shouldInclude = !exclusionPatterns.any { pattern -> 
                childName.contains(pattern.lowercase()) 
            }
            
            if (!shouldInclude) {
                Log.i(logTag, "Excluding: '${child.name}'")
            }
            
            shouldInclude
        })
    }

    /**
     * Filter children by file type
     * @param children Original list of children
     * @param allowedTypes List of allowed file types (e.g., ["mp4", "pdf", "jpg"])
     * @param logTag Tag for logging
     */
    fun filterByFileType(
        children: ArrayList<Children>,
        allowedTypes: List<String>,
        logTag: String = "ContentFilterUtils"
    ): ArrayList<Children> {
        
        Log.i(logTag, "Filtering by file type. Allowed types: $allowedTypes")
        
        return ArrayList(children.filter { child ->
            val fileName = child.name?.lowercase() ?: ""
            val fileExtension = fileName.substringAfterLast(".", "")
            
            val shouldInclude = allowedTypes.any { type -> 
                fileExtension == type.lowercase() 
            }
            
            if (!shouldInclude && fileName.isNotEmpty()) {
                Log.i(logTag, "Excluding: '${child.name}' (file type: '$fileExtension' not in allowed types)")
            }
            
            shouldInclude
        })
    }

    /**
     * Filter children by excluding specific file types
     */
    fun filterExcludeFileTypes(
        children: ArrayList<Children>,
        excludedTypes: List<String>,
        logTag: String = "ContentFilterUtils"
    ): ArrayList<Children> {
        
        Log.i(logTag, "Filtering to exclude file types: $excludedTypes")
        
        return ArrayList(children.filter { child ->
            val fileName = child.name?.lowercase() ?: ""
            val fileExtension = fileName.substringAfterLast(".", "")
            
            val shouldInclude = !excludedTypes.any { type -> 
                fileExtension == type.lowercase() 
            }
            
            if (!shouldInclude) {
                Log.i(logTag, "Excluding: '${child.name}' (excluded file type: '$fileExtension')")
            }
            
            shouldInclude
        })
    }

    /**
     * Custom filter function that allows you to define your own filtering logic
     */
    fun filterCustom(
        children: ArrayList<Children>,
        filterFunction: (Children) -> Boolean,
        logTag: String = "ContentFilterUtils"
    ): ArrayList<Children> {
        
        Log.i(logTag, "Applying custom filter. Original count: ${children.size}")
        
        return ArrayList(children.filter { child ->
            val shouldInclude = filterFunction(child)
            
            if (!shouldInclude) {
                Log.i(logTag, "Excluding: '${child.name}' (custom filter)")
            }
            
            shouldInclude
        })
    }
}
