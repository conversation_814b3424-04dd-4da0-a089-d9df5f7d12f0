package clt.india.classroom.utils

import android.util.Log

object SubjectInitializationTest {

    /**
     * Test to verify subject initialization is working correctly
     */
    fun testSubjectInitialization(
        selectedGrade: String?,
        eglJson: String?,
        selectedSubject: String?,
        subjectChaptersMapSize: Int,
        chaptersCount: Int
    ) {
        Log.i("SubjectInitializationTest", "=== SUBJECT INITIALIZATION TEST ===")
        
        // Test 1: Grade selection
        Log.i("SubjectInitializationTest", "1. Grade Selection Test:")
        Log.i("SubjectInitializationTest", "   Selected Grade: $selectedGrade")
        val gradeTestPassed = !selectedGrade.isNullOrEmpty()
        Log.i("SubjectInitializationTest", "   Grade Test Passed: $gradeTestPassed")
        
        // Test 2: EGL JSON data
        Log.i("SubjectInitializationTest", "2. EGL JSON Data Test:")
        Log.i("SubjectInitializationTest", "   EGL JSON is null: ${eglJson == null}")
        Log.i("SubjectInitializationTest", "   EGL JSON is empty: ${eglJson?.isEmpty() ?: true}")
        Log.i("SubjectInitializationTest", "   EGL JSON length: ${eglJson?.length ?: 0}")
        val jsonTestPassed = !eglJson.isNullOrEmpty() && eglJson != "null"
        Log.i("SubjectInitializationTest", "   JSON Test Passed: $jsonTestPassed")
        
        // Test 3: Subject selection
        Log.i("SubjectInitializationTest", "3. Subject Selection Test:")
        Log.i("SubjectInitializationTest", "   Selected Subject: $selectedSubject")
        val subjectTestPassed = !selectedSubject.isNullOrEmpty()
        Log.i("SubjectInitializationTest", "   Subject Test Passed: $subjectTestPassed")
        
        // Test 4: Subject chapters map
        Log.i("SubjectInitializationTest", "4. Subject Chapters Map Test:")
        Log.i("SubjectInitializationTest", "   Map Size: $subjectChaptersMapSize")
        val mapTestPassed = subjectChaptersMapSize > 0
        Log.i("SubjectInitializationTest", "   Map Test Passed: $mapTestPassed")
        
        // Test 5: Chapters count
        Log.i("SubjectInitializationTest", "5. Chapters Count Test:")
        Log.i("SubjectInitializationTest", "   Chapters Count: $chaptersCount")
        val chaptersTestPassed = chaptersCount > 0
        Log.i("SubjectInitializationTest", "   Chapters Test Passed: $chaptersTestPassed")
        
        // Overall result
        val allTestsPassed = gradeTestPassed && jsonTestPassed && subjectTestPassed && 
                            mapTestPassed && chaptersTestPassed
        
        Log.i("SubjectInitializationTest", "=== TEST RESULTS ===")
        Log.i("SubjectInitializationTest", "Grade Test: ${if (gradeTestPassed) "PASS" else "FAIL"}")
        Log.i("SubjectInitializationTest", "JSON Test: ${if (jsonTestPassed) "PASS" else "FAIL"}")
        Log.i("SubjectInitializationTest", "Subject Test: ${if (subjectTestPassed) "PASS" else "FAIL"}")
        Log.i("SubjectInitializationTest", "Map Test: ${if (mapTestPassed) "PASS" else "FAIL"}")
        Log.i("SubjectInitializationTest", "Chapters Test: ${if (chaptersTestPassed) "PASS" else "FAIL"}")
        Log.i("SubjectInitializationTest", "OVERALL: ${if (allTestsPassed) "PASS" else "FAIL"}")
        
        if (!allTestsPassed) {
            Log.e("SubjectInitializationTest", "INITIALIZATION FAILED - Check the failed tests above")
            
            // Provide specific recommendations
            if (!gradeTestPassed) {
                Log.e("SubjectInitializationTest", "RECOMMENDATION: Check GradePreferenceHelper.getSelection()")
            }
            if (!jsonTestPassed) {
                Log.e("SubjectInitializationTest", "RECOMMENDATION: Check EGL JSON data loading from SharedPreferences")
            }
            if (!subjectTestPassed) {
                Log.e("SubjectInitializationTest", "RECOMMENDATION: Check setupSubjectTabs() method")
            }
            if (!mapTestPassed) {
                Log.e("SubjectInitializationTest", "RECOMMENDATION: Check generateSubjectChaptersMap() method")
            }
            if (!chaptersTestPassed) {
                Log.e("SubjectInitializationTest", "RECOMMENDATION: Check updateChaptersForSubject() method")
            }
        } else {
            Log.i("SubjectInitializationTest", "SUCCESS: All initialization tests passed!")
        }
        
        Log.i("SubjectInitializationTest", "=== END INITIALIZATION TEST ===")
    }
    
    /**
     * Quick diagnostic for common issues
     */
    fun quickDiagnostic(fragmentName: String) {
        Log.i("SubjectInitializationTest", "=== QUICK DIAGNOSTIC FOR $fragmentName ===")
        Log.i("SubjectInitializationTest", "Common issues to check:")
        Log.i("SubjectInitializationTest", "1. Is selectedGrade properly set?")
        Log.i("SubjectInitializationTest", "2. Is EGL JSON data loaded from SharedPreferences?")
        Log.i("SubjectInitializationTest", "3. Is setupSubjectTabs() called after data is loaded?")
        Log.i("SubjectInitializationTest", "4. Is the first subject automatically selected?")
        Log.i("SubjectInitializationTest", "5. Is updateChaptersForSubject() called for the first subject?")
        Log.i("SubjectInitializationTest", "6. Is the chapter adapter properly initialized?")
        Log.i("SubjectInitializationTest", "=== END QUICK DIAGNOSTIC ===")
    }
}
