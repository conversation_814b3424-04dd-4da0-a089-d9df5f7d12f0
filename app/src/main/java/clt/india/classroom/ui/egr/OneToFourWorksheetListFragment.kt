package clt.india.classroom.ui.egr

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.WorksheetListAdapter
import clt.india.classroom.databinding.FragmentOneToFourWorksheetListBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity

class OneToFourWorksheetListFragment : Fragment() {

    private var _binding: FragmentOneToFourWorksheetListBinding? = null
    private val binding get() = _binding!!

    private var selectedChapter: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedChapter = arguments?.getString(ARG_CHAPTER)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentOneToFourWorksheetListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val worksheetTitles = listOf(
            "$selectedChapter - Worksheet 1",
            "$selectedChapter - Worksheet 2",
            "$selectedChapter - Worksheet 3"
        )

        val adapter = WorksheetListAdapter(worksheetTitles) { worksheetTitle ->
            val intent = Intent(requireContext(), VideoPlayingActivity::class.java)
            intent.putExtra("worksheet_title", worksheetTitle)
            startActivity(intent)
        }

        binding.worksheetrecyclerview.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.worksheetrecyclerview.adapter = adapter
        handleclicks()

    }
    private fun handleclicks(){
        binding.backButton.setOnClickListener{
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_CHAPTER = "chapter"

        fun newInstance(chapter: String): OneToFourWorksheetListFragment {
            val fragment = OneToFourWorksheetListFragment()
            val bundle = Bundle()
            bundle.putString(ARG_CHAPTER, chapter)
            fragment.arguments = bundle
            return fragment
        }
    }
}
