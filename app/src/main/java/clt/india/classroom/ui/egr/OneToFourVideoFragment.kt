package clt.india.classroom.ui.egr

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.OneToFourVideoChapterDetailsAdapter
import clt.india.classroom.adapter.VideoListAdapter
import clt.india.classroom.data.api.request.Children
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.data.api.response.egl.EGLData
import clt.india.classroom.databinding.FragmentVideoBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.ContentFilterUtils
import clt.india.classroom.utils.FilterTestVerification
import clt.india.classroom.utils.PrefUtilsManager
import com.google.gson.Gson


class OneToFourVideoFragment : Fragment() {

    private var _binding: FragmentVideoBinding? = null
    private val binding get() = _binding!!

    private lateinit var chapterAdapter: OneToFourVideoChapterDetailsAdapter
    private var selectedSubject: String? = null
    private var chapDetails: String? = null
    private var chapIndex: Int? = null
    private var chapDetailsArray: Array<ChapterNew>? = null
    var eglJson : String? = null
    var chaptersList = mutableListOf<ChapterNew>()
    var childrenOfChap : ArrayList<Children> = arrayListOf()
    var chaptersTitle: ArrayList<String> = arrayListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            chapDetails = it.getString(CHAP_DETAILS)
            chapIndex = it.getInt("pos")

            Log.i("OneToFourVideoFragment", "onCreates:index "+chapIndex)
            Log.i("OneToFourVideoFragment", "onCreates: "+chapDetails)
            Log.i("OneToFourVideoFragment", "onCreates:11 "+selectedSubject)
        }

        Log.i("OneToFourVideoFragment", "onCreates:11. "+selectedSubject)


    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentVideoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        eglJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()
        val map = mutableMapOf<String, List<ChapterNew>>()
        val gson = Gson()
        val egrData = gson.fromJson(eglJson.toString(), EGLData::class.java)

        egrData.usbDriveContents?.forEach { root -> // e.g., "Class 1"
            root.children?.forEach { contentSeries -> // e.g., "Foundational Literacy" (Subject)
                // Process only if the current subject matches the selected subject
                Log.i("OneToFourVideoFragment", "onVewCreated.. "+contentSeries.name +"  "+selectedSubject)

                if (contentSeries.name == "SERIES 1") {
                    val subjectName = contentSeries.name ?: "Unknown Subject"
                    chaptersList = mutableListOf<ChapterNew>()
                    contentSeries.children?.forEachIndexed { index, chapterContent -> // These are the actual chapters
                        // Assuming chapterContent.name is like "Chapter 1 - Title"
                        // You might need to adjust parsing based on your actual data structure for chapter names/titles
                        val chapterName = chapterContent.name ?: "Unnamed Chapter"
                        // For simplicity, let's assume the name is the title and chapter number is part of it or needs extraction

                        chaptersList.add(ChapterNew(chapterName, index.toString(), "No description", chapterContent.children)) // this need to update

                        Log.i("OneToFourVideoFragment", "onViewCreated:in " + chapIndex)
                        if (index == chapIndex) {
                            // Get the original children list
                            val originalChildren = chaptersList.get(chapIndex!!).children

                            Log.i("OneToFourVideoFragment", "=== VIDEO FILTERING DEBUG ===")
                            Log.i("OneToFourVideoFragment", "Original children count: ${originalChildren.size}")
                            originalChildren.forEachIndexed { idx, child ->
                                Log.i("OneToFourVideoFragment", "Original[$idx]: ${child.name}")
                            }

                            // VIDEO FILTER: Exclude worksheets and activities
                            childrenOfChap = ContentFilterUtils.filterForVideos(
                                children = originalChildren,
                                logTag = "OneToFourVideoFragment"
                            )

                            Log.i("OneToFourVideoFragment", "Filtered children count: ${childrenOfChap.size}")
                            childrenOfChap.forEachIndexed { idx, child ->
                                Log.i("OneToFourVideoFragment", "Filtered[$idx]: ${child.name}")
                            }
                            Log.i("OneToFourVideoFragment", "=== END VIDEO FILTERING DEBUG ===")

                            // TEST: Verify filters are working differently
                            FilterTestVerification.quickTest(originalChildren, "OneToFourVideoFragment")

                            // ALTERNATIVE: Smart filter (automatically detects video content)
                            // childrenOfChap = ContentFilterUtils.smartFilter(
                            //     children = originalChildren,
                            //     fragmentType = "video",
                            //     logTag = "OneToFourVideoFragment"
                            // )

                            // METHOD 2: Filter by file type (only videos)
                            // childrenOfChap = ContentFilterUtils.filterByFileType(
                            //     children = originalChildren,
                            //     allowedTypes = listOf("mp4", "avi", "mov"),
                            //     logTag = "OneToFourVideoFragment"
                            // )

                            // METHOD 3: Custom filtering for videos
                            // childrenOfChap = ContentFilterUtils.filterCustom(
                            //     children = originalChildren,
                            //     filterFunction = { child ->
                            //         val name = child.name?.lowercase() ?: ""
                            //         // Include only MP4 files that don't contain "activity" or "worksheet"
                            //         name.endsWith(".mp4") &&
                            //         !name.contains("activity") &&
                            //         !name.contains("worksheet")
                            //     },
                            //     logTag = "OneToFourVideoFragment"
                            // )

                            chaptersTitle.add(chaptersList.get(chapIndex!!).children.toString())

                            Log.i("OneToFourVideoFragment ", "onViewCreated:nn.. " + index + "  " + chapIndex)
                            Log.i("OneToFourVideoFragment ", "onViewCreated:nn.. " + childrenOfChap)
                            Log.i("OneToFourVideoFragment ", "onViewCreated:nn  chaptersTitle " + chaptersTitle)

                        }

                    }
                    map[subjectName] = chaptersList
                }
            }
        }

//        val titles = listOf("FL Video 1.............", "FL Video 2", "FL Video 3")
        val titles = chaptersTitle
        Log.i("OneToFourVideoFragment", "OneToFourVideoFragment: titles "+titles)


        chapterAdapter = OneToFourVideoChapterDetailsAdapter(
            chaptersTitle = childrenOfChap,
            onChapterClick = { chapterName ->

            }
        )

        binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.videoRecyclerView.adapter = chapterAdapter

    }




    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    companion object {
        private const val ARG_SUBJECT = "subject"
        private const val CHAP_DETAILS = "chap_details"

        fun newInstance(subject: String, pos: Int?): OneToFourVideoFragment {
            val fragment = OneToFourVideoFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            if (pos != null) {
                bundle.putInt("pos", pos)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

}