package clt.india.classroom.ui.assesment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.QuestionAdapter
import clt.india.classroom.databinding.FragmentAssesmentViewBinding
import clt.india.classroom.data.api.response.Question
import clt.india.classroom.intrface.OnChapterClickListener
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import clt.india.classroom.viewmodel.AssessmentViewModel


class AssesmentViewFragment : Fragment(), OnChapterClickListener {
    private var _binding: FragmentAssesmentViewBinding? = null
    private val binding get() = _binding!!
    private var selectedGrade: String? = null
    private val vm: AssessmentViewModel by activityViewModels()
    private lateinit var questionAdapter: QuestionAdapter
    private var assessment1Questions = listOf<Question>()
    private var assessment2Questions = listOf<Question>()
    private var currentQuestions = listOf<Question>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
         selectedGrade = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.GRADE) ?: "Grade 1"
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAssesmentViewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (requireActivity() as AppCompatActivity).supportActionBar?.hide()
        binding.gradeText.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        val args = AssesmentViewFragmentArgs.fromBundle(requireArguments())
        val startTab = args.initialTab

        setupSampleData()
        setupTabs(startTab)
        setupRecyclerView(
            if (startTab == 2) assessment2Questions else assessment1Questions
        )
        handleClicks()
    }

    private fun setupSampleData() {
        assessment1Questions = listOf(
            Question(
                1,
                "The method of separating seeds of paddy from their stalks is called?",
                listOf("Threshing", "Winnowing", "Sieving", "Filtration")
            ),
            Question(
                2,
                "The process of heating milk to kill bacteria is known as?",
                listOf("Boiling", "Pasteurization", "Freezing", "Evaporation")
            )
        )

        assessment2Questions = listOf(
            Question(
                1,
                "Which gas is responsible for global warming?",
                listOf("Oxygen", "Carbon Dioxide", "Nitrogen", "Hydrogen")
            ),
            Question(
                2,
                "Which is used to measure temperature?",
                listOf("Barometer", "Thermometer", "Hygrometer", "Speedometer")
            )
        )
    }

    private fun setupRecyclerView(questions: List<Question>) {
        currentQuestions = questions
        questionAdapter = QuestionAdapter(currentQuestions)
        binding.assessmentRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.assessmentRecyclerView.adapter = questionAdapter
    }

    private fun setupTabs(startTab: Int) {
        val tab1 = binding.tabAssessment1
        val tab2 = binding.tabAssessment2

        selectTab(tab1, startTab == 1)
        selectTab(tab2, startTab == 2)

        tab1.setOnClickListener {
            selectTab(tab1, true); selectTab(tab2, false)
            setupRecyclerView(assessment1Questions)
        }
        tab2.setOnClickListener {
            selectTab(tab1, false); selectTab(tab2, true)
            setupRecyclerView(assessment2Questions)
        }
    }

    private fun selectTab(tab: TextView, isSelected: Boolean) {
        if (isSelected) {
            // Selected tab - same style as Assessment 1 in your image
            tab.setBackgroundResource(R.drawable.selected_tab_background)
            tab.setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
        } else {
            // Unselected tab - gray background
            tab.setBackgroundResource(R.drawable.unselected_tab_background)
            tab.setTextColor(ContextCompat.getColor(requireContext(), R.color.black))
        }
    }

    private fun handleClicks() {
        binding.backButton.setOnClickListener { requireActivity().onBackPressedDispatcher.onBackPressed() }
        binding.submitButton.setOnClickListener {
            vm.answeredQuestions = currentQuestions
            findNavController().navigate(R.id.action_assesmentViewFragment_to_assesmentSubmissionFragment)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onChapterClicked(position: Int) {
        findNavController().navigate(R.id.action_assesmentViewFragment_to_assesmentSubmissionFragment)
    }
}
