package clt.india.classroom.ui.assesment

import android.os.Bundle
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupActionBarWithNavController
import clt.india.classroom.R
import clt.india.classroom.intrface.OnAssessmentClickListener

class AssesmentshostActivity : AppCompatActivity(), OnAssessmentClickListener {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_assesmentshost)
        val selectedGrade = intent.getStringExtra("selectedGrade")
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_assesments) as NavHostFragment
        val navController = navHostFragment.navController
        val navInflater = navController.navInflater
        val graph = navInflater.inflate(R.navigation.assesment_nav_graph)
        val bundle = Bundle().apply {
            putString("selectedGrade", selectedGrade)
        }

        navController.setGraph(graph, bundle)

        setupActionBarWithNavController(navController)
    }

    override fun onAssessmentClicked(position: Int) {
        Toast.makeText(
            this,
            "Assessment #${position + 1} clicked",
            Toast.LENGTH_SHORT
        ).show()
    }

    override fun onSupportNavigateUp(): Boolean {
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_assesments) as NavHostFragment
        return navHostFragment.navController.navigateUp() || super.onSupportNavigateUp()
    }
}
