package clt.india.classroom.ui.egr


import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.provider.Settings

import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import clt.india.classroom.databinding.ActivityClassroomLessonsBinding
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.PrefUtilsManager
import org.json.JSONArray
import org.json.JSONObject
import java.io.File

class ClassroomLessonsActivity : AppCompatActivity() {
    private lateinit var storageManager: StorageManager
    private lateinit var binding: ActivityClassroomLessonsBinding
    private lateinit var fi: File

    // Launcher for MANAGE_APP_ALL_FILES_ACCESS_PERMISSION
    private val requestManageAllFilesAccessLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Environment.isExternalStorageManager()) {
                Toast.makeText(this, "All Files Access Granted!", Toast.LENGTH_SHORT).show()
                // Now that permission is granted, try to find and list USB drives
                val usbDrives = findUsbDrives(storageManager)
                if (usbDrives.isNotEmpty()) {
                    listUsbDriveContents(usbDrives.first()) // List the first detected USB drive
                    listUsbDriveCltVideos(usbDrives.first()) // List the first detected USB drive CLT
                } else {
                    Toast.makeText(this, "No USB drives found currently.", Toast.LENGTH_LONG).show()
                }
            } else {
                Toast.makeText(this, "All Files Access Denied!", Toast.LENGTH_LONG).show()
            }
        }
    }


    private val storageVolumeCallback = object : StorageManager.StorageVolumeCallback() {
        override fun onStateChanged(volume: StorageVolume) {
            super.onStateChanged(volume)

            Log.d(
                "USBAccess",
                "Storage volume state changed: ${volume.getDescription(this@ClassroomLessonsActivity)} - ${volume.state}"
            )
            if (volume.isRemovable) { // You might also want to check `volume.state == StorageVolume.STATE_MOUNTED` here
                // This volume is now mounted and ready
                val usbRootPath = volume.directory?.absolutePath
                if (usbRootPath != null) {
                    Log.d("USBAccess", "USB drive mounted at: $usbRootPath")
                    Toast.makeText(
                        this@ClassroomLessonsActivity,
                        "USB drive detected: ${volume.getDescription(this@ClassroomLessonsActivity)}",
                        Toast.LENGTH_LONG
                    ).show()
                    // List contents if MANAGE_EXTERNAL_STORAGE is already granted
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && Environment.isExternalStorageManager()) {
                        listUsbDriveContents(File(usbRootPath))
                        listUsbDriveContents(File(usbRootPath))
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityClassroomLessonsBinding.inflate(layoutInflater)

        setContentView(binding.root)

        storageManager = getSystemService(Context.STORAGE_SERVICE) as StorageManager

        checkAndRequestPermissionsAndScanUsb()
    }

    override fun onStart() {
        super.onStart()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) { // API 24+
            storageManager.registerStorageVolumeCallback(mainExecutor, storageVolumeCallback)
        }
    }

    override fun onStop() {
        super.onStop()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) { // API 24+
            storageManager.unregisterStorageVolumeCallback(storageVolumeCallback)
        }
    }

    private fun checkAndRequestPermissionsAndScanUsb() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) { // Android 11+
            if (!Environment.isExternalStorageManager()) {
                showAllFilesAccessPermissionDialog()
            } else {
                Toast.makeText(this, "All Files Access already granted.", Toast.LENGTH_SHORT).show()
                val usbDrives = findUsbDrives(storageManager)
                if (usbDrives.isNotEmpty()) {
                    listUsbDriveContents(usbDrives.first())
                    listUsbDriveCltVideos(usbDrives.first())
                } else {
                    Toast.makeText(this, "No USB drives found currently.", Toast.LENGTH_LONG).show()
                }
            }
        } else {
            // For Android 10 and below, MANAGE_EXTERNAL_STORAGE doesn't exist in the same way.
            // You'd typically use READ_EXTERNAL_STORAGE and WRITE_EXTERNAL_STORAGE for broad access.
            // However, direct reliable access to USB OTG was always tricky.
            // This example focuses on MANAGE_EXTERNAL_STORAGE for modern Android versions.
            Toast.makeText(this, "MANAGE_EXTERNAL_STORAGE is for Android 11+.", Toast.LENGTH_LONG)
                .show()
            // You might implement checks for READ/WRITE_EXTERNAL_STORAGE here if you support older Android versions.
        }
    }

    private fun showAllFilesAccessPermissionDialog() {
        AlertDialog.Builder(this)
            .setTitle("Permission Required")
            .setMessage("To scan USB drives, this app needs 'All Files Access'. Please grant the permission in settings.")
            .setPositiveButton("Grant") { dialog, which ->
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                val uri = Uri.fromParts("package", packageName, null)
                intent.data = uri
                requestManageAllFilesAccessLauncher.launch(intent)
            }
            .setNegativeButton("Cancel") { dialog, which ->
                Toast.makeText(
                    this,
                    "Permission denied. Cannot scan USB drives.",
                    Toast.LENGTH_LONG
                ).show()
            }
            .show()
    }

    private fun findUsbDrives(storageManager: StorageManager): List<File> {
        val usbRoots = mutableListOf<File>()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) { // API 24+
            val volumes = storageManager.storageVolumes
            for (volume in volumes) {
                // A USB OTG drive will be removable, and typically not the primary emulated storage.
                // Checking for `volume.uuid != null` often helps distinguish actual removable drives.
                if (volume.isRemovable && !volume.isPrimary) {
                    val rootDir = volume.directory // This should be the accessible path
                    if (rootDir != null && rootDir.canRead()) {
                        usbRoots.add(rootDir)
                        Log.d(
                            "USBAccess",
                            "Found USB drive: ${rootDir.absolutePath} (${volume.getDescription(this)})"
                        )
                    } else {
                        Log.w(
                            "USBAccess",
                            "Found USB volume, but cannot read: ${volume.getDescription(this)}"
                        )
                    }
                }
            }
        }
        return usbRoots
    }



    fun listUsbDriveContents(usbRoot: File): JSONObject {
        val rootJson = JSONObject()
        val contentsArray = JSONArray()

        if (!usbRoot.exists() || !usbRoot.isDirectory) {
            Log.e("USBAccess", "USB root is not a valid directory: ${usbRoot.absolutePath}")
            rootJson.put("error", "USB root is not a valid directory")
            return rootJson
        }

        // Assuming permissions are already handled before calling this function

        try {
            val files = usbRoot.listFiles()
            if (files != null) {
                for (file in files) {
                    val fileJson = fileToJson(file) // Create basic JSON for this file/dir

                    if (file.isDirectory && file.name.equals(Constant.EGL_PARENT_NAME)) {
                        val parentEglChildrenArray = JSONArray()
                        val parentEglFile = file.listFiles()

                        if (parentEglFile != null) {
                            for (eglFileItem in parentEglFile) { // Series level
                                val seriesJson = fileToJson(eglFileItem)
                                if (eglFileItem.isDirectory) {
                                    val versionDetailsArray = JSONArray()
                                    val versionDetails = eglFileItem.listFiles()
                                    if (versionDetails != null) {
                                        for (versionWiseChap in versionDetails) { // Version chapter level
                                            val versionChapJson = fileToJson(versionWiseChap)
                                            if (versionWiseChap.isDirectory) {
                                                val chapDetailsArray = JSONArray()
                                                val chapDetails = versionWiseChap.listFiles()
                                                if (chapDetails != null) {
                                                    for (chapDetail in chapDetails) { // Chapter detail level
                                                        val chapDetailJson = fileToJson(chapDetail)
                                                        // If chapDetail can also be a directory and have children,
                                                        // you'd need another nested call here.
                                                        // For now, assuming chapDetail is the last level of nesting for EGL.
                                                        chapDetailsArray.put(chapDetailJson)
                                                        Log.d(
                                                        "child level 3.... ( chapter details) ",
                                                        "  - ${chapDetail.name} ${if (chapDetail.isDirectory) "(DIR)" else "(FILE)"} Size: ${chapDetail.length()} bytes"
                                                    )
                                                    }
                                                }
                                                if (chapDetailsArray.length() > 0) {
                                                    versionChapJson.put("children", chapDetailsArray)
                                                }
                                            }
                                            versionDetailsArray.put(versionChapJson)
                                        }
                                    }
                                    if (versionDetailsArray.length() > 0) {
                                        seriesJson.put("children", versionDetailsArray)
                                    }
                                }
                                parentEglChildrenArray.put(seriesJson)
                            }
                        }
                        if (parentEglChildrenArray.length() > 0) {
                            fileJson.put("children", parentEglChildrenArray)
                        }
                        // Add any other top-level file/directory to the main contents array
                        contentsArray.put(fileJson)
                    }

                }
            } else {
                Log.w("USBAccess", "No files found or unable to list files in: ${usbRoot.absolutePath}")
                rootJson.put("warning", "No files found or unable to list files.")
            }
        } catch (e: Exception) {
            Log.e("USBAccess", "Error generating JSON for USB drive contents: ${e.message}", e)
            rootJson.put("error", "Error generating JSON: ${e.message}")
        }

        rootJson.put("usbDriveContents", contentsArray)
        Log.i("json data : ", "listUsbDriveContents: ${rootJson}")
        Log.i("json data1 : ", "listUsbDriveContents: ${rootJson.toString()}")
        PrefUtilsManager.saveToPrefs(this, Constant.EGL_COMPLETE_JSON, rootJson.toString())
        Log.i("json data2 : ", "listUsbDriveContents: ${PrefUtilsManager.getFromPrefs(this, Constant.EGL_COMPLETE_JSON, "")}")

        AnalyticsUtils.logLargeString(rootJson.toString())
        return rootJson
    }

    fun listUsbDriveCltVideos(usbRoot: File): JSONObject {
        val rootJson = JSONObject()
        val contentsArray = JSONArray()

        if (!usbRoot.exists() || !usbRoot.isDirectory) {
            Log.e("USBAccess", "USB root is not a valid directory: ${usbRoot.absolutePath}")
            rootJson.put("error", "USB root is not a valid directory")
            return rootJson
        }

        try {
            val files = usbRoot.listFiles()
            if (files != null) {
                for (file in files) {
                    if (file.isDirectory && file.name.equals(Constant.CLTVIDEO)) {
                        val cltVideosJson = fileToJson(file)
                        val stateSyllabusArray = processDirectory(file) // Process "CLTVIDEOS"
                        if (stateSyllabusArray.length() > 0) {
                            cltVideosJson.put("children", stateSyllabusArray)
                        }
                        contentsArray.put(cltVideosJson)
                    }
                }
            } else {
                Log.w("USBAccess", "No files found or unable to list files in: ${usbRoot.absolutePath}")
                rootJson.put("warning", "No files found or unable to list files.")
            }
        } catch (e: Exception) {
            Log.e("USBAccess", "Error generating JSON for CLTVIDEOS: ${e.message}", e)
            rootJson.put("error", "Error generating JSON for CLTVIDEOS: ${e.message}")
        }

        rootJson.put("usbDriveContents", contentsArray)
        PrefUtilsManager.saveToPrefs(this, Constant.CLTVIDEO, rootJson.toString())

        Log.i("CLTVIDEOS JSON", "listUsbDriveCltVideos:.. ${rootJson.toString()}")
        // PrefUtilsManager.saveToPrefs(this, "CLTVIDEOS_JSON", rootJson.toString()) // Save if needed
        AnalyticsUtils.logLargeString("CLTVIDEOS_JSON: " + rootJson.toString())
        return rootJson
    }

    // Recursive function to process directories and their children
    private fun processDirectory(directory: File): JSONArray {
        val childrenArray = JSONArray()
        val items = directory.listFiles()
        if (items != null) {
            for (item in items) {
                val itemJson = fileToJson(item)
                if (item.isDirectory) {
                    val subChildrenArray = processDirectory(item) // Recursively process subdirectories
                    if (subChildrenArray.length() > 0) {
                        itemJson.put("children", subChildrenArray)
                    }
                }
                childrenArray.put(itemJson)
            }
        }
        return childrenArray
    }
    // Function to create a JSON representation of a file/directory
    fun fileToJson(file: File): JSONObject {
        val fileJson = JSONObject()
        fileJson.put("name", file.name)
        fileJson.put("absolute_path", file.absolutePath)
        fileJson.put("type", if (file.isDirectory) "DIR" else "FILE")
        fileJson.put("size", file.length())
        // Add path if needed: fileJson.put("path", file.absolutePath)
        return fileJson
    }

}