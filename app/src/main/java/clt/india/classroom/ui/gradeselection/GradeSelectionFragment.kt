package clt.india.classroom.ui.gradeselection

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import clt.india.classroom.R
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.Constant
import org.json.JSONObject
import clt.india.classroom.databinding.FragmentGradeSelectionBinding
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.viewmodel.GradeSelectionViewModel

// Dual-tab fragment for selecting either classroom grades (1-10) or digital literacy content (language + version selection)

class GradeSelectionFragment : Fragment() {

    private var _binding: FragmentGradeSelectionBinding? = null
    private val binding get() = _binding!!
    private lateinit var navController: NavController
    private var isLanguageStep = true
    private lateinit var gradeSelectionViewModel: GradeSelectionViewModel
    private val languageList = listOf("English", "Kannada", "Hindi", "Marathi", "Tamil")
    private val gradeList = listOf(
        "Grade 1",
        "Grade 2",
        "Grade 3",
        "Grade 4",
        "Grade 5",
        "Grade 6",
        "Grade 7",
        "Grade 8",
        "Grade 9",
        "Grade 10"
    )
    private var versionList = listOf<String>()

    private var isClassroomSelected = true

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentGradeSelectionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        navController = findNavController()
        gradeSelectionViewModel = ViewModelProvider(this)[GradeSelectionViewModel::class.java]
        val startTab = arguments?.getString("startTab") ?: "classroom"
        isClassroomSelected = startTab == "classroom"
        setupTabLayout()
        handleclickevents()
        setupGradeOptions()
    }

    private fun setupTabLayout() {
        binding.tabClassroom.setOnClickListener {
            if (!isClassroomSelected) {
                isClassroomSelected = true
                isLanguageStep = true
                selectTab()
                setupGradeOptions()
            }
        }

        binding.tabDigital.setOnClickListener {
            if (isClassroomSelected) {
                isClassroomSelected = false
                isLanguageStep = true
                selectTab()
                setupGradeOptions()
            }
        }

        selectTab()
    }


    private fun selectTab() {
        if (isClassroomSelected) {
            binding.tabClassroom.setBackgroundResource(R.drawable.selected_tab_background)
            binding.tabClassroom.setTextColor(resources.getColor(R.color.white))
            binding.tabDigital.setBackgroundResource(R.drawable.unselected_tab_background)
            binding.tabDigital.setTextColor(resources.getColor(R.color.black))
        } else {
            binding.tabDigital.setBackgroundResource(R.drawable.selected_tab_background)
            binding.tabDigital.setTextColor(resources.getColor(R.color.white))
            binding.tabClassroom.setBackgroundResource(R.drawable.unselected_tab_background)
            binding.tabClassroom.setTextColor(resources.getColor(R.color.black))
        }
    }

    private fun setupGradeOptions() {
        val items = if (isClassroomSelected) {
            gradeList
        } else {
            if (isLanguageStep) {
                val langList = mutableListOf<String>()
                val jsonString = PrefUtilsManager.getFromPrefs(requireContext(), Constant.DLP_LANGUAGES_KEY, "") as? String
                if (!jsonString.isNullOrEmpty()) {
                    val dlpJson = JSONObject(jsonString)
                    val langArray = dlpJson.getJSONArray("languages")
                    for (i in 0 until langArray.length()) {
                        val langObj = langArray.getJSONObject(i)
                        langList.add(langObj.getString("name"))
                    }
                }
                langList
            } else versionList
        }

        val defaultItem = items.first()
        binding.gradeText.text = defaultItem

        if (isClassroomSelected) {
            val gradeNum = defaultItem.substringAfter("Grade ").toIntOrNull() ?: 1
            gradeSelectionViewModel.selectGrade(gradeNum.toString())
            GradePreferenceHelper.saveSelection(requireContext(), gradeNum.toString(), GradePreferenceHelper.SelectionType.GRADE)
        } else {
            // For digital literacy, the defaultItem is either language or version
            // We don't need to extract a number here, just use the string.
            gradeSelectionViewModel.selectGrade(defaultItem)

            if (isClassroomSelected) {
                GradePreferenceHelper.saveSelection(requireContext(), defaultItem, GradePreferenceHelper.SelectionType.GRADE)
            } else {
                if (isLanguageStep) {
                    GradePreferenceHelper.saveSelection(requireContext(), defaultItem, GradePreferenceHelper.SelectionType.LANGUAGE)
                } else {
                    GradePreferenceHelper.saveSelection(requireContext(), defaultItem, GradePreferenceHelper.SelectionType.VERSION)
                }
            }
        }

        val dropdownClickListener = View.OnClickListener {
            val popupMenu = PopupMenu(requireContext(), binding.gradeText)
            items.forEach { item ->
                popupMenu.menu.add(item)
            }

            popupMenu.setOnMenuItemClickListener { menuItem ->
                val selectedItem = menuItem.title.toString()
                binding.gradeText.text = selectedItem

                if (isClassroomSelected) {
                    val gradeNum = selectedItem.substringAfter("Grade ").toIntOrNull() ?: 1
                    gradeSelectionViewModel.selectGrade(gradeNum.toString())
                    GradePreferenceHelper.saveSelection(requireContext(), gradeNum.toString(), GradePreferenceHelper.SelectionType.GRADE)
                } else {
                    // For digital literacy, the selectedItem is either language or version
                    // We don't need to extract a number here, just use the string.
                    gradeSelectionViewModel.selectGrade(selectedItem)
                    GradePreferenceHelper.saveSelection(requireContext(), selectedItem, GradePreferenceHelper.SelectionType.VERSION)
                }
                true
            }
            popupMenu.show()
        }

        binding.gradeText.setOnClickListener(dropdownClickListener)
        binding.dropdownIcon.setOnClickListener(dropdownClickListener)

        if (isClassroomSelected) {
            binding.selectText.text = getString(R.string.select_grade)
            binding.submitLabel.text = "Next"
            binding.submit.visibility = View.VISIBLE
        } else {
            if (isLanguageStep) {
                binding.selectText.text = "Select Language"
                binding.submitLabel.text = "Next"
                binding.submit.visibility = View.VISIBLE
            } else {
                binding.selectText.text = "Select Version"
                binding.submitLabel.text = "Start"
                binding.submit.visibility = View.VISIBLE
            }
        }
    }



    private fun handleclickevents() {
        binding.submit.setOnClickListener {
            val selectedGrade = gradeSelectionViewModel.selectedGrade.value ?: gradeList.first()

            if (isClassroomSelected) {
                val gradeNum = selectedGrade.substringAfter("Grade ").toIntOrNull() ?: 1
                val action = if (gradeNum in 1..4) {
                    GradeSelectionFragmentDirections.actionGradeSelectionFragmentToOneToFourIndexPageFragment()
                } else {
                    GradeSelectionFragmentDirections.actionGradeSelectionFragmentToIndexPageFragment()
                }
                navController.navigate(action)
            } else {
                if (isLanguageStep) {
                    val selectedLang = gradeSelectionViewModel.selectedGrade.value ?: return@setOnClickListener
                    PrefUtilsManager.saveToPrefs(requireContext(), Constant.DLP_SELECTED_LANGUAGE, selectedLang)
                    val jsonString = PrefUtilsManager.getFromPrefs(requireContext(), Constant.DLP_LANGUAGES_KEY, "") as? String
                    val versionListFromJson = mutableListOf<String>()

                    if (!jsonString.isNullOrEmpty()) {
                        val dlpJson = JSONObject(jsonString)
                        val langArray = dlpJson.getJSONArray("languages")
                        for (i in 0 until langArray.length()) {
                            val langObj = langArray.getJSONObject(i)
                            if (langObj.getString("name") == selectedLang) {
                                val versions = langObj.getJSONArray("versions")
                                for (j in 0 until versions.length()) {
                                    versionListFromJson.add(versions.getJSONObject(j).getString("name"))
                                }
                                break
                            }
                        }
                    }

                    if (versionListFromJson.isNotEmpty()) {
                        isLanguageStep = false
                        versionList = versionListFromJson
                        setupGradeOptions()
                    } else {
                        Toast.makeText(requireContext(), "No versions found for $selectedLang", Toast.LENGTH_SHORT).show()
                    }

                } else {
                    val selectedVersion = gradeSelectionViewModel.selectedGrade.value ?: ""
                    PrefUtilsManager.saveToPrefs(requireContext(), Constant.DLP_SELECTED_VERSION, selectedVersion)
                    val action = GradeSelectionFragmentDirections
                        .actionGradeSelectionFragmentToDigitalLiteracyProgramFragment()
                    navController.navigate(action)
                }
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

