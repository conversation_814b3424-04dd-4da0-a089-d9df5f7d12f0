package clt.india.classroom.ui.resources

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import clt.india.classroom.databinding.ActivityTextBookViewBinding


class TextBookViewActivity : AppCompatActivity() {
    private lateinit var binding: ActivityTextBookViewBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTextBookViewBinding.inflate(layoutInflater)
        setContentView(binding.root)
        supportActionBar?.hide()
        loadPdf()
//        handleclicks()

    }
    private fun loadPdf() {
        binding.pdfView.fromAsset("ved_removed.pdf")
            .enableSwipe(true)
            .swipeHorizontal(false)
            .enableDoubletap(true)
            .load()
    }

//    private fun handleclicks() {
//        binding.backButton.setOnClickListener {
//            onBackPressedDispatcher.onBackPressed()
//        }
//    }
}