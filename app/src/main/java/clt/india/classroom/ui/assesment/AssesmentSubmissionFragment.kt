package clt.india.classroom.ui.assesment

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import clt.india.classroom.R
import clt.india.classroom.databinding.FragmentAssesmentSubmissionBinding
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils

class AssesmentSubmissionFragment : Fragment() {
    private var _binding: FragmentAssesmentSubmissionBinding? = null
    private val binding get() = _binding!!

    private var selectedGrade: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedGrade = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.GRADE)
            ?: "Grade 1"

    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentAssesmentSubmissionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleClicks()
        binding.gradeText.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
    }

    private fun handleClicks() {
        binding.backButton.setOnClickListener {
            findNavController().navigateUp()
        }
        binding.submitButton.setOnClickListener {
            findNavController().navigate(R.id.action_assesmentSubmissionFragment_to_assessmentResultsFragment)
        }
        binding.continuebutton.setOnClickListener{
            val action = AssesmentSubmissionFragmentDirections
                .actionAssesmentSubmissionFragmentToAssesmentViewFragment(initialTab = 2)
            findNavController().navigate(action)

        }

    }

}