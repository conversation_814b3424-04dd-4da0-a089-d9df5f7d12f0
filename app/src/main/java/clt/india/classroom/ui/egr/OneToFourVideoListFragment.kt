package clt.india.classroom.ui.egr

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.VideoListAdapter
import clt.india.classroom.databinding.FragmentOneToFourVideoListBinding
import clt.india.classroom.ui.resources.VideoPlayingActivity

class OneToFourVideoListFragment : Fragment() {

    private var _binding: FragmentOneToFourVideoListBinding? = null
    private val binding get() = _binding!!

    private var selectedChapter: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedChapter = arguments?.getString(ARG_CHAPTER)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentOneToFourVideoListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val videoTitles = listOf(
            "$selectedChapter - Video 1",
            "$selectedChapter - Video 2",
            "$selectedChapter - Video 3"
        )

        val adapter = VideoListAdapter(videoTitles) { videoTitle ->
            val intent = Intent(requireContext(), VideoPlayingActivity::class.java)
            intent.putExtra("video_title", videoTitle)
            startActivity(intent)
        }
        binding.videoListRecyclerView.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.videoListRecyclerView.adapter = adapter
        handleclicks()

    }
    private fun handleclicks(){
        binding.backButton.setOnClickListener{
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_CHAPTER = "chapter"

        fun newInstance(chapter: String): OneToFourVideoListFragment {
            val fragment = OneToFourVideoListFragment()
            val bundle = Bundle()
            bundle.putString(ARG_CHAPTER, chapter)
            fragment.arguments = bundle
            return fragment
        }
    }
}
