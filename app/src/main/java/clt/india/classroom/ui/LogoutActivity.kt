package clt.india.classroom.ui

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import clt.india.classroom.databinding.ActivityLogoutBinding
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.PrefUtilsManager

class LogoutActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLogoutBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLogoutBinding.inflate(layoutInflater)
        val view = binding.root
        setContentView(view)

        // Track screen view
        AnalyticsUtils.logScreenView("LogoutActivity")

        binding.btnLogout.setOnClickListener {
            // Track logout event
            AnalyticsUtils.logCustomEvent("logout_clicked")

            PrefUtilsManager.clearData(this)
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)
            startActivity(intent)
        }
    }
}