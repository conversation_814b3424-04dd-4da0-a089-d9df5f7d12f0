package clt.india.classroom.ui.gradeselection

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.ChapterAdapter
import clt.india.classroom.adapter.SubjectTabAdapter
import clt.india.classroom.adapter.SubjectTabAdapter5To10
import clt.india.classroom.adapter.SubjectTabAdapterNew
import clt.india.classroom.databinding.FragmentIndexPageBinding
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.data.api.response.cltvideos.Root
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.PrefUtilsManager.LANG
import com.google.gson.Gson
import okhttp3.internal.notify
import kotlin.collections.set
import kotlin.toString


class IndexPageFragment : Fragment() {

    private var _binding: FragmentIndexPageBinding? = null
    private val binding get() = _binding!!
    private var selectedSubject: String? = null
    private var selectedGrade: String? = null
    private var cltVideos: String? = null
    private var selectedLang: String? = null
    private var selectedSubjectPosition: Int? = 0
    private var subjectNames = mutableListOf<String>()
    private var chaptersList = mutableListOf<Child>()
    private lateinit var chapterAdapter: ChapterAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedGrade = GradePreferenceHelper.getSelection(
            requireContext(), GradePreferenceHelper.SelectionType.GRADE
        ) ?: "Grade 1"
        selectedLang = (PrefUtilsManager.getFromPrefs(requireActivity(), LANG, "English")
            ?: "English").toString()
        cltVideos =
            PrefUtilsManager.getFromPrefs(requireActivity(), Constant.CLTVIDEO, "").toString()

        Log.i("IndexPageFragment", "onCreate: " + cltVideos)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentIndexPageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.gradeSelect.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        setupSubjectTabs()
        setupChapterRecyclerView()
        handleClicks()
    }

    private fun setupSubjectTabs() {
        generateSubjectChaptersMap()

        val subjects = subjectNames

        // Initialize selectedSubjectPosition if not set
        if (selectedSubjectPosition == null && subjectNames.isNotEmpty()) {
            // Find the position of the currently selected subject
            val position = subjectNames.indexOf(selectedSubject)
            selectedSubjectPosition = if (position >= 0) position else 0
            Log.i("IndexPageFragment", "setupSubjectTabs: Initialized selectedSubjectPosition to $selectedSubjectPosition")
        }

//        val adapter = SubjectTabAdapter(subjects) { subject ->
//            selectedSubject = subject
//            val chapters = subjectChaptersMap[subject] ?: emptyList()
//            chapterAdapter.updateChapters(chapters)
//        }


        if (subjectNames.isNotEmpty()) {
            val adapter = SubjectTabAdapter5To10(subjectNames, selectedSubjectPosition ?: 0) { subject ->
                // Use centralized method for subject selection
                onSubjectSelected(subject)
            }

            // Set the OnItemClickListener
            adapter.setOnItemClickListener(object : SubjectTabAdapter5To10.OnItemClickListener {
                override fun onItemClick(position: Int) {
                    Log.d("IndexPageFragment", "Tab clicked via listener at position: $position")
                    if (position < subjects.size) {
                        val subject = subjects[position]
                        // Use centralized method for subject selection
                        onSubjectSelected(subject, position)
                    }
                }
            })
            // Pass selectedSubjectPosition to the adapter

            binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            binding.subjectTabsRecyclerView.adapter = adapter
        }
//        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext())
//        binding.subjectTabsRecyclerView.adapter = adapter
    }

    /**
     * Centralized method to handle subject selection
     * This ensures selectedSubjectPosition is always updated correctly
     */
    private fun onSubjectSelected(subject: String, position: Int? = null) {
        Log.i("IndexPageFragment", "onSubjectSelected: subject = $subject, position = $position")

        // Update selected subject
        selectedSubject = subject

        // Update selected position
        val newPosition = position ?: subjectNames.indexOf(subject)
        if (newPosition >= 0 && newPosition < subjectNames.size) {
            selectedSubjectPosition = newPosition
            Log.i("IndexPageFragment", "onSubjectSelected: Updated selectedSubjectPosition to $selectedSubjectPosition")
        } else {
            Log.w("IndexPageFragment", "onSubjectSelected: Invalid position $newPosition for subject $subject")
        }

        // Update chapters for the new subject
        updateChaptersForSubject(subject)

        // Update the chapter adapter's selected position
        if (::chapterAdapter.isInitialized) {
            chapterAdapter.updateSelectedPosition(selectedSubjectPosition ?: 0)
        }

        // Log the final state
        Log.i("IndexPageFragment", "onSubjectSelected: Final state - subject: $selectedSubject, position: $selectedSubjectPosition")
    }

    /**
     * Public method to select a subject by position
     * Useful for external calls or programmatic selection
     */
    fun selectSubjectByPosition(position: Int) {
        Log.i("IndexPageFragment", "selectSubjectByPosition: $position")

        if (position >= 0 && position < subjectNames.size) {
            val subject = subjectNames[position]
            onSubjectSelected(subject, position)
        } else {
            Log.e("IndexPageFragment", "selectSubjectByPosition: Invalid position $position. Available subjects: ${subjectNames.size}")
        }
    }

    /**
     * Public method to select a subject by name
     * Useful for external calls or programmatic selection
     */
    fun selectSubjectByName(subjectName: String) {
        Log.i("IndexPageFragment", "selectSubjectByName: $subjectName")

        val position = subjectNames.indexOf(subjectName)
        if (position >= 0) {
            onSubjectSelected(subjectName, position)
        } else {
            Log.e("IndexPageFragment", "selectSubjectByName: Subject '$subjectName' not found in $subjectNames")
        }
    }

    /**
     * Get current selected subject information
     */
    fun getCurrentSelection(): Pair<String?, Int?> {
        return Pair(selectedSubject, selectedSubjectPosition)
    }

    private fun filterChapters(query: String) {
        val chapters = subjectChaptersMap[selectedSubject ?: "English Grammar"] ?: emptyList()
        val filtered = chapters.filter {
            (it.name?.contains(query, ignoreCase = true) == true)
        }

        chapterAdapter.updateChapters(filtered)
    }

    private fun handleClicks() {
        binding.searchVideo.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {
                val query = binding.searchVideo.text.toString().trim()
                filterChapters(query)
                hideKeyboard()
                true
            } else {
                false
            }
        }
        binding.changeGrade.setOnClickListener {
            findNavController().navigateUp()
        }
        binding.searchFun.setOnClickListener {
            val query = binding.searchVideo.text.toString().trim()
            filterChapters(query)
            hideKeyboard()
        }
    }

    private fun hideKeyboard() {
        val imm =
            requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchVideo.windowToken, 0)
    }

    private fun setupChapterRecyclerView() {
        Log.i("IndexPageFragment", "setupChapterRecyclerView:ind1 " + selectedSubjectPosition)
        val defaultSubject = selectedSubject ?: "English Grammar"
        val chapters = subjectChaptersMap[defaultSubject] ?: emptyList()
        chapterAdapter = ChapterAdapter(chapters, selectedGrade, subjectNames,selectedSubjectPosition)
        binding.chaptersRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.chaptersRecyclerView.adapter = chapterAdapter

        // Load chapters for the default or initially selected subject
        updateChaptersForSubject(defaultSubject.toString())
    }

    private fun updateChaptersForSubject(subjectName: String) {
        val chapters = subjectChaptersMap[subjectName] ?: emptyList()
        // Clear the previous list of chapters and add new ones
        chaptersList.clear()
        chaptersList.addAll(chapters)
        chapterAdapter.updateChapters(chapters)
    }

    private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
//    C:\Users\<USER>\adit\clt_classroom_backup_23_may_2025\CLTVIDEOS\Karnataka State Syllabus\English\5\English Grammar\Articles
        val gson = Gson()
        // Parse the top-level structure which is a list of UsbDriveContent items
        val cltVideos = gson.fromJson(cltVideos.toString(), Root::class.java)
        subjectNames = mutableListOf<String>()
        for (usbDriveContent in cltVideos.usbDriveContents) {
            for (gradeLevel in usbDriveContent.children) {
                for ((index, subject) in gradeLevel.children.withIndex()) {
                    // Ensure subject has children and index is valid before accessing
//                    Log.i("IndexPageFragment", "getGrade!!!! " + subject.children[index].name)
                    Log.i("IndexPageFragment", "getSub..... " + gradeLevel.children[index].name)
                    Log.i("IndexPageFragment", "getSub.. " + selectedLang)
                    if (gradeLevel.children[index].name == selectedLang) {

                        for ((topicIndex, topic) in subject.children.withIndex()) {
                            Log.i("IndexPageFragment", "getGrade..... " + gradeLevel.children[index].name)
                            Log.i("IndexPageFragment", "getGrade.. " + selectedLang)
                            if (subject.children[topicIndex].name == selectedGrade) {
                                Log.i("IndexPageFragment", "getGrade!!..... " + gradeLevel.children[index].name)
                                Log.i("IndexPageFragment", "getGrade!!!!.. " + selectedLang)
                                for (subTopic in topic.children) {
                                    val testVal = subTopic.name
                                    subjectNames.add(subTopic.name)
                                    Log.i("IndexPageFragment", "getSubject:.. " + testVal)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent -> // These are the actual chapters
                                        // Assuming chapterContent.name is like "Chapter 1 - Title"
                                        // You might need to adjust parsing based on your actual data structure for chapter names/titles
//                                            if (testVal == selectedSubject) {
//                                                val chapterName =
//                                                chapterContent.name ?: "Unnamed Chapter"
//                                            val type = chapterContent.type ?: ""
//                                            val size = chapterContent.size ?: 0
//                                            val absolute_path = chapterContent.absolute_path ?: ""
//                                            Log.i(
//                                                "IndexPageFragment",
//                                                "getSubject:#### " + chapterName
//                                            )
//                                            Log.i(
//                                                "IndexPageFragment",
//                                                "getSubject:##### " + selectedSubject
//                                            )
                                        // Assuming chapterContent.children contains the list of actual chapter Child objects
//                                            if (chapterName == selectedSubject) {
                                        chapterContent.children?.let {
                                            for ((chapterIndex, chapter) in it.withIndex()) {

                                                if (chapter.type == "DIR") {
                                                    currentSubjectChapters.add(chapter)
                                                }
                                                Log.i(
                                                    "IndexPageFragment",
                                                    "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                )
                                            }

                                        }
//                                            }
//                                        }
                                    }
                                    Log.i("IndexPageFragment", "getSubject:@@ " + chaptersList)
//                                    map[subTopic.name] = chaptersList
                                    // Store the chapters specifically for this subject
                                    map[subTopic.name] = currentSubjectChapters
                                }
                            }


                        }
                    }
                }
            }
        }
        AnalyticsUtils.logLargeString(map.toString())
        return map
    }
    private val subjectChaptersMap: Map<String, List<Child>> by lazy { generateSubjectChaptersMap() }

}
