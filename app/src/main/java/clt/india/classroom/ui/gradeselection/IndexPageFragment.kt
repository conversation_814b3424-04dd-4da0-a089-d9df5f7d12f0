package clt.india.classroom.ui.gradeselection

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.ChapterAdapter
import clt.india.classroom.adapter.SubjectTabAdapter
import clt.india.classroom.adapter.SubjectTabAdapter5To10
import clt.india.classroom.adapter.SubjectTabAdapterNew
import clt.india.classroom.databinding.FragmentIndexPageBinding
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.data.api.response.cltvideos.Root
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.PrefUtilsManager.LANG
import clt.india.classroom.utils.TvFocusHelper
import com.google.gson.Gson
import okhttp3.internal.notify
import kotlin.collections.set
import kotlin.toString


class IndexPageFragment : Fragment() {

    private var _binding: FragmentIndexPageBinding? = null
    private val binding get() = _binding!!
    private var selectedSubject: String? = "English Grammar"
    private var selectedGrade: String? = null
    private var cltVideos: String? = null
    private var selectedLang: String? = null
    private var selectedSubjectPosition: Int? = 0
    private var subjectNames = mutableListOf<String>()
    private var chaptersList = mutableListOf<Child>()
    private lateinit var chapterAdapter: ChapterAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedGrade = GradePreferenceHelper.getSelection(
            requireContext(), GradePreferenceHelper.SelectionType.GRADE
        ) ?: "Grade 1"
        selectedLang = (PrefUtilsManager.getFromPrefs(requireActivity(), LANG, "English")
            ?: "English").toString()
        cltVideos =
            PrefUtilsManager.getFromPrefs(requireActivity(), Constant.CLTVIDEO, "").toString()

        Log.i("IndexPageFragment", "onCreate: " + cltVideos)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentIndexPageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.gradeSelect.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        setupSubjectTabs()
        setupChapterRecyclerView()
        handleClicks()
    }

    private fun setupSubjectTabs() {
        generateSubjectChaptersMap()

        val subjects = subjectNames

//        val adapter = SubjectTabAdapter(subjects) { subject ->
//            selectedSubject = subject
//            val chapters = subjectChaptersMap[subject] ?: emptyList()
//            chapterAdapter.updateChapters(chapters)
//        }


        if (subjectNames.isNotEmpty()) {
            val adapter = SubjectTabAdapter5To10(subjectNames, selectedSubjectPosition ?: 0) { subject ->
                selectedSubjectPosition = subjectNames.indexOf(subject)
                selectedSubject = subject
                updateChaptersForSubject(subject)
            }

            // Set the OnItemClickListener
            adapter.setOnItemClickListener(object : SubjectTabAdapter5To10.OnItemClickListener {
                override fun onItemClick(position: Int) {
                    Log.d("TabAdapter", "Tab clicked via listener at position: $position")
                    val subject = subjects[position]
                    selectedSubjectPosition = position

                    // Handle the click - navigate to subject details, update UI, etc.
                    chapterAdapter.updateSelectedPosition(selectedSubjectPosition?:0)
//                    handleSubjectSelection(subject, position)
                }
            })
            // Pass selectedSubjectPosition to the adapter

            binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            binding.subjectTabsRecyclerView.adapter = adapter
        }
//        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext())
//        binding.subjectTabsRecyclerView.adapter = adapter
    }

    private fun filterChapters(query: String) {
        Log.i("IndexPageFragment", "filterChapters: Searching for '$query'")

        val chapters = subjectChaptersMap[selectedSubject ?: "English Grammar"] ?: emptyList()
        Log.i("IndexPageFragment", "filterChapters: Searching in ${chapters.size} chapters")

        if (query.isEmpty()) {
            // If query is empty, show all original chapters
            Log.i("IndexPageFragment", "filterChapters: Empty query, showing all chapters")
            chapterAdapter.resetToOriginalChapters()
        } else {
            // Filter chapters based on query
            val filtered = chapters.filter {
                (it.name?.contains(query, ignoreCase = true) == true)
            }

            Log.i("IndexPageFragment", "filterChapters: Found ${filtered.size} matching chapters")
            filtered.forEachIndexed { index, chapter ->
                Log.i("IndexPageFragment", "filterChapters: Match[$index]: ${chapter.name}")
            }

            // Update adapter with filtered results (keeps original chapters intact)
            chapterAdapter.updateFilteredChapters(filtered)
        }
    }

    private fun handleClicks() {
        binding.searchVideo.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {
                val query = binding.searchVideo.text.toString().trim()
                filterChapters(query)
                hideKeyboard()
                true
            } else {
                false
            }
        }
        binding.changeGrade.setOnClickListener {
            findNavController().navigateUp()
        }
        binding.searchFun.setOnClickListener {
            val query = binding.searchVideo.text.toString().trim()
            filterChapters(query)
            hideKeyboard()
        }
    }

    private fun hideKeyboard() {
        val imm =
            requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchVideo.windowToken, 0)
    }

    private fun setupChapterRecyclerView() {
        Log.i("IndexPageFragment", "setupChapterRecyclerView:ind1 " + selectedSubjectPosition)
        val defaultSubject = selectedSubject ?: "English Grammar"
        val chapters = subjectChaptersMap[defaultSubject] ?: emptyList()
        chapterAdapter = ChapterAdapter(chapters, selectedGrade, subjectNames, selectedSubjectPosition, chapters)
        binding.chaptersRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.chaptersRecyclerView.adapter = chapterAdapter

        // Load chapters for the default or initially selected subject
        updateChaptersForSubject(defaultSubject.toString())
    }

    private fun updateChaptersForSubject(subjectName: String) {
        Log.i("IndexPageFragment", "updateChaptersForSubject: $subjectName")
        val chapters = subjectChaptersMap[subjectName] ?: emptyList()
        Log.i("IndexPageFragment", "updateChaptersForSubject: Found ${chapters.size} chapters for $subjectName")

        // Clear the previous list of chapters and add new ones
        chaptersList.clear()
        chaptersList.addAll(chapters)

        // Use updateAllChapters to set both original and current chapters
        chapterAdapter.updateAllChapters(chapters)

        // Clear any existing search
        binding.searchVideo.text?.clear()
    }

    /*private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
//    C:\Users\<USER>\adit\clt_classroom_backup_23_may_2025\CLTVIDEOS\Karnataka State Syllabus\English\5\English Grammar\Articles
        val gson = Gson()
        // Parse the top-level structure which is a list of UsbDriveContent items
        val cltVideos = gson.fromJson(cltVideos.toString(), Root::class.java)
        subjectNames = mutableListOf<String>()
       *//* for (usbDriveContent in cltVideos.usbDriveContents) {
            for (gradeLevel in usbDriveContent.children) {
                for ((index, subject) in gradeLevel.children.withIndex()) {
                    // Ensure subject has children and index is valid before accessing
//                    Log.i("IndexPageFragment", "getGrade!!!! " + subject.children[index].name)
                    Log.i("IndexPageFragment", "getSub..... " + gradeLevel.children[index].name)
                    Log.i("IndexPageFragment", "getSub.. " + selectedLang)
                    if (gradeLevel.children[index].name == selectedLang) {

                        for ((topicIndex, topic) in subject.children.withIndex()) {
                            Log.i("IndexPageFragment", "getGrade..... " + gradeLevel.children[index].name)
                            Log.i("IndexPageFragment", "getGrade.. " + selectedLang)
                            if (subject.children[topicIndex].name == selectedGrade) {
                                Log.i("IndexPageFragment", "getGrade!!..... " + gradeLevel.children[index].name)
                                Log.i("IndexPageFragment", "getGrade!!!!.. " + selectedLang)
                                for (subTopic in topic.children) {
                                    val testVal = subTopic.name
                                    subjectNames.add(subTopic.name)
                                    Log.i("IndexPageFragment", "getSubject:.. " + testVal)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent -> // These are the actual chapters
                                        // Assuming chapterContent.name is like "Chapter 1 - Title"
                                        // You might need to adjust parsing based on your actual data structure for chapter names/titles
//                                            if (testVal == selectedSubject) {
//                                                val chapterName =
//                                                chapterContent.name ?: "Unnamed Chapter"
//                                            val type = chapterContent.type ?: ""
//                                            val size = chapterContent.size ?: 0
//                                            val absolute_path = chapterContent.absolute_path ?: ""
                                            Log.i(
                                                "IndexPageFragment",
                                                "getSubject:######,,, " + chapterContent.name
                                            )
                                            Log.i(
                                                "IndexPageFragment",
                                                "getSubject:##### " + selectedSubject
                                            )
                                        if (chapterContent.type == "DIR") {
                                                    currentSubjectChapters.add(chapterContent)
                                                }
                                        // Assuming chapterContent.children contains the list of actual chapter Child objects
//                                            if (chapterName == selectedSubject) {
//                                        chapterContent.children?.let {
//                                            for ((chapterIndex, chapter) in it.withIndex()) {
//
//                                                if (chapter.type == "DIR") {
//                                                    currentSubjectChapters.add(chapter)
//                                                }
//                                                Log.i(
//                                                    "IndexPageFragment",
//                                                    "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
//                                                )
//                                            }
//
//                                        }
//                                            }
//                                        }
                                    }
                                    Log.i("IndexPageFragment", "getSubject:@@ " + chaptersList)
//                                    map[subTopic.name] = chaptersList
                                    // Store the chapters specifically for this subject
                                    map[subTopic.name] = currentSubjectChapters
                                }
                            }


                        }
                    }
                }
            }
            // ADD YOUR SAFE CODE HERE (as fallback or additional processing)
            // This will only run if the above code doesn't populate the map properly
            if (map.isEmpty()) {
                Log.i("IndexPageFragment", "Using safe fallback approach...")
                try {
                    for (usbDriveContent in cltVideos.usbDriveContents ?: emptyList()) {
                        for (gradeLevel in usbDriveContent.children ?: emptyList()) {
                            for ((index, subject) in gradeLevel.children?.withIndex() ?: emptyList<IndexedValue<Child>>()) {
                                val subjectName = gradeLevel.children?.getOrNull(index)?.name
                                if (subjectName == selectedLang) {
                                    for ((topicIndex, topic) in subject.children?.withIndex() ?: emptyList<IndexedValue<Child>>()) {
                                        if (subject.children?.getOrNull(topicIndex)?.name == selectedGrade) {
                                            topic.children?.forEach { subTopic ->
                                                val testVal = subTopic.name
                                                if (!subjectNames.contains(subTopic.name)) {
                                                    subjectNames.add(subTopic.name)
                                                }
                                                val currentSubjectChapters = mutableListOf<Child>()
                                                subTopic.children?.forEach { chapterContent ->
                                                    chapterContent.children?.forEachIndexed { chapterIndex, chapter ->
                                                        if (chapter.type == "DIR") {
                                                            currentSubjectChapters.add(chapter)
                                                        }
                                                        Log.i(
                                                            "IndexPageFragment",
                                                            "[SAFE] getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                        )
                                                    }
                                                }
                                                if (currentSubjectChapters.isNotEmpty()) {
                                                    map[subTopic.name] = currentSubjectChapters
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e("IndexPageFragment", "Error in safe fallback: ${e.message}")
                }
            }
        }*//*
        for (usbDriveContent in cltVideos.usbDriveContents ?: emptyList()) {
            for (gradeLevel in usbDriveContent.children ?: emptyList()) {
                for ((index, subject) in gradeLevel.children?.withIndex() ?: emptyList<IndexedValue<Child>>()) {
                    val subjectName = gradeLevel.children?.getOrNull(index)?.name
                    if (subjectName == selectedLang) {
                        for ((topicIndex, topic) in subject.children?.withIndex() ?: emptyList<IndexedValue<Child>>()) {
                            if (subject.children?.getOrNull(topicIndex)?.name == selectedGrade) {
                                topic.children?.forEach { subTopic ->
                                    val testVal = subTopic.name
                                    subjectNames.add(subTopic.name)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent ->
                                        chapterContent.children?.forEachIndexed { chapterIndex, chapter ->
                                            if (chapter.type == "DIR") {
                                                currentSubjectChapters.add(chapter)
                                            }
                                            Log.i(
                                                "IndexPageFragment",
                                                "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                            )
                                        }
                                    }
                                    map[subTopic.name] = currentSubjectChapters
                                }
                            }
                        }
                    }
                }
            }
        }
        AnalyticsUtils.logLargeString(map.toString())
        return map
    }*/
    private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
//    C:\Users\<USER>\adit\clt_classroom_backup_23_may_2025\CLTVIDEOS\Karnataka State Syllabus\English\5\English Grammar\Articles
        val gson = Gson()
        // Parse the top-level structure which is a list of UsbDriveContent items
        val cltVideos = gson.fromJson(cltVideos.toString(), Root::class.java)
        subjectNames = mutableListOf<String>()
        for (usbDriveContent in cltVideos.usbDriveContents ?: emptyList()) {
            for (gradeLevel in usbDriveContent.children ?: emptyList()) {
                for ((index, subject) in gradeLevel.children?.withIndex() ?: emptyList<IndexedValue<Child>>()) {
                    // Ensure subject has children and index is valid before accessing
//                    Log.i("IndexPageFragment", "getGrade!!!! " + subject.children[index].name)
                    Log.i("IndexPageFragment", "getSub..... " + gradeLevel.children?.getOrNull(index)?.name)
                    Log.i("IndexPageFragment", "getSub.. " + selectedLang)
                    if (gradeLevel.children?.getOrNull(index)?.name == selectedLang) {

                        for ((topicIndex, topic) in subject.children?.withIndex() ?: emptyList<IndexedValue<Child>>()) {
                            Log.i("IndexPageFragment", "getGrade..... " + gradeLevel.children?.getOrNull(index)?.name)
                            Log.i("IndexPageFragment", "getGrade.. " + selectedLang)
                            if (subject.children?.getOrNull(topicIndex)?.name == selectedGrade) {
                                Log.i("IndexPageFragment", "getGrade!!..... " + gradeLevel.children?.getOrNull(index)?.name)
                                Log.i("IndexPageFragment", "getGrade!!!!.. " + selectedLang)
                                for (subTopic in topic.children ?: emptyList()){
                                    val testVal = subTopic.name
                                    subTopic.name?.let { subjectNames.add(it) }
                                    Log.i("IndexPageFragment", "getSubject:.. " + testVal)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent -> // These are the actual chapters
                                        // Assuming chapterContent.name is like "Chapter 1 - Title"
                                        // You might need to adjust parsing based on your actual data structure for chapter names/titles
//                                            if (testVal == selectedSubject) {
//                                                val chapterName =
//                                                chapterContent.name ?: "Unnamed Chapter"
//                                            val type = chapterContent.type ?: ""
//                                            val size = chapterContent.size ?: 0
//                                            val absolute_path = chapterContent.absolute_path ?: ""
//                                            Log.i(
//                                                "IndexPageFragment",
//                                                "getSubject:#### " + chapterName
//                                            )
//                                            Log.i(
//                                                "IndexPageFragment",
//                                                "getSubject:##### " + selectedSubject
//                                            )
                                        // Assuming chapterContent.children contains the list of actual chapter Child objects
//                                            if (chapterName == selectedSubject) {
                                        chapterContent.children?.let {
                                            for ((chapterIndex, chapter) in it.withIndex()) {

                                                if (chapter.type == "DIR") {
                                                    currentSubjectChapters.add(chapter)
                                                }
                                                Log.i(
                                                    "IndexPageFragment",
                                                    "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                )
                                            }

                                        }
//                                            }
//                                        }
                                    }
                                    Log.i("IndexPageFragment", "getSubject:@@ " + chaptersList)
//                                    map[subTopic.name] = chaptersList
                                    // Store the chapters specifically for this subject
                                    //map[subTopic.name] = currentSubjectChapters
                                    subTopic.name?.let { map[it] = currentSubjectChapters }
                                }
                            }


                        }
                    }
                }
            }
        }
        AnalyticsUtils.logLargeString(map.toString())
        return map
    }

    private val subjectChaptersMap: Map<String, List<Child>> by lazy { generateSubjectChaptersMap() }

}
