    package clt.india.classroom.ui.splash

    import android.content.Intent
    import android.os.Bundle
    import android.provider.Settings
    import android.util.Log
    import androidx.appcompat.app.AppCompatActivity
    import androidx.lifecycle.lifecycleScope
    import clt.india.classroom.databinding.ActivitySplashVideoBinding
    import clt.india.classroom.ui.egr.ClassroomLessonsActivity
    import kotlinx.coroutines.delay
    import kotlinx.coroutines.launch

    class SplashVideoActivity : AppCompatActivity() {
        val TAG: String = "SplashScreenVideo "

        private lateinit var binding: ActivitySplashVideoBinding
        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            binding = ActivitySplashVideoBinding.inflate(layoutInflater)
            setContentView(binding.root)

            Log.d(TAG,"Android ID: " + Settings.Secure.getString(getApplicationContext().getContentResolver(), Settings.Secure.ANDROID_ID));
            Log.d(TAG,"IMEI ID: " + "12345");
            lifecycleScope.launch {
                delay(3000)
                startActivity(Intent(this@SplashVideoActivity, ClassroomLessonsActivity::class.java))
                finish()
            }
        }
        }
