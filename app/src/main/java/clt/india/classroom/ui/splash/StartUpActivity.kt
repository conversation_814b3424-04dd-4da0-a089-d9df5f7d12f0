package clt.india.classroom.ui.splash

import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_NO_HISTORY
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import clt.india.classroom.R
import clt.india.classroom.data.api.request.DeviceLoginResponse
import clt.india.classroom.data.api.response.BaseResponse
import clt.india.classroom.data.api.response.LoginResponse
import clt.india.classroom.databinding.ActivityStartUpScreenBinding
import clt.india.classroom.databinding.CustomProgressBarBinding
import clt.india.classroom.ui.DeviceIdVerificationActivity
import clt.india.classroom.ui.LogoutActivity
import clt.india.classroom.ui.egr.ClassroomLessonsActivity
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.SessionManager
import clt.india.classroom.utils.SessionManager.DEVICE_ID
import clt.india.classroom.utils.SessionManager.FIRST_TIME
import clt.india.classroom.viewmodel.SplashScreenViewModel

class StartUpActivity : AppCompatActivity() {
    val TAG: String = "SplashScreen "
    var androidId: String = "SplashScreen "
    private lateinit var includedLayoutBinding: CustomProgressBarBinding
    private lateinit var binding: ActivityStartUpScreenBinding
    private val viewModel by viewModels<SplashScreenViewModel>()

//        val androidId: String = "12333"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStartUpScreenBinding.inflate(layoutInflater)
        setContentView(binding.root)
        androidId = Settings.Secure.getString(
            getApplicationContext().getContentResolver(),
            Settings.Secure.ANDROID_ID
        )

        val view: View = binding.getRoot().getViewById(R.id.prgs)

        includedLayoutBinding = CustomProgressBarBinding.bind(view)

        Log.d(
            TAG,
            "Android ID: " + Settings.Secure.getString(
                getApplicationContext().getContentResolver(),
                Settings.Secure.ANDROID_ID
            )
        );
        Log.d(TAG, "IMEI ID: " + "12345");
Log.d(TAG, "0000000000")
        viewModel.deviceIdVerification(androidId, "0.0.1")
        viewModel.deviceLoginResult.observe(this) { it ->
            when (it) {
                is BaseResponse.Loading -> {
                    showLoading()
                    Log.d(TAG, "1111111111111");
                }

                is BaseResponse.Success -> {
                    Log.d(TAG, "22222222");
                    stopLoading()
                    processLogin(it.data)
//                        lifecycleScope.launch {
//                            delay(3000)
//                            startActivity(Intent(this@SplashScreenActivity, ClassroomLessonsActivity::class.java))
//                        }
                }

                is BaseResponse.Error -> {
                    Log.d(TAG, "33333333")
//                    Toast.makeText(this, "Something went wrong", Toast.LENGTH_SHORT).show()
                    stopLoading()
                    processError("Something went wrong")
                }

                else -> {
                    Log.d(TAG, "44444444")
                    showToast("1111111111111111111111")
                    processError("Something went wrong")
                }
            }
        }
    }

    fun processLogin(data: DeviceLoginResponse?) {
        Toast.makeText(this, "Success..." + androidId, Toast.LENGTH_SHORT).show()
        if (data != null && data.status != null) {
            data.status?.let { SessionManager.saveAuthDevice(this, it) }
            data.languages?.let { PrefUtilsManager.saveToPrefs(this,"language", it) }
            navigate(SessionManager.getFromPrefs(this, DEVICE_ID, 0))
        }
    }

    fun processError(data: String?) {
        navigate(SessionManager.getFromPrefs(this, DEVICE_ID, 0))
    }

    fun showLoading() {
        includedLayoutBinding.prgbar.visibility = View.VISIBLE
    }

    fun stopLoading() {
        includedLayoutBinding.prgbar.visibility = View.GONE
    }

    fun showToast(msg: String) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
    }

    fun navigate(status: Any?) {
       val firstTime : Boolean? = SessionManager.getFromPrefs(this, FIRST_TIME, true) as Boolean?
        if (status == 1 && !firstTime!!) {
            startActivity(Intent(this@StartUpActivity, ClassroomLessonsActivity::class.java))
            finish()
        } else {
            startActivity(Intent(this@StartUpActivity, DeviceIdVerificationActivity::class.java))
            finish()
        }
    }
}
