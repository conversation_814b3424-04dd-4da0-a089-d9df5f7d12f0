package clt.india.classroom.ui

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import clt.india.classroom.databinding.ActivityDeviceIdVerificationBinding
import clt.india.classroom.ui.egr.ClassroomLessonsActivity
import clt.india.classroom.utils.SessionManager
import clt.india.classroom.utils.SessionManager.DEVICE_ID
import clt.india.classroom.utils.SessionManager.FIRST_TIME
import clt.india.classroom.utils.SessionManager.saveToPrefs
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class
DeviceIdVerificationActivity : AppCompatActivity() {
    private lateinit var binding: ActivityDeviceIdVerificationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeviceIdVerificationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        supportActionBar?.hide()

        checkDeviceId()

    }

    fun checkDeviceId() {
        var deviceId: String = Settings.Secure.getString(
            getApplicationContext().getContentResolver(),
            Settings.Secure.ANDROID_ID
        )
        val status: Any? = SessionManager.getFromPrefs(this, DEVICE_ID, 0)
        if (deviceId != null) {
            binding.device.visibility = View.VISIBLE
            binding.noDevice.visibility = View.GONE

            binding.tvDeviceIdVal.text = deviceId
            if (status == 0) { // this is wrong, it should be 1, added for nav only.............................
                saveToPrefs(this, FIRST_TIME, false)
                binding.device.visibility = View.VISIBLE
                binding.tvDeviceIdSuccess.visibility = View.VISIBLE
                lifecycleScope.launch {
                    delay(2000)
                    startActivity(Intent(this@DeviceIdVerificationActivity, ClassroomLessonsActivity::class.java))
                    finish()
                }


            } else {
                binding.tvDeviceIdSuccess.visibility = View.GONE
            }
        } else {
            binding.device.visibility = View.GONE
            binding.noDevice.visibility = View.VISIBLE
        }
    }
}