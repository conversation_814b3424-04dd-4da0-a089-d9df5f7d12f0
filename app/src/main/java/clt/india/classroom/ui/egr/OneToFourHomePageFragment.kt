package clt.india.classroom.ui.egr

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.OneToFourChapterContentPagerAdapter
import clt.india.classroom.adapter.SubjectTabAdapter
import clt.india.classroom.databinding.FragmentOneToFourHomePageBinding
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import com.google.android.material.tabs.TabLayoutMediator

class OneToFourHomePageFragment : Fragment() {

    private var _binding: FragmentOneToFourHomePageBinding? = null
    private val binding get() = _binding!!

    private var selectedSubject: String? = null
    private var selectedGrade: String? = null
    private var chapDetails: String? = null
    private var position: Int? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentOneToFourHomePageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val subjectName = arguments?.getString("subjectName")
        val chapDetails = arguments?.getString("chap_details")
        position = arguments?.getInt("pos")

        Log.i("OneToFourHomePageFragment", "onViewCreated: "+subjectName)
        Log.i("OneToFourHomePageFragment", "onViewCreated:.. "+chapDetails)
        Log.i("OneToFourHomePageFragment", "onViewCreated:2222 "+position)
        selectedGrade = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.GRADE)
            ?: "Grade 1"
        binding.gradeText.text = GradeUtils.getOrdinalGrade(selectedGrade!!)

        setupSubjectTabs()
        setupViewPagerWithTabs(chapDetails)
        handleClicks()
    }

    private fun handleClicks() {
        binding.chapterNavigation.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
    }

    private fun setupSubjectTabs() {
        val subjects = listOf("Foundational Literacy")
        selectedSubject = subjects[0]

        val adapter = SubjectTabAdapter(subjects) { subject ->
            selectedSubject = subject
            refreshFragments()
        }

        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.subjectTabsRecyclerView.adapter = adapter
    }

    private fun refreshFragments() {
        binding.viewPager.adapter =
            OneToFourChapterContentPagerAdapter(requireActivity(), selectedSubject ?: "",
                position
            )
    }

    private fun setupViewPagerWithTabs(chapDetails: String?) {
        Log.i("OneToFourHomePageFragment", "setupViewPagerWithTabs:... "+chapDetails)
        val pagerAdapter =
            OneToFourChapterContentPagerAdapter(requireActivity(), selectedSubject ?: "",
                position
            )

        binding.viewPager.adapter = pagerAdapter

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = OneToFourChapterContentPagerAdapter.TAB_TITLES[position]
        }.attach()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
