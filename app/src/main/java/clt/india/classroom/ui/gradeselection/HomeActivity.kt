package clt.india.classroom.ui.gradeselection

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.ChapterContentPagerAdapter
import clt.india.classroom.adapter.SubjectTabAdapter
import clt.india.classroom.adapter.SubjectTabAdapter5To10
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.databinding.ActivityHomeBinding
import clt.india.classroom.intrface.OnAssessmentClickListener
import clt.india.classroom.ui.assesment.AssesmentshostActivity
import clt.india.classroom.ui.egr.ClassroomLessonsActivity
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import com.google.android.material.tabs.TabLayoutMediator

class HomeActivity : AppCompatActivity(), OnAssessmentClickListener, SubjectTabAdapter5To10.OnItemClickListener {  // Implement listener

//    private var selectedSubject: String? = null
    private lateinit var binding: ActivityHomeBinding
    private var selectedGrade: String? = null
    private var position: Int? = null
    private var selectedSubjectPosition: Int? = 0
    private var chapterName: String? = null
    private var chapterChild: String? = null
    private var subjectNames: ArrayList<String>? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHomeBinding.inflate(layoutInflater)

        enableEdgeToEdge()
        setContentView(binding.root)
        supportActionBar?.hide()
//        getIntent().getStringExtra("subjectName")?.let {
//            selectedSubject = it
//        }
        getIntent().getIntExtra("position", 0)?.let {
            position = it
        }
        getIntent().getStringExtra("chapterTitle")?.let {
            chapterName = it
        }
        getIntent().getStringArrayListExtra("subjectNames")?.let {
            subjectNames = it
        }
        getIntent().getIntExtra("selectedSubjectPosition",0)?.let {
            selectedSubjectPosition = it
        }
        getIntent().getIntExtra("chapterChild",0)?.let {
            chapterChild = it.toString()
        }

//        Log.i("HomeActivity", "onCreate11:.. "+selectedSubject)
        Log.i("HomeActivity", "onCreate12:.. "+selectedSubjectPosition)
        Log.i("HomeActivity", "onCreate13:.. "+subjectNames)
        Log.i("HomeActivity", "onCreate14:.. "+subjectNames?.get(selectedSubjectPosition!!))

        selectedGrade = GradePreferenceHelper.getSelection(this, GradePreferenceHelper.SelectionType.GRADE)
            ?: "Grade 1"
        binding.gradeText.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        setupSubjectTabs()
        setupViewPagerWithTabs()
        handleclicks()
    }

    private fun handleclicks() {
        binding.chapterNavigation.setOnClickListener {
            finish()
        }
        binding.changeGrade.setOnClickListener {
            val intent = Intent(this, ClassroomLessonsActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setupSubjectTabs() {
        val subjects = subjectNames

//        val adapter = SubjectTabAdapter5To10(subjects) { subject ->
//            selectedSubject = subject
//            refreshFragments()
//        }

        Log.i("HomeActivity", "setupSubjectTabs:.@.. "+selectedSubjectPosition)
        val adapter = SubjectTabAdapter5To10(subjectNames, selectedSubjectPosition ?: 0) { subject ->
            selectedSubjectPosition = subjectNames!!.indexOf(subject)
//            selectedSubject = subject
            refreshFragments()
        }

        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(this)
        binding.subjectTabsRecyclerView.adapter = adapter
    }
    private fun refreshFragments() {
//        Log.i("HomeActivity", "refreshFragments:.. "+selectedSubject)

        val pagerAdapter = ChapterContentPagerAdapter(
            fragmentActivity = this,
            listener = this,
            selectedGrade = selectedGrade,
            selectedSubject = subjectNames?.get(selectedSubjectPosition!!),  // pass the subject here if needed
            chapterName = chapterName,
            positionOfChap = position,
            chapterChild = chapterChild
        )
        binding.viewPager.adapter = pagerAdapter
    }

    private fun setupViewPagerWithTabs() {
//        Log.i("HomeActivity", "setupViewPagerWithTabs11:.. "+selectedSubject)
        val pagerAdapter = ChapterContentPagerAdapter(
            fragmentActivity = this,
            listener = this,
            selectedGrade = selectedGrade,
            selectedSubject = subjectNames?.get(selectedSubjectPosition!!),  // pass the subject here if needed
            chapterName = chapterName,
            positionOfChap = position,
            chapterChild = chapterChild
        )
        binding.viewPager.adapter = pagerAdapter

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = ChapterContentPagerAdapter.TAB_TITLES[position]
        }.attach()
    }

    override fun onAssessmentClicked(position: Int) {
        val intent = Intent(this, AssesmentshostActivity::class.java)
        intent.putExtra("selectedGrade", selectedGrade)

        startActivity(intent)
    }

    override fun onItemClick(position: Int) {
        TODO("Not yet implemented")
        selectedSubjectPosition = position
        Log.i("HomeActivity", "onItemClick:HomeActivity "+position)
    }

}
