package clt.india.classroom.ui.resources

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.ChapterDetailsAdapter
import clt.india.classroom.adapter.TextBookAdapter
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.data.api.response.cltvideos.Root
import clt.india.classroom.databinding.FragmentTextBooksBinding
import clt.india.classroom.databinding.FragmentVideoBinding
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.PrefUtilsManager.LANG
import com.google.gson.Gson


class TextBooksFragment : Fragment() {

    private var _binding: FragmentTextBooksBinding? = null
    private val binding get() = _binding!!

    private lateinit var chapterAdapter: TextBookAdapter
    private var selectedSubject: String? = null
    private var selectedChapter: String? = null
    private var position: Int? = null
    var childrenOfChap : ArrayList<Child> = arrayListOf()
//    private lateinit var chapterAdapter: ChapterDetailsAdapter
    private var selectedGrade: String? = null
    private var cltVideos: String? = null
    private var selectedLang: String? = null
    private var subjectNames = mutableListOf<String>()
    private var chaptersList = mutableListOf<Child>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            selectedChapter = it.getString(ARG_CHAPTER)
            position = it.getInt(ARG_POSITION, -1).takeIf { it != -1 }
        }
        selectedGrade = GradePreferenceHelper.getSelection(
            requireContext(), GradePreferenceHelper.SelectionType.GRADE
        ) ?: "Grade 1"
        selectedLang = (PrefUtilsManager.getFromPrefs(requireActivity(), LANG, "English")
            ?: "English").toString()
        cltVideos =
            PrefUtilsManager.getFromPrefs(requireActivity(), Constant.CLTVIDEO, "").toString()

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentTextBooksBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Track screen view
        AnalyticsUtils.logScreenView("TextBooksFragment")

        Log.i("TextBooksFragment", "onViewCreated: selectedSubject = $selectedSubject")
        Log.i("TextBooksFragment", "onViewCreated: selectedChapter = $selectedChapter")
        Log.i("TextBooksFragment", "onViewCreated: position = $position")

        // Initialize adapter first
        chapterAdapter = TextBookAdapter(childrenOfChap)
        binding.textBooksRecyclerview.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.textBooksRecyclerview.adapter = chapterAdapter

        // Load PDF content for selected chapter
        loadPdfContentOnly()
    }

    /**
     * Load PDF content only from selected chapter
     */
    private fun loadPdfContentOnly() {
        Log.i("TextBooksFragment", "loadPdfContentOnly: Starting PDF filtering")

        try {
            // Generate the subject chapters map
            val chaptersMap = subjectChaptersMap
            Log.i("TextBooksFragment", "loadPdfContentOnly: Generated map with ${chaptersMap.size} subjects")

            // Get chapters for the selected subject
            val subjectChapters = chaptersMap[selectedSubject] ?: emptyList()
            Log.i("TextBooksFragment", "loadPdfContentOnly: Found ${subjectChapters.size} chapters for subject '$selectedSubject'")

            // Clear existing content
            childrenOfChap.clear()

            // Filter PDF content from selected chapter
            val pdfContent = filterPdfContentFromChapters(subjectChapters)

            // Update the display with PDF content
            childrenOfChap.addAll(pdfContent)

            // Update adapter
            if (::chapterAdapter.isInitialized) {
                chapterAdapter.notifyDataSetChanged()
                Log.i("TextBooksFragment", "loadPdfContentOnly: Adapter updated with ${childrenOfChap.size} PDF items")
            }

        } catch (e: Exception) {
            Log.e("TextBooksFragment", "loadPdfContentOnly: Error loading PDF content", e)
        }
    }

    /**
     * Filter PDF content from chapters
     */
    private fun filterPdfContentFromChapters(chapters: List<Child>): List<Child> {
        Log.i("TextBooksFragment", "filterPdfContentFromChapters: Filtering ${chapters.size} chapters for PDF content")

        val allPdfContent = mutableListOf<Child>()

        val selectedChapterContent = when {
            // Method 1: If position is specified, get that specific chapter's content
            position != null && position!! >= 0 && position!! < chapters.size -> {
                val selectedChapter = chapters[position!!]
                Log.i("TextBooksFragment", "filterPdfContentFromChapters: Selected chapter: ${selectedChapter.name}")
                selectedChapter.children ?: emptyList()
            }
            // Method 2: Get content from all chapters
            chapters.isNotEmpty() -> {
                Log.i("TextBooksFragment", "filterPdfContentFromChapters: Getting content from all chapters")
                val allContent = mutableListOf<Child>()
                chapters.forEach { chapter ->
                    chapter.children?.let { allContent.addAll(it) }
                }
                allContent
            }
            // Method 3: No chapters available
            else -> {
                Log.w("TextBooksFragment", "filterPdfContentFromChapters: No chapters available")
                emptyList()
            }
        }

        // Filter for PDF files only
        val pdfFiles = filterPdfFilesOnly(selectedChapterContent)
        allPdfContent.addAll(pdfFiles)

        Log.i("TextBooksFragment", "filterPdfContentFromChapters: Found ${allPdfContent.size} PDF files")
        return allPdfContent
    }

    /**
     * Filter to get ONLY PDF files from the content list
     */
    private fun filterPdfFilesOnly(contentList: List<Child>): List<Child> {
        Log.i("TextBooksFragment", "filterPdfFilesOnly: Filtering ${contentList.size} items for PDF files")

        val pdfFiles = contentList.filter { item ->
            val fileName = item.name?.lowercase() ?: ""
            val isPdfFile = fileName.endsWith(".pdf")

            if (isPdfFile) {
                Log.i("TextBooksFragment", "filterPdfFilesOnly: Including PDF file: ${item.name}")
            } else {
                Log.i("TextBooksFragment", "filterPdfFilesOnly: Excluding non-PDF item: ${item.name}")
            }

            isPdfFile
        }

        Log.i("TextBooksFragment", "filterPdfFilesOnly: Found ${pdfFiles.size} PDF files out of ${contentList.size} total items")
        return pdfFiles
    }

    /**
     * Public method to update the selected chapter position
     * This will filter childrenOfChap to show only PDF files from the selected chapter
     */
    fun updateSelectedChapterPosition(chapterPosition: Int) {
        Log.i("TextBooksFragment", "updateSelectedChapterPosition: $chapterPosition")
        position = chapterPosition
        loadPdfContentOnly()
    }

    /**
     * Public method to show PDF files from all chapters
     */
    fun showAllPdfFiles() {
        Log.i("TextBooksFragment", "showAllPdfFiles: Showing PDFs from all chapters")
        position = null // Show from all chapters
        loadPdfContentOnly()
    }

    /**
     * Public method to filter for specific document formats
     */
    fun showSpecificDocumentFormat(extension: String) {
        Log.i("TextBooksFragment", "showSpecificDocumentFormat: Filtering for $extension files")

        try {
            val chaptersMap = subjectChaptersMap
            val subjectChapters = chaptersMap[selectedSubject] ?: emptyList()

            val selectedChapterContent = when {
                position != null && position!! >= 0 && position!! < subjectChapters.size -> {
                    subjectChapters[position!!].children ?: emptyList()
                }
                else -> {
                    val allContent = mutableListOf<Child>()
                    subjectChapters.forEach { chapter ->
                        chapter.children?.let { allContent.addAll(it) }
                    }
                    allContent
                }
            }

            // Filter for specific extension
            val specificFiles = selectedChapterContent.filter { item ->
                val fileName = item.name?.lowercase() ?: ""
                fileName.endsWith(extension.lowercase())
            }

            // Clear and add specific format files
            childrenOfChap.clear()
            childrenOfChap.addAll(specificFiles)

            Log.i("TextBooksFragment", "showSpecificDocumentFormat: Found ${specificFiles.size} $extension files")

            // Update adapter
            if (::chapterAdapter.isInitialized) {
                chapterAdapter.notifyDataSetChanged()
            }

        } catch (e: Exception) {
            Log.e("TextBooksFragment", "showSpecificDocumentFormat: Error filtering documents", e)
        }
    }

    /**
     * Get current chapter and PDF information
     */
    fun getCurrentPdfInfo(): Triple<String?, Int?, Int> {
        return Triple(selectedSubject, position, childrenOfChap.size)
    }

    /**
     * Get document statistics for current chapter
     */
    fun getDocumentStatistics(): Map<String, Int> {
        try {
            val chaptersMap = subjectChaptersMap
            val subjectChapters = chaptersMap[selectedSubject] ?: emptyList()

            val selectedChapterContent = when {
                position != null && position!! >= 0 && position!! < subjectChapters.size -> {
                    subjectChapters[position!!].children ?: emptyList()
                }
                else -> {
                    val allContent = mutableListOf<Child>()
                    subjectChapters.forEach { chapter ->
                        chapter.children?.let { allContent.addAll(it) }
                    }
                    allContent
                }
            }

            val stats = mutableMapOf<String, Int>()
            stats["total_items"] = selectedChapterContent.size
            stats["pdf_files"] = selectedChapterContent.count { it.name?.lowercase()?.endsWith(".pdf") == true }
            stats["doc_files"] = selectedChapterContent.count { it.name?.lowercase()?.endsWith(".doc") == true }
            stats["docx_files"] = selectedChapterContent.count { it.name?.lowercase()?.endsWith(".docx") == true }
            stats["txt_files"] = selectedChapterContent.count { it.name?.lowercase()?.endsWith(".txt") == true }
            stats["all_documents"] = stats["pdf_files"]!! + stats["doc_files"]!! + stats["docx_files"]!! + stats["txt_files"]!!
            stats["non_documents"] = selectedChapterContent.size - stats["all_documents"]!!

            Log.i("TextBooksFragment", "getDocumentStatistics: $stats")
            return stats

        } catch (e: Exception) {
            Log.e("TextBooksFragment", "getDocumentStatistics: Error getting statistics", e)
            return emptyMap()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_SUBJECT = "subject"
        private const val ARG_CHAPTER = "chapter"
        private const val ARG_POSITION = "position"

        fun newInstance(subject: String?): TextBooksFragment {
            val fragment = TextBooksFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            fragment.arguments = bundle
            return fragment
        }

        fun newInstance(subject: String?, chapter: String?, position: Int?): TextBooksFragment {
            val fragment = TextBooksFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            bundle.putString(ARG_CHAPTER, chapter)
            position?.let { bundle.putInt(ARG_POSITION, it) }
            fragment.arguments = bundle
            return fragment
        }
    }

    private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
//    C:\Users\<USER>\adit\clt_classroom_backup_23_may_2025\CLTVIDEOS\Karnataka State Syllabus\English\5\English Grammar\Articles
        val gson = Gson()
        // Parse the top-level structure which is a list of UsbDriveContent items
        val cltVideos = gson.fromJson(cltVideos.toString(), Root::class.java)
        subjectNames = mutableListOf<String>()
        for (usbDriveContent in cltVideos.usbDriveContents) {
            for (gradeLevel in usbDriveContent.children) {
                for ((index, subject) in gradeLevel.children.withIndex()) {
                    // Ensure subject has children and index is valid before accessing
//                    Log.i("IndexPageFragment", "getGrade!!!! " + subject.children[index].name)
                    Log.i("IndexPageFragment", "getSub..... " + gradeLevel.children[index].name)
                    Log.i("IndexPageFragment", "getSub.. " + selectedLang)
                    if (gradeLevel.children[index].name == selectedLang) {

                        for ((topicIndex, topic) in subject.children.withIndex()) {
                            Log.i("IndexPageFragment", "getGrade..... " + gradeLevel.children[index].name)
                            Log.i("IndexPageFragment", "getGrade.. " + selectedLang)
                            if (subject.children[topicIndex].name == selectedGrade) {
                                Log.i("IndexPageFragment", "getGrade!!..... " + gradeLevel.children[index].name)
                                Log.i("IndexPageFragment", "getGrade!!!!.. " + selectedLang)
                                for (subTopic in topic.children) {
                                    val testVal = subTopic.name
                                    subjectNames.add(subTopic.name)
                                    Log.i("IndexPageFragment", "getSubject:.. " + testVal)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent -> // These are the actual chapters

                                        chapterContent.children?.let {
                                            for ((chapterIndex, chapter) in it.withIndex()) {

                                                if (chapter.type == "DIR") {
                                                    currentSubjectChapters.add(chapter)

                                                    chaptersList.add(chapter)
                                                    // Don't set childrenOfChap here - let loadPdfContentOnly handle it
                                                    Log.i("TextBooksFragment", "generateSubjectChaptersMap: Added chapter: ${chapter.name}")
                                                }
                                                Log.i(
                                                    "IndexPageFragment",
                                                    "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                )
                                            }

                                        }
//                                            }
//                                        }
                                    }
                                    Log.i("IndexPageFragment", "getSubject:@@ " + chaptersList)
//                                    map[subTopic.name] = chaptersList
                                    // Store the chapters specifically for this subject
                                    map[subTopic.name] = currentSubjectChapters
                                }
                            }


                        }
                    }
                }
            }
        }
        AnalyticsUtils.logLargeString(map.toString())
        return map
    }
    private val subjectChaptersMap: Map<String, List<Child>> by lazy { generateSubjectChaptersMap() }

}
