package clt.india.classroom.ui.assesment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.QuestionViewAdapter
import clt.india.classroom.databinding.FragmentAssesmentsBinding
import clt.india.classroom.intrface.OnAssessmentClickListener
import clt.india.classroom.intrface.OnChapterClickListener

class AssesmentsFragment : Fragment(), OnChapterClickListener {

    private var _binding: FragmentAssesmentsBinding? = null
    private val binding get() = _binding!!
    private lateinit var chapterAdapter: QuestionViewAdapter
    private lateinit var listener: OnAssessmentClickListener
    private var selectedSubject: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        val activity = requireActivity()
        if (activity is OnAssessmentClickListener) {
            listener = activity
        } else {
            throw IllegalStateException("$activity must implement OnAssessmentClickListener")
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAssesmentsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val images = List(6) { R.drawable.image1 }
        val titles = List(6) { index -> "Assessment ${index + 1}" }

        chapterAdapter = QuestionViewAdapter(
            images,
            titles,
            clickListener = this
        )

        binding.assessmentsRecyclerView.apply {
            layoutManager = GridLayoutManager(requireContext(), 3)
            adapter = chapterAdapter
        }
    }

    override fun onChapterClicked(position: Int) {
        listener.onAssessmentClicked(position)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_SUBJECT = "subject"

        fun newInstance(subject: String?): AssesmentsFragment {
            val fragment = AssesmentsFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            fragment.arguments = bundle
            return fragment
        }
    }
}
