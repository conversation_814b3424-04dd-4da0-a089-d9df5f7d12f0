package clt.india.classroom.ui.assesment

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.QuestionViewAdapter
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.data.api.response.cltvideos.Root
import clt.india.classroom.databinding.FragmentAssesmentsBinding
import clt.india.classroom.intrface.OnAssessmentClickListener
import clt.india.classroom.intrface.OnChapterClickListener
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.PrefUtilsManager.LANG
import com.google.gson.Gson


class AssesmentsFragment : Fragment(), OnChapterClickListener {

    private var _binding: FragmentAssesmentsBinding? = null
    private val binding get() = _binding!!
    private lateinit var chapterAdapter: QuestionViewAdapter
    private lateinit var listener: OnAssessmentClickListener
    private var selectedSubject: String? = null
    private var chapters = listOf<Child>()

    private var selectedGrade: String? = null
    private var cltVideos: String? = null
    private var selectedLang: String? = null
    private var subjectNames = mutableListOf<String>()
    private var chaptersList = mutableListOf<Child>()
    var childrenOfChap: ArrayList<Child> = arrayListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            Log.d("AssessmentFragment", "onCreate: selectedSubject = $selectedSubject")
        }

        selectedGrade = GradePreferenceHelper.getSelection(
            requireContext(), GradePreferenceHelper.SelectionType.GRADE
        ) ?: "Grade 1"
        selectedLang = (PrefUtilsManager.getFromPrefs(requireActivity(), LANG, "English")
            ?: "English").toString()
        cltVideos =
            PrefUtilsManager.getFromPrefs(requireActivity(), Constant.CLTVIDEO, "").toString()

        Log.i("AssessmentFragment", "onCreate: $cltVideos")
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        val activity = requireActivity()
        if (activity is OnAssessmentClickListener) {
            listener = activity
        } else {
            throw IllegalStateException("$activity must implement OnAssessmentClickListener")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAssesmentsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        generateSubjectChaptersMap()
        val chapterName = arguments?.getString(ARG_CHAPTER)
        val subjectChaptersMap = generateSubjectChaptersMap()

        // Only get chapter folder matching the given chapterName
        val chapterFolders = subjectChaptersMap[selectedSubject]
            ?.filter { it.name.equals(chapterName, ignoreCase = true) }
            ?: emptyList()

        Log.d(
            "AssessmentFragment",
            "Filtered folders with chapter name: $chapterName, Count: ${chapterFolders.size}"
        )


        Log.d(
            "AssessmentFragment",
            "Chapter folders found for $selectedSubject: ${chapterFolders.size}"
        )

        // Go TWO LEVELS DEEPER to find JSON files
        val jsonFiles = mutableListOf<Child>()
        chapterFolders.forEach { chapterFolder ->
            Log.d("AssessmentFragment", "Processing chapter folder: ${chapterFolder.name}")

            // First level: Go inside chapter folder (like "Articles")
            chapterFolder.children?.forEach { subFolder ->
                Log.d(
                    "AssessmentFragment",
                    "Found subfolder: ${subFolder.name}, Type: ${subFolder.type}"
                )

                if (subFolder.type == "DIR") {
                    // Second level: Go inside subfolder and look for JSON files
                    subFolder.children?.forEach { file ->
                        Log.d("AssessmentFragment", "Found file: ${file.name}, Type: ${file.type}")

                        if (file.type == "FILE" && file.name.lowercase().endsWith(".json")) {

                            jsonFiles.add(file)
                            Log.d(
                                "AssessmentFragment",
                                "Added JSON file: ${file.name} from folder: ${subFolder.name}"
                            )
                        }
                    }
                } else if (subFolder.type == "FILE" && subFolder.name.lowercase()
                        .endsWith(".json")
                ) {
                    // Handle case where JSON is directly in chapter folder
                    jsonFiles.add(subFolder)
                    Log.d("AssessmentFragment", "Added direct JSON file: ${subFolder.name}")
                }
            }
        }

        chapters = jsonFiles

        Log.d("AssessmentFragment", "Total JSON files found: ${chapters.size}")
        chapters.forEachIndexed { index, child ->
            Log.d("AssessmentFragment", "JSON File $index: ${child.name}")
        }

        if (chapters.isNotEmpty()) {
            val images = List(chapters.size) { R.drawable.image1 }
            // Use JSON file names or create custom titles
            val titles = chapters.map { jsonFile ->
                // Option 1: Use JSON filename without extension
                jsonFile.name.removeSuffix(".json")
            }

            chapterAdapter = QuestionViewAdapter(images, titles, 0, this)
            binding.assessmentsRecyclerView.apply {
                layoutManager = GridLayoutManager(requireContext(), 3)
                adapter = chapterAdapter
            }
        } else {
            Log.e("AssessmentFragment", "No JSON files found for subject: $selectedSubject")
//            Toast.makeText(
//                requireContext(),
//                "No assessments available for $selectedSubject",
//                Toast.LENGTH_SHORT
//            ).show()
        }
    }

    private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
        val gson = Gson()

        if (cltVideos.isNullOrEmpty()) {
            Log.e("AssessmentFragment", "cltVideos is null or empty")
            return map
        }

        try {
            val cltVideosRoot = gson.fromJson(cltVideos, Root::class.java)

            cltVideosRoot.usbDriveContents?.forEach { usbDriveContent ->
                usbDriveContent.children?.forEach { gradeLevel ->
                    gradeLevel.children?.withIndex()?.forEach { (index, subject) ->
                        if (subject.name == selectedLang) {
                            subject.children?.withIndex()?.forEach { (topicIndex, topic) ->
                                if (topic.name == selectedGrade) {
                                    topic.children?.forEach { subTopic ->
                                        val testVal = subTopic.name
                                        subjectNames.add(testVal)
                                        Log.i("AssessmentFragment", "getSubject: $testVal")

                                        val currentSubjectChapters = mutableListOf<Child>()

                                        subTopic.children?.forEach { chapterContent ->
                                            chapterContent.children?.forEachIndexed { chapterIndex, chapter ->
                                                if (chapter.type == "DIR") {
                                                    currentSubjectChapters.add(chapter)
                                                    chaptersList.add(chapter)

                                                    if (index < chaptersList.size) {
                                                        childrenOfChap =
                                                            chaptersList[index].children
                                                                ?: arrayListOf()
                                                    }
                                                }
                                                Log.i(
                                                    "AssessmentFragment",
                                                    "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                )
                                            }
                                        }

                                        map[testVal] = currentSubjectChapters
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("AssessmentFragment", "Error parsing JSON", e)
        }

        Log.d("AssessmentFragment", "Generated map: $map")
        return map
    }

    override fun onChapterClicked(position: Int, chapterTitle: String) {
        Log.d("AssessmentFragment", " onChapterClicked called:")
        Log.d("AssessmentFragment", "  Position: $position")
        Log.d("AssessmentFragment", "  Chapter Title: $chapterTitle")
        Log.d("AssessmentFragment", "  Chapters size: ${chapters.size}")

        if (position < chapters.size) {
            val selectedJsonFile = chapters[position]

            Log.d("AssessmentFragment", "📁 Selected JSON file details:")
            Log.d("AssessmentFragment", "  Name: '${selectedJsonFile.name}'")
            Log.d("AssessmentFragment", "  Type: '${selectedJsonFile.type}'")
            Log.d("AssessmentFragment", "  Absolute path: '${selectedJsonFile.absolute_path}'")
            Log.d("AssessmentFragment", "  Path is null: ${selectedJsonFile.absolute_path == null}")
            Log.d(
                "AssessmentFragment",
                "  Path is empty: ${selectedJsonFile.absolute_path?.isEmpty()}"
            )


            Log.d("AssessmentFragment", "  Calling listener.onAssessmentClicked with:")
            Log.d("AssessmentFragment", "  Position: $position")
            Log.d("AssessmentFragment", "  JSON Path: '${selectedJsonFile.absolute_path}'")
            Log.d("AssessmentFragment", "  Chapter Title: '$chapterTitle'")

            listener.onAssessmentClicked(
                position,
                selectedJsonFile.absolute_path ?: "",
                chapterTitle
            )
        } else {
            Log.e(
                "AssessmentFragment",
                " Position $position is out of bounds for chapters list (size: ${chapters.size})"
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_SUBJECT = "subject"
        private const val ARG_CHAPTER = "chapter"

        fun newInstance(subject: String?, chapter: String?): AssesmentsFragment {
            val fragment = AssesmentsFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            bundle.putString(ARG_CHAPTER, chapter)
            fragment.arguments = bundle
            return fragment
        }
    }
}
