package clt.india.classroom.ui.assesment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.QuestionResultsAdapter
import clt.india.classroom.databinding.FragmentAssessmentResultsBinding
import clt.india.classroom.data.api.response.Question
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import clt.india.classroom.viewmodel.AssessmentViewModel

class AssessmentResultsFragment : Fragment() {
    private var _binding: FragmentAssessmentResultsBinding? = null
    private val binding get() = _binding!!

    private val vm: AssessmentViewModel by activityViewModels()
    private lateinit var questionResultsAdapter: QuestionResultsAdapter
    private var assessment1Questions = listOf<Question>()
    private var assessment2Questions = listOf<Question>()
    private var currentQuestions = listOf<Question>()
    private var selectedGrade: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedGrade = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.GRADE)
            ?: "Grade 1"

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAssessmentResultsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (requireActivity() as AppCompatActivity).supportActionBar?.hide()
        binding.gradeText.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        setupSampleData()
        setupRecyclerView(assessment1Questions)
        setupTabs()
        handleClicks()
    }

    private fun setupSampleData() {
        // Get user's actual answers from ViewModel
        val userAnswers = vm.answeredQuestions

        assessment1Questions = listOf(
            Question(
                1,
                "The method of separating seeds of paddy from their stalks is called?",
                listOf("Threshing", "Winnowing", "Sieving", "Filtration"),
                selectedOption = userAnswers.getOrNull(0)?.selectedOption, // ← Use actual user selection
                correctAnswer = 0
            ),
            Question(
                2,
                "The process of heating milk to kill bacteria is known as?",
                listOf("Boiling", "Pasteurization", "Freezing", "Evaporation"),
                selectedOption = userAnswers.getOrNull(1)?.selectedOption, // ← Use actual user selection
                correctAnswer = 1
            )
        )

        assessment2Questions = listOf(
            Question(
                1,
                "Which gas is responsible for global warming?",
                listOf("Oxygen", "Carbon Dioxide", "Nitrogen", "Hydrogen"),
                selectedOption = userAnswers.getOrNull(0)?.selectedOption, // ← Use actual user selection
                correctAnswer = 1
            ),
            Question(
                2,
                "Which is used to measure temperature?",
                listOf("Barometer", "Thermometer", "Hygrometer", "Speedometer"),
                selectedOption = userAnswers.getOrNull(1)?.selectedOption, // ← Use actual user selection
                correctAnswer = 1
            )
        )
    }

    private fun setupRecyclerView(questions: List<Question>) {
        currentQuestions = questions
        questionResultsAdapter = QuestionResultsAdapter(currentQuestions)
        binding.assessmentRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.assessmentRecyclerView.adapter = questionResultsAdapter
    }

    private fun setupTabs() {
        val tab1 = binding.tabAssessment1
        val tab2 = binding.tabAssessment2

        selectTab(tab1, true)
        selectTab(tab2, false)

        tab1.setOnClickListener {
            selectTab(tab1, true)
            selectTab(tab2, false)
            setupRecyclerView(assessment1Questions)
        }
        tab2.setOnClickListener {
            selectTab(tab1, false)
            selectTab(tab2, true)
            setupRecyclerView(assessment2Questions)
        }
    }

    private fun selectTab(tab: TextView, isSelected: Boolean) {
        if (isSelected) {
            tab.setBackgroundResource(R.drawable.selected_tab_background)
            tab.setTextColor(resources.getColor(android.R.color.white, null))
        } else {
            tab.setBackgroundResource(R.drawable.unselected_tab_background)
            tab.setTextColor(resources.getColor(android.R.color.black, null))
        }
    }

    private fun handleClicks() {
        binding.backButton.setOnClickListener {
            findNavController().navigateUp()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}