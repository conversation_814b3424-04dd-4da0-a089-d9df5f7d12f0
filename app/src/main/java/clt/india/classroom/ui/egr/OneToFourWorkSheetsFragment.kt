package clt.india.classroom.ui.egr

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.OneToFourVideoChapterDetailsAdapter
import clt.india.classroom.data.api.request.Children
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.data.api.response.egl.EGLData
import clt.india.classroom.databinding.FragmentOneToFourWorkSheetsBinding
import clt.india.classroom.databinding.FragmentVideoBinding
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.ContentFilterUtils
import clt.india.classroom.utils.PrefUtilsManager
import com.google.gson.Gson


class OneToFourWorkSheetsFragment : Fragment() {

    private var _binding: FragmentVideoBinding? = null
    private val binding get() = _binding!!

    private lateinit var chapterAdapter: OneToFourVideoChapterDetailsAdapter
    private var selectedSubject: String? = null
    private var chapDetails: String? = null
    private var chapIndex: Int? = null
    private var chapDetailsArray: Array<ChapterNew>? = null
    var eglJson : String? = null
    var chaptersList = mutableListOf<ChapterNew>()
    var childrenOfChap : ArrayList<Children> = arrayListOf()
    var chaptersTitle: ArrayList<String> = arrayListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            chapDetails = it.getString(CHAP_DETAILS)
            chapIndex = it.getInt("pos")

            Log.i("OneToFourVideoFragment", "onCreates:index "+chapIndex)
            Log.i("OneToFourVideoFragment", "onCreates: "+chapDetails)
        }

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentVideoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        eglJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()
        val map = mutableMapOf<String, List<ChapterNew>>()
        val gson = Gson()
        val egrData = gson.fromJson(eglJson.toString(), EGLData::class.java)

        egrData.usbDriveContents?.forEach { root -> // e.g., "Class 1"
            root.children?.forEach { contentSeries -> // e.g., "Foundational Literacy" (Subject)
                // Process only if the current subject matches the selected subject
                Log.i("OneToFourVideoFragment", "onVewCreated.. "+contentSeries.name +"  "+selectedSubject)

                if (contentSeries.name == "SERIES 1") {
                    val subjectName = contentSeries.name ?: "Unknown Subject"
                    chaptersList = mutableListOf<ChapterNew>()
                    contentSeries.children?.forEachIndexed { index, chapterContent -> // These are the actual chapters
                        // Assuming chapterContent.name is like "Chapter 1 - Title"
                        // You might need to adjust parsing based on your actual data structure for chapter names/titles
                        val chapterName = chapterContent.name ?: "Unnamed Chapter"
                        // For simplicity, let's assume the name is the title and chapter number is part of it or needs extraction

                        chaptersList.add(ChapterNew(chapterName, index.toString(), "No description", chapterContent.children)) // this need to update

                        Log.i("OneToFourVideoFragment", "onViewCreated:in " + chapIndex)
                        if (index == chapIndex) {
//                            Log.i("OneToFourVideoFragment", "onViewCreated:innn " + chaptersList.get(chapIndex!!).children.get(2).name)
                            // Filter out children whose name is "worksheet"
//                            childrenOfChap = ArrayList(chaptersList[chapIndex!!].children.filterNot { it.name == "worksheet" || it.name!!.contains("- Activity", ignoreCase = true) })

//                            val iterator = childrenOfChap.iterator()
//                            while (iterator.hasNext()) {
//                                if (iterator.next().name == "worksheet") {
//                                    iterator.remove()
//                                }
//                            }
                            // Get the original children list
                            val originalChildren = chaptersList.get(chapIndex!!).children

                            // Filter to INCLUDE only worksheets and PDFs (exclude videos)
                            childrenOfChap = ContentFilterUtils.filterCustom(
                                children = originalChildren,
                                filterFunction = { child ->
                                    val name = child.name?.lowercase() ?: ""
                                    // Include if it contains "worksheet" OR is a PDF file
                                    name.contains("worksheet") || name.endsWith(".pdf")
                                },
                                logTag = "OneToFourWorkSheetsFragment"
                            )

                            chaptersTitle.add(chaptersList.get(chapIndex!!).children.toString())

                            Log.i("OneToFourWorkSheetsFragment", "onViewCreated:nn.. " + index + "  " + chapIndex)
                            Log.i("OneToFourWorkSheetsFragment", "onViewCreated:nn.. " + childrenOfChap)
                            Log.i("OneToFourWorkSheetsFragment", "onViewCreated:nn  chaptersTitle " + chaptersTitle)

                        }

                    }
                    map[subjectName] = chaptersList
                }
            }
        }

//        val titles = listOf("FL Video 1.............", "FL Video 2", "FL Video 3")
        val titles = chaptersTitle
        Log.i("OneToFourWorkSheetsFragment", "OneToFourWorkSheetsFragment: titles "+titles)


        chapterAdapter = OneToFourVideoChapterDetailsAdapter(
            chaptersTitle = childrenOfChap,
            onChapterClick = { chapterName ->
//                val videoListFragment = OneToFourVideoListFragment.newInstance(chapterName.chapterTitle)
//                parentFragmentManager.beginTransaction()
//                    .replace(R.id.fragment_container, videoListFragment)
//                    .addToBackStack(null)
//                    .commit()
//                val intent = Intent(requireContext(), VideoPlayingActivity::class.java)
//                intent.putExtra("video_title", chapterName.chapterTitle)
//                startActivity(intent)

            }
        )

        binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.videoRecyclerView.adapter = chapterAdapter

    }




    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    companion object {
        private const val ARG_SUBJECT = "subject"
        private const val CHAP_DETAILS = "chap_details"

        fun newInstance(subject: String, pos: Int?): OneToFourVideoFragment {
            val fragment = OneToFourVideoFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            if (pos != null) {
                bundle.putInt("pos", pos)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

}