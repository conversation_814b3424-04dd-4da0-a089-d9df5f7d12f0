package clt.india.classroom.ui.resources

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.ChapterDetailsAdapter
import clt.india.classroom.data.api.request.Children
import clt.india.classroom.data.api.response.egl.EGLData
import clt.india.classroom.databinding.FragmentVideoBinding
import clt.india.classroom.data.api.response.Chapter
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.data.api.response.cltvideos.Root
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.FragmentArgumentsTest
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.PrefUtilsManager.LANG
import com.google.gson.Gson


class VideoFragment5To10 : Fragment() {

    private var _binding: FragmentVideoBinding? = null
    private val binding get() = _binding!!
    var egrJson : String? = null

    private lateinit var chapterAdapter: ChapterDetailsAdapter
    private var selectedSubject: String? = null
    private var selectedGrade: String? = null
    private var cltVideos: String? = null
    private var selectedLang: String? = null
    private var subjectNames = mutableListOf<String>()
    private var chaptersList = mutableListOf<Child>()

    var childrenOfChap : ArrayList<Child> = arrayListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Test fragment arguments
        FragmentArgumentsTest.testFragmentArguments("VideoFragment5To10", arguments, null)

        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            Log.i("VideoFragment5To10", "Arguments received - selectedSubject: $selectedSubject")
        } ?: run {
            Log.w("VideoFragment5To10", "No arguments received!")
        }

        selectedGrade = GradePreferenceHelper.getSelection(
            requireContext(), GradePreferenceHelper.SelectionType.GRADE
        ) ?: "Grade 1"
        selectedLang = (PrefUtilsManager.getFromPrefs(requireActivity(), LANG, "English")
            ?: "English").toString()
        cltVideos =
            PrefUtilsManager.getFromPrefs(requireActivity(), Constant.CLTVIDEO, "").toString()

        Log.i("VideoFragment5To10", "=== VideoFragment5To10 onCreate Debug ===")
        Log.i("VideoFragment5To10", "selectedSubject: $selectedSubject")
        Log.i("VideoFragment5To10", "selectedGrade: $selectedGrade")
        Log.i("VideoFragment5To10", "selectedLang: $selectedLang")
        Log.i("VideoFragment5To10", "cltVideos length: ${cltVideos?.length ?: 0}")
        Log.i("VideoFragment5To10", "=== End Debug ===")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentVideoBinding.inflate(inflater, container, false)
//        egrJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Log.i("VideoFragment5To10", "onViewCreated: selectedSubject = $selectedSubject")

        // If selectedSubject is null, try to use the first available subject
        if (selectedSubject == null && subjectNames.isNotEmpty()) {
            selectedSubject = subjectNames[0]
            Log.i("VideoFragment5To10", "Using fallback subject: $selectedSubject")
        }

        // Initialize chapterAdapter with an empty list or based on the initial/default subject
        updateChaptersForSubject(selectedSubject)

        chapterAdapter = ChapterDetailsAdapter(childrenOfChap)
        binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.videoRecyclerView.adapter = chapterAdapter

        Log.i("VideoFragment5To10", "onViewCreated completed. childrenOfChap size: ${childrenOfChap.size}")
    }

    private fun updateChaptersForSubject(subjectName: String?) {
        Log.i("VideoFragment5To10", "updateChaptersForSubject called with: $subjectName")
        Log.i("VideoFragment5To10", "Available subjects in map: ${subjectChaptersMap.keys}")

        // Fetch chapters for the given subjectName from the map
        val chaptersForSubject = subjectChaptersMap[subjectName] ?: emptyList()
        Log.i("VideoFragment5To10", "Found ${chaptersForSubject.size} chapters for subject: $subjectName")

        // Clear the existing list and add new chapters
        childrenOfChap.clear()
        childrenOfChap.addAll(chaptersForSubject)

        // If the adapter is already initialized, notify it of the data change
        if (::chapterAdapter.isInitialized) {
            chapterAdapter.notifyDataSetChanged()
            Log.i("VideoFragment5To10", "Adapter updated with ${childrenOfChap.size} chapters")
        } else {
            Log.i("VideoFragment5To10", "Adapter not yet initialized, will be created later")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion
    object {
        private const val ARG_SUBJECT = "subject"

        fun newInstance(subject: String?): VideoFragment5To10 {
            val fragment = VideoFragment5To10()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            fragment.arguments = bundle
            return fragment
        }
    }

    private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
//    C:\Users\<USER>\adit\clt_classroom_backup_23_may_2025\CLTVIDEOS\Karnataka State Syllabus\English\5\English Grammar\Articles
        val gson = Gson()
        // Parse the top-level structure which is a list of UsbDriveContent items
        val cltVideos = gson.fromJson(cltVideos.toString(), Root::class.java)
        subjectNames = mutableListOf<String>()
        for (usbDriveContent in cltVideos.usbDriveContents) {
            for (gradeLevel in usbDriveContent.children) {
                for ((index, subject) in gradeLevel.children.withIndex()) {
                    // Ensure subject has children and index is valid before accessing
//                    Log.i("IndexPageFragment", "getGrade!!!! " + subject.children[index].name)
                    Log.i("IndexPageFragment", "getSub..... " + gradeLevel.children[index].name)
                    Log.i("IndexPageFragment", "getSub.. " + selectedLang)
                    if (gradeLevel.children[index].name == selectedLang) {

                        for ((topicIndex, topic) in subject.children.withIndex()) {
                            Log.i("IndexPageFragment", "getGrade..... " + gradeLevel.children[index].name)
                            Log.i("IndexPageFragment", "getGrade.. " + selectedLang)
                            if (subject.children[topicIndex].name == selectedGrade) {
                                Log.i("IndexPageFragment", "getGrade!!..... " + gradeLevel.children[index].name)
                                Log.i("IndexPageFragment", "getGrade!!!!.. " + selectedLang)
                                for (subTopic in topic.children) {
                                    val testVal = subTopic.name
                                    subjectNames.add(subTopic.name)
                                    Log.i("IndexPageFragment", "getSubject:.. " + testVal)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent -> // These are the actual chapters

                                        chapterContent.children?.let {
                                            for ((chapterIndex, chapter) in it.withIndex()) {

                                                if (chapter.type == "DIR") {
                                                    currentSubjectChapters.add(chapter)

                                                    chaptersList.add(chapter)
                                                    if (index < chaptersList.size) { // Check if index is within bounds
                                                        childrenOfChap = chaptersList.get(index).children
                                                    }
                                                }
                                                Log.i(
                                                    "IndexPageFragment",
                                                    "getSubject: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                )
                                            }

                                        }
//                                            }
//                                        }
                                    }
                                    Log.i("IndexPageFragment", "getSubject:@@ " + chaptersList)
//                                    map[subTopic.name] = chaptersList
                                    // Store the chapters specifically for this subject
                                    map[subTopic.name] = currentSubjectChapters
                                }
                            }


                        }
                    }
                }
            }
        }
        AnalyticsUtils.logLargeString(map.toString())
        return map
    }
    private val subjectChaptersMap: Map<String, List<Child>> by lazy { generateSubjectChaptersMap() }


}
