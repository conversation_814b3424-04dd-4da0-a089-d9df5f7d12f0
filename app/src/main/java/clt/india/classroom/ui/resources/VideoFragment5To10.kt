package clt.india.classroom.ui.resources

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.adapter.ChapterDetailsAdapter
import clt.india.classroom.databinding.FragmentVideoBinding
import clt.india.classroom.data.api.response.cltvideos.Child
import clt.india.classroom.data.api.response.cltvideos.Root
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.PrefUtilsManager
import clt.india.classroom.utils.PrefUtilsManager.LANG
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken


class VideoFragment5To10 : Fragment() {

    private var _binding: FragmentVideoBinding? = null
    private val binding get() = _binding!!
    var egrJson : String? = null

    private lateinit var chapterAdapter: ChapterDetailsAdapter
    private var selectedSubject: String? = null
    private var selectedGrade: String? = null
    private var cltVideos: String? = null
    private var selectedLang: String? = null
    private var chapterdetails: List<Child>? = null
    private var position: Int? = 0
    private var subjectNames = mutableListOf<String>()
    private var chaptersList = mutableListOf<Child>()

    var childrenOfChap : ArrayList<Child> = arrayListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            position = it.getInt(POSTION)
            val chapterDetailsString = it.getString(CHAP_DETAILS)
            if (chapterDetailsString != null) {
                val gson = Gson()
                try {
                    Log.i("VideoFragment5To10", "Parsing chapterDetailsString: $chapterDetailsString")

                    // Check if the string is a valid JSON array
                    if (chapterDetailsString.trim().startsWith("[")) {
                        chapterdetails = gson.fromJson(chapterDetailsString, object : TypeToken<List<Child>>() {}.type)
                        Log.i("VideoFragment5To10", "Successfully parsed ${chapterdetails.size} chapter details")
                    } else {
                        Log.w("VideoFragment5To10", "chapterDetailsString is not a JSON array, it starts with: ${chapterDetailsString.take(10)}")
                        chapterdetails = emptyList()
                    }
                } catch (e: JsonSyntaxException) {
                    Log.e("VideoFragment5To10", "JSON parsing error: ${e.message}")
                    Log.e("VideoFragment5To10", "Problematic JSON string: $chapterDetailsString")
                    chapterdetails = emptyList()
                } catch (e: Exception) {
                    Log.e("VideoFragment5To10", "Unexpected error parsing JSON: ${e.message}")
                    chapterdetails = emptyList()
                }
            }
        }

        selectedGrade = GradePreferenceHelper.getSelection(
            requireContext(), GradePreferenceHelper.SelectionType.GRADE
        ) ?: "Grade 1"
        selectedLang = (PrefUtilsManager.getFromPrefs(requireActivity(), LANG, "English")
            ?: "English").toString()
        cltVideos =
            PrefUtilsManager.getFromPrefs(requireActivity(), Constant.CLTVIDEO, "").toString()

        Log.i("IndexPageFragment", "onCreate: " + cltVideos)
        Log.i("IndexPageFragment", "onCreate:11 " + selectedSubject)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentVideoBinding.inflate(inflater, container, false)
//        egrJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize chapterAdapter with an empty list or based on the initial/default subject
        updateChaptersForSubject(selectedSubject)
//        val images = listOf(
//            R.drawable.image1,
//        )
        // Fix: Use the class member `childrenOfChap` which is of type ArrayList<Child>
        // val titles: List<String> = childrenOfChap.map { it.name } // This seems unused, consider removing

        chapterAdapter = ChapterDetailsAdapter(chapterdetails, position?:0) // Assuming you want to pass the list of Child objects
        binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.videoRecyclerView.adapter = chapterAdapter

    }

    private fun updateChaptersForSubject(subjectName: String?) {
        Log.i("VideoFragment5To10", "updateChaptersForSubject: subject = $subjectName, position = $position")

        // Fetch chapters for the given subjectName from the map
        val chaptersForSubject = subjectChaptersMap[subjectName] ?: emptyList()
        Log.i("VideoFragment5To10", "updateChaptersForSubject: Found ${chaptersForSubject.size} chapters for subject")

        // Clear the existing list
        childrenOfChap.clear()

        // Populate only selected chapter's content
        populateSelectedChapterOnly(chaptersForSubject)

        // If the adapter is already initialized, notify it of the data change
        if (::chapterAdapter.isInitialized) {
            chapterAdapter.notifyDataSetChanged()
            Log.i("VideoFragment5To10", "updateChaptersForSubject: Adapter updated with ${childrenOfChap.size} items")
        } else {
            // If the adapter is not initialized, create it with the new data
            chapterAdapter = ChapterDetailsAdapter(childrenOfChap, position?:0)
            binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
            binding.videoRecyclerView.adapter = chapterAdapter
            Log.i("VideoFragment5To10", "updateChaptersForSubject: New adapter created with ${childrenOfChap.size} items")
        }
    }

    /**
     * Populate childrenOfChap with ONLY MP4 videos from selected chapter
     */
    private fun populateSelectedChapterOnly(chaptersForSubject: List<Child>) {
        Log.i("VideoFragment5To10", "populateSelectedChapterOnly: position = $position, filtering for MP4 videos only")

        val selectedChapterContent = when {
            // Method 1: If position is specified, get that specific chapter's content
            position != null && position!! >= 0 && position!! < chaptersForSubject.size -> {
                val selectedChapter = chaptersForSubject[position!!]
                Log.i("VideoFragment5To10", "populateSelectedChapterOnly: Selected chapter: ${selectedChapter.name}")

                // Get ONLY the content from the selected chapter
                val chapterContent = selectedChapter.children ?: emptyList()
                Log.i("VideoFragment5To10", "populateSelectedChapterOnly: Found ${chapterContent.size} total items in selected chapter")
                chapterContent
            }
            // Method 2: Default to first chapter if no position specified
            chaptersForSubject.isNotEmpty() -> {
                val firstChapter = chaptersForSubject[0]
                Log.i("VideoFragment5To10", "populateSelectedChapterOnly: Using first chapter: ${firstChapter.name}")
                val chapterContent = firstChapter.children ?: emptyList()
                Log.i("VideoFragment5To10", "populateSelectedChapterOnly: Found ${chapterContent.size} total items in first chapter")
                chapterContent
            }
            // Method 3: No chapters available
            else -> {
                Log.w("VideoFragment5To10", "populateSelectedChapterOnly: No chapters available")
                emptyList()
            }
        }

        // Filter for MP4 videos only
        val mp4Videos = filterMp4VideosOnly(selectedChapterContent)

        // Add ONLY the MP4 videos to childrenOfChap
        childrenOfChap.addAll(mp4Videos)

        // Log the filtered content
        Log.i("VideoFragment5To10", "populateSelectedChapterOnly: Filtered to ${mp4Videos.size} MP4 videos from ${selectedChapterContent.size} total items")
        mp4Videos.forEachIndexed { index, video ->
            Log.i("VideoFragment5To10", "populateSelectedChapterOnly: MP4Video[$index]: ${video.name} (type: ${video.type})")
        }
    }

    /**
     * Filter to get ONLY MP4 videos from the content list
     */
    private fun filterMp4VideosOnly(contentList: List<Child>): List<Child> {
        Log.i("VideoFragment5To10", "filterMp4VideosOnly: Filtering ${contentList.size} items for MP4 videos")

        val mp4Videos = contentList.filter { item ->
            val fileName = item.name?.lowercase() ?: ""
            val isMP4Video = fileName.endsWith(".mp4")

            if (isMP4Video) {
                Log.i("VideoFragment5To10", "filterMp4VideosOnly: Including MP4 video: ${item.name}")
            } else {
                Log.i("VideoFragment5To10", "filterMp4VideosOnly: Excluding non-MP4 item: ${item.name}")
            }

            isMP4Video
        }

        Log.i("VideoFragment5To10", "filterMp4VideosOnly: Found ${mp4Videos.size} MP4 videos out of ${contentList.size} total items")
        return mp4Videos
    }

    /**
     * Filter for multiple video formats (MP4, AVI, MOV, etc.)
     */
    private fun filterAllVideoFormats(contentList: List<Child>): List<Child> {
        Log.i("VideoFragment5To10", "filterAllVideoFormats: Filtering ${contentList.size} items for all video formats")

        val videoExtensions = listOf(".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm")

        val allVideos = contentList.filter { item ->
            val fileName = item.name?.lowercase() ?: ""
            val isVideo = videoExtensions.any { extension -> fileName.endsWith(extension) }

            if (isVideo) {
                Log.i("VideoFragment5To10", "filterAllVideoFormats: Including video: ${item.name}")
            } else {
                Log.i("VideoFragment5To10", "filterAllVideoFormats: Excluding non-video item: ${item.name}")
            }

            isVideo
        }

        Log.i("VideoFragment5To10", "filterAllVideoFormats: Found ${allVideos.size} videos out of ${contentList.size} total items")
        return allVideos
    }

    /**
     * Public method to update the selected chapter position
     * This will populate childrenOfChap with only the selected chapter's content
     */
    fun updateSelectedChapterPosition(chapterPosition: Int) {
        Log.i("VideoFragment5To10", "updateSelectedChapterPosition: $chapterPosition")
        position = chapterPosition
        updateChaptersForSubject(selectedSubject)
    }

    /**
     * Public method to filter and show only MP4 videos
     */
    fun showMp4VideosOnly() {
        Log.i("VideoFragment5To10", "showMp4VideosOnly: Filtering for MP4 videos only")
        updateChaptersForSubject(selectedSubject)
    }

    /**
     * Public method to filter and show all video formats
     */
    fun showAllVideoFormats() {
        Log.i("VideoFragment5To10", "showAllVideoFormats: Filtering for all video formats")

        // Temporarily modify the filtering logic to use all video formats
        val chaptersForSubject = subjectChaptersMap[selectedSubject] ?: emptyList()

        val selectedChapterContent = when {
            position != null && position!! >= 0 && position!! < chaptersForSubject.size -> {
                chaptersForSubject[position!!].children ?: emptyList()
            }
            chaptersForSubject.isNotEmpty() -> {
                chaptersForSubject[0].children ?: emptyList()
            }
            else -> emptyList()
        }

        // Clear and add all video formats
        childrenOfChap.clear()
        val allVideos = filterAllVideoFormats(selectedChapterContent)
        childrenOfChap.addAll(allVideos)

        // Update adapter
        if (::chapterAdapter.isInitialized) {
            chapterAdapter.notifyDataSetChanged()
        }
    }

    /**
     * Public method to show specific video format
     */
    fun showSpecificVideoFormat(extension: String) {
        Log.i("VideoFragment5To10", "showSpecificVideoFormat: Filtering for $extension videos")

        val chaptersForSubject = subjectChaptersMap[selectedSubject] ?: emptyList()

        val selectedChapterContent = when {
            position != null && position!! >= 0 && position!! < chaptersForSubject.size -> {
                chaptersForSubject[position!!].children ?: emptyList()
            }
            chaptersForSubject.isNotEmpty() -> {
                chaptersForSubject[0].children ?: emptyList()
            }
            else -> emptyList()
        }

        // Filter for specific extension
        val specificVideos = selectedChapterContent.filter { item ->
            val fileName = item.name?.lowercase() ?: ""
            fileName.endsWith(extension.lowercase())
        }

        // Clear and add specific format videos
        childrenOfChap.clear()
        childrenOfChap.addAll(specificVideos)

        Log.i("VideoFragment5To10", "showSpecificVideoFormat: Found ${specificVideos.size} $extension videos")

        // Update adapter
        if (::chapterAdapter.isInitialized) {
            chapterAdapter.notifyDataSetChanged()
        }
    }

    /**
     * Get current chapter and video information
     */
    fun getCurrentChapterInfo(): Triple<String?, Int?, Int> {
        return Triple(selectedSubject, position, childrenOfChap.size)
    }

    /**
     * Get video statistics for current chapter
     */
    fun getVideoStatistics(): Map<String, Int> {
        val chaptersForSubject = subjectChaptersMap[selectedSubject] ?: emptyList()

        val selectedChapterContent = when {
            position != null && position!! >= 0 && position!! < chaptersForSubject.size -> {
                chaptersForSubject[position!!].children ?: emptyList()
            }
            chaptersForSubject.isNotEmpty() -> {
                chaptersForSubject[0].children ?: emptyList()
            }
            else -> emptyList()
        }

        val stats = mutableMapOf<String, Int>()
        stats["total_items"] = selectedChapterContent.size
        stats["mp4_videos"] = selectedChapterContent.count { it.name?.lowercase()?.endsWith(".mp4") == true }
        stats["all_videos"] = selectedChapterContent.count { item ->
            val fileName = item.name?.lowercase() ?: ""
            listOf(".mp4", ".avi", ".mov", ".mkv", ".wmv").any { fileName.endsWith(it) }
        }
        stats["non_videos"] = selectedChapterContent.size - stats["all_videos"]!!

        Log.i("VideoFragment5To10", "getVideoStatistics: $stats")
        return stats
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

//    companion
//    object {
//        private const val ARG_SUBJECT = "subject"
//
//        fun newInstance(subject: String?): VideoFragment5To10 {
//            val fragment = VideoFragment5To10()
//            val bundle = Bundle()
//            bundle.putString(ARG_SUBJECT, subject)
//            fragment.arguments = bundle
//            return fragment
//        }
//    }
companion object {
    private const val ARG_SUBJECT = "subject"
    private const val CHAP_DETAILS = "chap_details"
    private const val POSTION = "pos"

    fun newInstance(subject: String, pos: Int?, chapDetails: String?): VideoFragment5To10 {
        val fragment = VideoFragment5To10()
        val bundle = Bundle()
        bundle.putString(ARG_SUBJECT, subject)
        if (pos != null) {
            bundle.putInt(POSTION, pos)
        }
        bundle.putString(CHAP_DETAILS, chapDetails)
        fragment.arguments = bundle
        return fragment
    }
}

    private fun generateSubjectChaptersMap(): Map<String, List<Child>> {
        val map = mutableMapOf<String, List<Child>>()
        Log.i("VideoFragment5To10", "generateSubjectChaptersMap: Starting to generate subject chapters map")

        try {
            val gson = Gson()

            // Check if cltVideos is valid
            if (cltVideos == null) {
                Log.e("VideoFragment5To10", "generateSubjectChaptersMap: cltVideos is null")
                return emptyMap()
            }

            // Convert to string and check format
            val cltVideosString = cltVideos.toString()
            Log.i("VideoFragment5To10", "generateSubjectChaptersMap: cltVideos string length: ${cltVideosString.length}")
            Log.i("VideoFragment5To10", "generateSubjectChaptersMap: cltVideos starts with: ${cltVideosString.take(50)}")

            // Parse the top-level structure
            val parsedCltVideos = try {
                gson.fromJson(cltVideosString, Root::class.java)
            } catch (e: JsonSyntaxException) {
                Log.e("VideoFragment5To10", "generateSubjectChaptersMap: JSON parsing error: ${e.message}")
                Log.e("VideoFragment5To10", "generateSubjectChaptersMap: Problematic JSON: ${cltVideosString.take(200)}")
                return emptyMap()
            } catch (e: Exception) {
                Log.e("VideoFragment5To10", "generateSubjectChaptersMap: Unexpected parsing error: ${e.message}")
                return emptyMap()
            }

            if (parsedCltVideos?.usbDriveContents == null) {
                Log.e("VideoFragment5To10", "generateSubjectChaptersMap: Parsed data is null or has no usbDriveContents")
                return emptyMap()
            }

            Log.i("VideoFragment5To10", "generateSubjectChaptersMap: Successfully parsed, found ${parsedCltVideos.usbDriveContents.size} USB drive contents")
        subjectNames = mutableListOf<String>()
        for (usbDriveContent in cltVideos.usbDriveContents) {
            for (gradeLevel in usbDriveContent.children) {
                for ((index, subject) in gradeLevel.children.withIndex()) {
                    // Ensure subject has children and index is valid before accessing
//                    Log.i("IndexPageFragment", "getGrade!!!! " + subject.children[index].name)
                    Log.i("IndexPageFragment", "getSub..... " + gradeLevel.children[index].name)
                    Log.i("IndexPageFragment", "getSub.. " + selectedLang)
                    if (gradeLevel.children[index].name == selectedLang) {

                        for ((topicIndex, topic) in subject.children.withIndex()) {
                            Log.i("IndexPageFragment", "getGrade..... " + gradeLevel.children[index].name)
                            Log.i("IndexPageFragment", "getGrade.. " + selectedLang)
                            if (subject.children[topicIndex].name == selectedGrade) {
                                Log.i("IndexPageFragment", "getGrade!!..... " + gradeLevel.children[index].name)
                                Log.i("IndexPageFragment", "getGrade!!!!.. " + selectedLang)
                                for (subTopic in topic.children) {
                                    val testVal = subTopic.name
                                    subjectNames.add(subTopic.name)
                                    Log.i("IndexPageFragment", "getSubject:.. " + testVal)
                                    val currentSubjectChapters = mutableListOf<Child>()
                                    subTopic.children?.forEach { chapterContent -> // These are the actual chapters

                                        chapterContent.children?.let {
                                            for ((chapterIndex, chapter) in it.withIndex()) {

                                                if (chapter.type == "DIR") {
                                                    currentSubjectChapters.add(chapter)

                                                    chaptersList.add(chapter)

                                                    // Populate only selected chapter's children
                                                    if (position != null && chapterIndex == position) {
                                                        Log.i("VideoFragment5To10", "Populating selected chapter: ${chapter.name} at position $position")
                                                        childrenOfChap.clear()
                                                        childrenOfChap.addAll(chapter.children ?: emptyList())
                                                        Log.i("VideoFragment5To10", "Added ${childrenOfChap.size} items from selected chapter")
                                                    }
                                                }
                                                Log.i(
                                                    "IndexPageFragment",
                                                    "getSubject&&: Chapter $chapterIndex - ${chapter.name} for Subject $testVal"
                                                )
                                            }

                                        }
//                                            }
//                                        }
                                    }
                                    Log.i("IndexPageFragment", "getSubject:@@ " + chaptersList)
//                                    map[subTopic.name] = chaptersList
                                    // Store the chapters specifically for this subject
                                    map[subTopic.name] = currentSubjectChapters
                                }
                            }


                        }
                    }
                }
            }
        }
        AnalyticsUtils.logLargeString(map.toString())
        return map
    }
    private val subjectChaptersMap: Map<String, List<Child>> by lazy { generateSubjectChaptersMap() }


}
