package clt.india.classroom.ui.egr

import android.os.Bundle
import android.util.Log
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.OneToFourChapterContentPagerAdapter
import clt.india.classroom.adapter.SubjectTabAdapter
import clt.india.classroom.databinding.ActivityHomeBinding
import clt.india.classroom.databinding.ActivityOneToFourHomeBinding
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils
import com.google.android.material.tabs.TabLayoutMediator


class OneToFourHomeActivity : AppCompatActivity() {
    private lateinit var binding: ActivityOneToFourHomeBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOneToFourHomeBinding.inflate(layoutInflater)
        val subjectName = intent.getStringExtra("subjectName")
        val chapDetails = intent.getStringExtra("chap_details")
        val position = intent.getIntExtra("pos",0)

        Log.i("OneToFourHomeActivity", "onCreate:222 "+position)
        supportActionBar?.hide()
        enableEdgeToEdge()


        setContentView(binding.root)
        if (savedInstanceState == null) {
            val fragment = OneToFourHomePageFragment().apply {
                arguments = Bundle().apply {
                    // Pass data to the fragment using Bundle
                    // This is how you would fetch it in OneToFourHomePageFragment:
                    // val subjectName = arguments?.getString("subjectName")
                    // val chapDetails = arguments?.getString("chap_details")                    putString("subjectName", subjectName)
                    putString("chap_details", chapDetails)
                    putInt("pos", position)

                }
            }
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit()
        }

    }


}