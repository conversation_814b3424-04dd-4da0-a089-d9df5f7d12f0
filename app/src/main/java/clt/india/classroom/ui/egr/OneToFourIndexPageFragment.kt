package clt.india.classroom.ui.egr

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.OneToFourChapterAdapter
import clt.india.classroom.adapter.SubjectTabAdapterNew
import clt.india.classroom.data.api.response.egl.EGLData
import clt.india.classroom.databinding.FragmentOneToFourIndexPageBinding
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.utils.AnalyticsUtils
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.PrefUtilsManager
import com.google.gson.Gson
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils


class OneToFourIndexPageFragment : Fragment() {
    private var _binding: FragmentOneToFourIndexPageBinding? = null
    private val binding get() = _binding!!
    private var subjectNames: String? = null
    private var selectedSubject: String? = null
    private lateinit var chapterAdapter: OneToFourChapterAdapter
    var eglJson : String? = null
    var defaultSubject : String? = null
    var seriesName : String? = "Series 1"
    private var selectedGrade: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedGrade = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.GRADE)
            ?: "Grade 1"

        Log.i("OneToFourIndexPageFragment", "onCreate:ss "+selectedGrade)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentOneToFourIndexPageBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Track screen view
        AnalyticsUtils.logScreenView("OneToFourIndexPageFragment")

        eglJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()

        Log.i("OneToFourIndexPageFragment", "onViewCreated: selectedGrade = $selectedGrade")
        Log.i("OneToFourIndexPageFragment", "onViewCreated: eglJson length = ${eglJson?.length ?: 0}")
        Log.i("OneToFourIndexPageFragment", "onViewCreated: eglJson preview = ${eglJson?.take(200) ?: "null"}")

        binding.gradeSelect.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        setupSubjectTabs()
        setupChapterRecyclerView()
        handleClicks()

        // Debug data availability
        debugDataAvailability()
    }
    private fun filterChapters(query: String) {
        val chapters = subjectChaptersMap[selectedSubject ?: ""] ?: emptyList()
        val filtered = chapters.filter {
            it.chapterNumber.contains(query, ignoreCase = true) ||
                    it.chapterTitle.contains(query, ignoreCase = true)
        }

        chapterAdapter.updateChapters(filtered)
    }
    private fun handleClicks() {
        binding.searchVideo.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {
                val query = binding.searchVideo.text.toString().trim()
                filterChapters(query)
                hideKeyboard()
                true
            } else {
                false
            }
        }

        binding.searchFun.setOnClickListener {
            val query = binding.searchVideo.text.toString().trim()
            filterChapters(query)
            hideKeyboard()
        }
        binding.chnageGrade.setOnClickListener{
            findNavController().navigateUp()
        }
    }
    private fun hideKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchVideo.windowToken, 0)
    }
    private fun setupSubjectTabs() {
        Log.i("OneToFourIndexPageFragment", "setupSubjectTabs: eglJson = $eglJson")

        // Check if JSON data exists
        if (eglJson.isNullOrEmpty() || eglJson == "null") {
            Log.e("OneToFourIndexPageFragment", "EGL JSON data is null or empty")
            return
        }

        try {
            val gson = Gson()
            val eglData = gson.fromJson(eglJson, EGLData::class.java)
            val subjectNames = mutableListOf<String>()

            // Determine the series name based on the selected grade
            seriesName = when (selectedGrade) {
                "Grade 1" -> "SERIES 1"
                "Grade 2" -> "SERIES 2"
                "Grade 3" -> "SERIES 3"
                "Grade 4" -> "SERIES 4"
                else -> "SERIES 1" // Default
            }

            Log.i("OneToFourIndexPageFragment", "Looking for series: $seriesName for grade: $selectedGrade")

            eglData.usbDriveContents?.forEach { root ->
                Log.i("OneToFourIndexPageFragment", "Processing root: ${root.name}")

                root.children?.forEach { series ->
                    Log.i("OneToFourIndexPageFragment", "Found series: ${series.name}")

                    if (series.name == seriesName) {
                        Log.i("OneToFourIndexPageFragment", "Matched series: ${series.name}")

                        // Add the series name as the subject (since it's "Foundational Literacy" content)
                        if (!subjectNames.contains("Foundational Literacy")) {
                            subjectNames.add("Foundational Literacy")
                        }
                    }
                }
            }

            Log.i("OneToFourIndexPageFragment", "Subject names found: $subjectNames")

            if (subjectNames.isNotEmpty()) {
                selectedSubject = subjectNames[0] // Set default selected subject
                val adapter = SubjectTabAdapterNew(subjectNames) { subject ->
                    selectedSubject = subject
                    updateChaptersForSubject(subject)
                }
                binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
                binding.subjectTabsRecyclerView.adapter = adapter
            } else {
                Log.w("OneToFourIndexPageFragment", "No subjects found for grade: $selectedGrade")
                // Fallback: Create default subject
                createFallbackSubjects()
            }
        } catch (e: Exception) {
            Log.e("OneToFourIndexPageFragment", "Error parsing EGL data: ${e.message}", e)
        }
    }
    private fun setupChapterRecyclerView() {
        defaultSubject = selectedSubject ?: "Foundational Literacy"

        Log.i("OneToFourIndexPageFragment", "setupChapterRecyclerView: defaultSubject = $defaultSubject")

        // Initialize chapterAdapter with an empty list initially
        chapterAdapter = OneToFourChapterAdapter(emptyList())
        binding.chaptersRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.chaptersRecyclerView.adapter = chapterAdapter

        // Load chapters for the default or initially selected subject
        updateChaptersForSubject(defaultSubject.toString())
    }

    private fun updateChaptersForSubject(subjectName: String) {
        Log.i("OneToFourIndexPageFragment", "updateChaptersForSubject: $subjectName")
        val chapters = subjectChaptersMap[subjectName] ?: emptyList()
        Log.i("OneToFourIndexPageFragment", "updateChaptersForSubject: Found ${chapters.size} chapters for $subjectName")
        chapterAdapter.updateChapters(chapters)
    }


    private fun generateSubjectChaptersMap(): Map<String, List<ChapterNew>> {
        val map = mutableMapOf<String, List<ChapterNew>>()

        if (eglJson.isNullOrEmpty() || eglJson == "null") {
            Log.e("OneToFourIndexPageFragment", "Cannot generate chapters map - EGL JSON is null or empty")
            return map
        }

        try {
            val gson = Gson()
            val eglData = gson.fromJson(eglJson, EGLData::class.java)

            Log.i("OneToFourIndexPageFragment", "selctedGrade.. "+selectedGrade)

            // Determine the series name based on the selected grade
            val targetSeriesName = when (selectedGrade) {
                "1" -> "SERIES 1"
                "2" -> "SERIES 2"
                "3" -> "SERIES 3"
                "4" -> "SERIES 4"
                else -> "SERIES 1"
            }

            Log.i("OneToFourIndexPageFragment", "generateSubjectChaptersMap: Looking for series: $targetSeriesName")

            eglData.usbDriveContents?.forEach { root ->
                Log.i("OneToFourIndexPageFragment", "generateSubjectChaptersMap: Processing root: ${root.name}")

                root.children?.forEach { series ->
                    Log.i("OneToFourIndexPageFragment", "generateSubjectChaptersMap: Found series: ${series.name}")

                    if (series.name == targetSeriesName) {
                        Log.i("OneToFourIndexPageFragment", "generateSubjectChaptersMap: Matched target series: ${series.name}")

                        val chaptersList = mutableListOf<ChapterNew>()
                        series.children?.forEachIndexed { index, chapterContent ->
                            val chapterName = chapterContent.name ?: "Unnamed Chapter"
                            val chapterNumber = "Chapter ${index + 1}"

                            Log.i("OneToFourIndexPageFragment", "generateSubjectChaptersMap: Adding chapter: $chapterNumber - $chapterName")

                            chaptersList.add(
                                ChapterNew(
                                    chapterNumber = chapterNumber,
                                    chapterTitle = chapterName,
                                    ChapterContent = "No description",
                                    children = chapterContent.children
                                )
                            )
                        }

                        // Map to "Foundational Literacy" as the subject name
                        map["Foundational Literacy"] = chaptersList
                        Log.i("OneToFourIndexPageFragment", "generateSubjectChaptersMap: Added ${chaptersList.size} chapters for Foundational Literacy")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("OneToFourIndexPageFragment", "Error generating chapters map: ${e.message}", e)
        }

        return map
    }


    private val subjectChaptersMap: Map<String, List<ChapterNew>> by lazy { generateSubjectChaptersMap() }

    // Debug method to check data availability
    private fun debugDataAvailability() {
        Log.i("OneToFourIndexPageFragment", "=== DEBUG DATA AVAILABILITY ===")
        Log.i("OneToFourIndexPageFragment", "selectedGrade: $selectedGrade")
        Log.i("OneToFourIndexPageFragment", "seriesName: $seriesName")
        Log.i("OneToFourIndexPageFragment", "selectedSubject: $selectedSubject")
        Log.i("OneToFourIndexPageFragment", "eglJson is null: ${eglJson == null}")
        Log.i("OneToFourIndexPageFragment", "eglJson is empty: ${eglJson?.isEmpty() ?: true}")
        Log.i("OneToFourIndexPageFragment", "subjectChaptersMap size: ${subjectChaptersMap.size}")
        Log.i("OneToFourIndexPageFragment", "subjectChaptersMap keys: ${subjectChaptersMap.keys}")
        subjectChaptersMap.forEach { (key, value) ->
            Log.i("OneToFourIndexPageFragment", "Subject '$key' has ${value.size} chapters")
        }
        Log.i("OneToFourIndexPageFragment", "=== END DEBUG ===")
    }

    // Fallback method when no data is found
    private fun createFallbackSubjects() {
        Log.i("OneToFourIndexPageFragment", "Creating fallback subjects")
        val fallbackSubjects = listOf("Foundational Literacy")
        selectedSubject = fallbackSubjects[0]

        val adapter = SubjectTabAdapterNew(fallbackSubjects) { subject ->
            selectedSubject = subject
            updateChaptersForSubject(subject)
        }
        binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        binding.subjectTabsRecyclerView.adapter = adapter
    }
}