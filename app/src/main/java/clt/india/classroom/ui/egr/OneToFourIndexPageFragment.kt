package clt.india.classroom.ui.egr

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.OneToFourChapterAdapter
import clt.india.classroom.adapter.SubjectTabAdapterNew
import clt.india.classroom.data.api.response.egl.EGLData
import clt.india.classroom.databinding.FragmentOneToFourIndexPageBinding
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.PrefUtilsManager
import com.google.gson.Gson
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.GradeUtils


class OneToFourIndexPageFragment : Fragment() {
    private var _binding: FragmentOneToFourIndexPageBinding? = null
    private val binding get() = _binding!!
    private var subjectNames: String? = null
    private var selectedSubject: String? = null
    private lateinit var chapterAdapter: OneToFourChapterAdapter
    var eglJson : String? = null
    var defaultSubject : String? = null
    var seriesName : String? = "Series 1"
    private var selectedGrade: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedGrade = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.GRADE)
            ?: "Grade 1"

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentOneToFourIndexPageBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        eglJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()

        Log.i("onViewCreated ", "onViewCreated: "+eglJson)
        binding.gradeSelect.text = GradeUtils.getOrdinalGrade(selectedGrade!!)
        setupSubjectTabs()
        setupChapterRecyclerView()
        handleClicks()
    }
    private fun filterChapters(query: String) {
        val chapters = subjectChaptersMap[selectedSubject ?: ""] ?: emptyList()
        val filtered = chapters.filter {
            it.chapterNumber.contains(query, ignoreCase = true) ||
                    it.chapterTitle.contains(query, ignoreCase = true)
        }

        chapterAdapter.updateChapters(filtered)
    }
    private fun handleClicks() {
        binding.searchVideo.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {
                val query = binding.searchVideo.text.toString().trim()
                filterChapters(query)
                hideKeyboard()
                true
            } else {
                false
            }
        }

        binding.searchFun.setOnClickListener {
            val query = binding.searchVideo.text.toString().trim()
            filterChapters(query)
            hideKeyboard()
        }
        binding.chnageGrade.setOnClickListener{
            findNavController().navigateUp()
        }
    }
    private fun hideKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchVideo.windowToken, 0)
    }
    private fun setupSubjectTabs() {

        Log.i(" json data 3 :", "eglsh: "+eglJson)
        val gson = Gson()
        // Parse the top-level structure which is a list of UsbDriveContent items
        val eglData = gson.fromJson(eglJson.toString(), EGLData::class.java)
        val subjectNames = mutableListOf<String>()

        eglData.usbDriveContents?.forEach { root ->
            Log.i("TAG", "json root:.. " + root.name) // This is the class name like "Class 1"
            // Determine the series index based on the selected grade
            val seriesIndex = when (selectedGrade) {
                "Grade 1" -> 0 // Series 1
                "Grade 2" -> 1 // Series 2
                "Grade 3" -> 2 // Series 3
                "Grade 4" -> 3 // Series 4
                // Add more cases for other grades if needed
                else -> -1 // Default or error case
            }
            eglData.usbDriveContents?.forEach { root ->
                Log.i("TAG", "json root:.. " + root.name) // This is the class name like "Class 1"
                // Determine the series index based on the selected grade
                 seriesName = when (selectedGrade) {
                    "Grade 1" -> "SERIES 1" // Series 1
                    "Grade 2" -> "SERIES 2" // Series 2
                    "Grade 3" -> "SERIES 3" // Series 3
                    "Grade 4" -> "SERIES 4" // Series 4
                    // Add more cases for other grades if needed
                    else -> "Series 1" // Default or error case
                }
                }

                val selectedSeries = root.children[seriesIndex]
                Log.i("TAG", "json root@@@@ " + selectedSeries.name)
                selectedSeries.name?.let {
                    Log.i("TAG", "Processing subject '$it' for $selectedGrade (Series ${seriesIndex + 1})")
                    if (!subjectNames.contains(it)) { // Avoid adding duplicate subject names if structure allows
                        subjectNames.add("Foundational Literacy")
                    }
                }
        }

        if (subjectNames.isNotEmpty()) {
            val adapter = SubjectTabAdapterNew(subjectNames) { subject ->
                selectedSubject = subject
                updateChaptersForSubject(subject)
            }
            binding.subjectTabsRecyclerView.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            binding.subjectTabsRecyclerView.adapter = adapter
        }
    }
    private fun setupChapterRecyclerView() {
        defaultSubject = selectedSubject ?: seriesName

        // Initialize chapterAdapter with an empty list or default chapters
        chapterAdapter = OneToFourChapterAdapter(generateSubjectChaptersMap().get(selectedSubject) ?: emptyList()) // Or provide default chapters
        binding.chaptersRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.chaptersRecyclerView.adapter = chapterAdapter
        // Load chapters for the default or initially selected subject
        updateChaptersForSubject(defaultSubject.toString())
    }

    private fun updateChaptersForSubject(subjectName: String) {
        val chapters = subjectChaptersMap[subjectName] ?: emptyList()
        chapterAdapter.updateChapters(chapters)
    }


    private fun generateSubjectChaptersMap(): Map<String, List<ChapterNew>> {
        val map = mutableMapOf<String, List<ChapterNew>>()
        val gson = Gson()
        val eglData = gson.fromJson(eglJson.toString(), EGLData::class.java)

        eglData.usbDriveContents?.forEach { root -> // e.g., "Class 1"
            Log.i("TAG", "generateSubjectChaptersMap: "+root.children.get(0).name)
            Log.i("TAG", "generateSubjectChaptersMap:.. "+root.children.get(1).name)
            root.children?.forEach { contentSeries -> // e.g., "Foundational Literacy" (Subject)
                val subjectName = contentSeries.name ?: "Unknown Subject"
                val chaptersList = mutableListOf<ChapterNew>()
                contentSeries.children?.forEach { chapterContent -> // These are the actual chapters
                    // Assuming chapterContent.name is like "Chapter 1 - Title"
                    // You might need to adjust parsing based on your actual data structure for chapter names/titles
                    val chapterName = chapterContent.name ?: "Unnamed Chapter"
                    // For simplicity, let's assume the name is the title and chapter number is part of it or needs extraction

                    chaptersList.add(ChapterNew(chapterName, "",  "No description",chapterContent.children)) // this need to update
                }
                map[subjectName] = chaptersList
            }
        }
        return map
    }


    private val subjectChaptersMap: Map<String, List<ChapterNew>> by lazy { generateSubjectChaptersMap() }
}