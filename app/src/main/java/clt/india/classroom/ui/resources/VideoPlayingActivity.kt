package clt.india.classroom.ui.resources

import Decrypt
import android.media.MediaPlayer
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import clt.india.classroom.R
import clt.india.classroom.databinding.ActivityVideoPlayingBinding
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.security.SecureRandom
import java.util.Calendar
import javax.crypto.Cipher
import javax.crypto.CipherOutputStream
import kotlin.concurrent.thread

import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec


class VideoPlayingActivity : AppCompatActivity() {
    private lateinit var binding: ActivityVideoPlayingBinding
    private lateinit var videoPlayer: MediaPlayer
    private val handler = Handler()
    var isFullscreen = false

    var path : String? = null
    var title : String? = null
    var  decryptedFilePath : String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)


        enableEdgeToEdge()
        path = intent.getStringExtra("chapter_vid_absolute_path")
        title = intent.getStringExtra("title")
        supportActionBar?.hide()
        binding = ActivityVideoPlayingBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.videoTitle.text = title
        handleClicks()
        decryptAndPlayVideo()

// Start the decryption process and get the path to the decrypted file
//         decryptedFilePath = decrypt.file!!
        Log.i("TAG", "onCreate:@@@@@@@@ "+decryptedFilePath+" "+decryptedFilePath!!.length)
    }
    private fun handleClicks() {
        binding.backButton.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
       /* binding.volumeButton.setOnClickListener {
            val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager

            isMuted = !isMuted

            if (isMuted) {
                audioManager.adjustStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    AudioManager.ADJUST_MUTE,
                    0
                )
                binding.volumeButton.setImageResource(R.drawable.ic_volume)
            } else {
                audioManager.adjustStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    AudioManager.ADJUST_UNMUTE,
                    0
                )
                binding.volumeButton.setImageResource(R.drawable.ic_volume)
            }
        }*/

        binding.fullScreen.setOnClickListener {
            if (!isFullscreen) {
                binding.videoView.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
                binding.videoView.layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                window.decorView.systemUiVisibility = (
                        View.SYSTEM_UI_FLAG_FULLSCREEN
                                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        )
                supportActionBar?.hide()
            } else {
                binding.videoView.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
                binding.videoView.layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
                supportActionBar?.show()
            }
            binding.videoView.requestLayout()
            isFullscreen = !isFullscreen
        }

        binding.forwardbutton.setOnClickListener {
            if (binding.videoView.duration > 0) {
                val pos = (binding.videoView.currentPosition + 10_000)
                    .coerceAtMost(binding.videoView.duration)
                binding.videoView.seekTo(pos)
            }
        }

        binding.replayButton.setOnClickListener {
            if (binding.videoView.duration > 0) {
                val pos = (binding.videoView.currentPosition - 10_000)
                    .coerceAtLeast(0)
                binding.videoView.seekTo(pos)
            }
        }
    }

    private fun decryptAndPlayVideo() {
        thread {
            // Initialize the Decrypt class with required parameters
            val decrypt = Decrypt().apply {
                // Set the encrypted file path
                fileName = path

                // Set the output directory where decrypted file will be saved
                val parentDir = File(path!!).parentFile
                outPutPath = parentDir?.absolutePath ?: ""
                ascii
                Log.i("TAG", "decryptAndPlayVideo: Decrypting in thread: ${Thread.currentThread().name}")
                Log.i("TAG", "decryptAndPlayVideo: Output path: $outPutPath, Length: ${outPutPath!!.length}")
            }

// Start the decryption process and get the path to the decrypted file
            decryptedFilePath = decrypt.file!!
            Log.i("VideoPlayingActivity", "decryptAndPlayVideo: Decrypted file path: $decryptedFilePath")

            runOnUiThread {
                playVideo()
                setupControls()
            }
        }
    }
    private fun playVideo() {
        binding.videoView.setVideoURI(decryptedFilePath.toUri())
        binding.videoView.setOnPreparedListener { mp ->
            videoPlayer = mp
            binding.videoView.start()
            binding.playPauseButton.setImageResource(R.drawable.ic_pause)
            updateSeekBar()
        }
        binding.videoView.setOnCompletionListener {
            binding.playPauseButton.setImageResource(R.drawable.ic_replay)
        }
    }

    private fun setupControls() {
        binding.playPauseButton.setOnClickListener {
            if (binding.videoView.isPlaying) {
                binding.videoView.pause()
                binding.playPauseButton.setImageResource(R.drawable.ic_pause)
            } else {
                binding.videoView.start()
                binding.playPauseButton.setImageResource(R.drawable.ic_pause)
            }
        }


        binding.seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(sb: SeekBar, prog: Int, fromUser: Boolean) {
                if (fromUser) binding.videoView.seekTo(prog)
            }
            override fun onStartTrackingTouch(sb: SeekBar) {}
            override fun onStopTrackingTouch(sb: SeekBar) {}
        })
    }

    private fun updateSeekBar() {
        val duration = binding.videoView.duration
        binding.seekBar.max = duration

        val runnable = object : Runnable {
            override fun run() {
                if (binding.videoView.isPlaying) {
                    val currentPos = binding.videoView.currentPosition
                    binding.seekBar.progress = currentPos

                    // Update time text
                    val current = formatTime(currentPos)
                    val total = formatTime(duration)
                    binding.timeText.text = "$current / $total"
                }
                handler.postDelayed(this, 500)
            }
        }
        handler.postDelayed(runnable, 0)
    }

    private fun formatTime(ms: Int): String {
        val seconds = ms / 1000 % 60
        val minutes = ms / 1000 / 60
        return String.format("%d:%02d", minutes, seconds)
    }

    private fun lastInd(): Char {
        val var1 = 'S'
        return var1
    }

    override fun onDestroy() {
        super.onDestroy()
        // Delete the decrypted file when the activity is destroyed
        if (decryptedFilePath.isNotEmpty()) {
            val decryptedFile = File(decryptedFilePath)
            if (decryptedFile.exists()) {
                decryptedFile.delete()
            }
        }
    }

}