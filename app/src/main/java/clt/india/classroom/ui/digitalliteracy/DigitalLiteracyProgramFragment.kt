package clt.india.classroom.ui.digitalliteracy

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.adapter.VideoChapterDetailsAdapter
import clt.india.classroom.databinding.FragmentDigitalLiteracyProgramBinding
import clt.india.classroom.utils.GradePreferenceHelper

class DigitalLiteracyProgramFragment : Fragment() {

    private var _binding: FragmentDigitalLiteracyProgramBinding? = null
    private val binding get() = _binding!!
    private lateinit var chapterAdapter: VideoChapterDetailsAdapter
    private lateinit var chapterAdapters: VideoChapterDetailsAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentDigitalLiteracyProgramBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val savedVersion = GradePreferenceHelper.getSelection(requireContext(), GradePreferenceHelper.SelectionType.VERSION)
        savedVersion?.let {
            binding.versionText.text = "($it)"
        }
        val titles = listOf(
            "Lorem Ipsum", "Lorem Ipsum"
        )
        val title = listOf(
            "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum",
            "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum",
            "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum",
            "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum"
        )
        chapterAdapter = VideoChapterDetailsAdapter(titles)
        chapterAdapters = VideoChapterDetailsAdapter(title)
        binding.recyclerViewChapters.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.recyclerViewChapters.adapter = chapterAdapter

        binding.recyclerViewChapterss.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.recyclerViewChapterss.adapter = chapterAdapters
        handleclicks()

    }

    private fun handleclicks() {
        val navController = findNavController()
        val action = DigitalLiteracyProgramFragmentDirections
            .actionDigitalLiteracyProgramFragmentToGradeSelectionFragment("digital")

        binding.backButton.setOnClickListener {
            navController.navigate(action)
        }

        binding.changeVersion.setOnClickListener {
            navController.navigate(action)
        }
    }


}