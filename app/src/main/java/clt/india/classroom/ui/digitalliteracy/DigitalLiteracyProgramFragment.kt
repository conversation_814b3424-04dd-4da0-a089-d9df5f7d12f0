package clt.india.classroom.ui.digitalliteracy

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import clt.india.classroom.adapter.VideoChapterDetailsAdapter
import clt.india.classroom.data.api.response.dlp.DlpVideo
import clt.india.classroom.databinding.FragmentDigitalLiteracyProgramBinding
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.GradePreferenceHelper
import clt.india.classroom.utils.PrefUtilsManager
import org.json.JSONObject
class DigitalLiteracyProgramFragment : Fragment() {

    private var _binding: FragmentDigitalLiteracyProgramBinding? = null
    private val binding get() = _binding!!
    private lateinit var chapterAdapter: VideoChapterDetailsAdapter
    private lateinit var chapterAdapters: VideoChapterDetailsAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentDigitalLiteracyProgramBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val selectedLang = PrefUtilsManager.getFromPrefs(requireContext(), Constant.DLP_SELECTED_LANGUAGE, "") as? String
        val selectedVersion = PrefUtilsManager.getFromPrefs(requireContext(), Constant.DLP_SELECTED_VERSION, "") as? String

        binding.versionText.text = selectedVersion ?: "Version not selected"
        binding.dlpVersionText.text = selectedVersion ?: "Version not selected"
        Log.d("DLP_DEBUG", "Selected Language: $selectedLang")
        Log.d("DLP_DEBUG", "Selected Version: $selectedVersion")

        val jsonString = PrefUtilsManager.getFromPrefs(requireContext(), Constant.DLP_LANGUAGES_KEY, "") as? String
        Log.d("DLP_DEBUG", "JSON String from prefs: $jsonString")

        val videoList = mutableListOf<DlpVideo>()

        if (!jsonString.isNullOrEmpty()) {
            try {
                val dlpJson = JSONObject(jsonString)
                val langArray = dlpJson.getJSONArray("languages")
                Log.d("DLP_DEBUG", "Languages array length: ${langArray.length()}")

                for (i in 0 until langArray.length()) {
                    val langObj = langArray.getJSONObject(i)
                    val langName = langObj.getString("name")
                    Log.d("DLP_DEBUG", "Checking language: $langName against selected: $selectedLang")

                    if (langName == selectedLang) {
                        Log.d("DLP_DEBUG", "Found matching language: $langName")
                        val versions = langObj.getJSONArray("versions")
                        Log.d("DLP_DEBUG", "Versions array length: ${versions.length()}")

                        for (j in 0 until versions.length()) {
                            val versionObj = versions.getJSONObject(j)
                            val versionName = versionObj.getString("name")
                            Log.d("DLP_DEBUG", "Checking version: $versionName against selected: $selectedVersion")

                            if (versionName == selectedVersion) {
                                Log.d("DLP_DEBUG", "Found matching version: $versionName")
                                val videoArray = versionObj.getJSONArray("videos")
                                Log.d("DLP_DEBUG", "Videos array length: ${videoArray.length()}")

                                for (k in 0 until videoArray.length()) {
                                    val videoJson = videoArray.getJSONObject(k)
                                    val videoName = videoJson.getString("name")
                                    val videoPath = videoJson.getString("absolute_path")
                                    Log.d("DLP_DEBUG", "Adding video: $videoName at path: $videoPath")
                                    // Updated to match the new data class constructor
                                    videoList.add(DlpVideo(videoName, videoPath))
                                }
                                break
                            }
                        }
                        break // Found the language, no need to continue
                    }
                }
            } catch (e: Exception) {
                Log.e("DLP_DEBUG", "Error parsing JSON: ${e.message}", e)
            }
        } else {
            Log.e("DLP_DEBUG", "JSON string is null or empty!")
        }

        Log.d("DLP_DEBUG", "Final video list size: ${videoList.size}")
        videoList.forEach { video ->
            Log.d("DLP_DEBUG", "Video in list: ${video.name} - ${video.absolutePath}")
        }

        binding.recyclerViewChapters.layoutManager = GridLayoutManager(requireContext(), 3)
        chapterAdapter = VideoChapterDetailsAdapter(videoList)
        binding.recyclerViewChapters.adapter = chapterAdapter

        // ADD: Notify adapter about data change
        binding.recyclerViewChapters.post {
            chapterAdapter.notifyDataSetChanged()
        }

        handleclicks()
    }



    private fun handleclicks() {
        val navController = findNavController()
        val action = DigitalLiteracyProgramFragmentDirections
            .actionDigitalLiteracyProgramFragmentToGradeSelectionFragment("digital")

        binding.backButton.setOnClickListener {
            navController.navigate(action)
        }

        binding.changeVersion.setOnClickListener {
            navController.navigate(action)
        }
    }


}

