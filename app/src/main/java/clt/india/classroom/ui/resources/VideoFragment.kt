package clt.india.classroom.ui.resources

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import clt.india.classroom.R
import clt.india.classroom.adapter.ChapterDetailsAdapter
import clt.india.classroom.data.api.response.egl.EGLData
import clt.india.classroom.databinding.FragmentVideoBinding
import clt.india.classroom.data.api.response.Chapter
import clt.india.classroom.data.api.response.ChapterNew
import clt.india.classroom.utils.Constant
import clt.india.classroom.utils.PrefUtilsManager
import com.google.gson.Gson


class VideoFragment : Fragment() {

    private var _binding: FragmentVideoBinding? = null
    private val binding get() = _binding!!
    var egrJson : String? = null

    private lateinit var chapterAdapter: ChapterDetailsAdapter
    private var selectedSubject: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedSubject = it.getString(ARG_SUBJECT)
            Log.i("VideoFragment", "onCreate:ar1 "+selectedSubject)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentVideoBinding.inflate(inflater, container, false)
        egrJson = PrefUtilsManager.getFromPrefs(requireActivity(), Constant.EGL_COMPLETE_JSON,"").toString()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        generateSubjectChaptersMap()
        val images = listOf(
            R.drawable.image1, R.drawable.image1, R.drawable.image1,
            R.drawable.image1, R.drawable.image1, R.drawable.image1,
            R.drawable.image1, R.drawable.image1, R.drawable.image1
        )

        val titles = listOf(
            "Chapter 1", "Chapter 2", "Chapter 3",
            "Chapter 4", "Chapter 5", "Chapter 6",
            "Chapter 5", "Chapter 6", "Chapter 7",

            )

//        chapterAdapter = ChapterDetailsAdapter( titles)
//
//        binding.videoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
//        binding.videoRecyclerView.adapter = chapterAdapter

        println("DEBUG: Adapter set with ${titles.size} items")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_SUBJECT = "subject"

        fun newInstance(subject: String?): VideoFragment {
            val fragment = VideoFragment()
            val bundle = Bundle()
            bundle.putString(ARG_SUBJECT, subject)
            fragment.arguments = bundle
            return fragment
        }
    }

    private fun generateSubjectChaptersMap(): Map<String, List<ChapterNew>> {
        val map = mutableMapOf<String, List<ChapterNew>>()
        val gson = Gson()
        val egrData = gson.fromJson(egrJson.toString(), EGLData::class.java)

        egrData.usbDriveContents?.forEach { root -> // e.g., "Class 1"
            root.children?.forEach { contentSeries -> // e.g., "Foundational Literacy" (Subject)
                val subjectName = contentSeries.name ?: "Unknown Subject"
                val chaptersList = mutableListOf<ChapterNew>()
                contentSeries.children?.forEach { chapterContent -> // These are the actual chapters
                    // Assuming chapterContent.name is like "Chapter 1 - Title"
                    // You might need to adjust parsing based on your actual data structure for chapter names/titles
                    val chapterName = chapterContent.name ?: "Unnamed Chapter"
                    // For simplicity, let's assume the name is the title and chapter number is part of it or needs extraction

                    chaptersList.add(ChapterNew(chapterName, "",  "No description",chapterContent.children)) // this need to update

                }
                map[subjectName] = chaptersList
            }
        }
        return map
    }

}
