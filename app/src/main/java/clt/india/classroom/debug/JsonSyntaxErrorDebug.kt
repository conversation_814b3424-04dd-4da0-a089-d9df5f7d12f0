package clt.india.classroom.debug

import android.util.Log
import clt.india.classroom.utils.JsonDebugger
import clt.india.classroom.utils.JsonErrorHandler

/**
 * Debug guide for JsonSyntaxException: Expected BEGIN_ARRAY but was NUMBER
 */
object JsonSyntaxErrorDebug {

    /**
     * Main debugging method for JSON syntax errors
     */
    fun debugJsonSyntaxError(jsonString: String?, expectedType: String) {
        Log.i("JsonSyntaxErrorDebug", "=== DEBUGGING JSON SYNTAX ERROR ===")
        Log.i("JsonSyntaxErrorDebug", "Expected type: $expectedType")
        
        if (jsonString == null) {
            Log.e("JsonSyntaxErrorDebug", "❌ JSON string is NULL")
            printNullJsonSolutions()
            return
        }
        
        // Use the JsonDebugger to analyze the issue
        JsonDebugger.debugJsonIssue(jsonString, expectedType)
        
        // Analyze the specific error
        analyzeSpecificError(jsonString, expectedType)
        
        // Provide solutions
        provideSolutions(jsonString, expectedType)
        
        Log.i("JsonSyntaxErrorDebug", "=== END DEBUGGING ===")
    }

    private fun analyzeSpecificError(jsonString: String, expectedType: String) {
        Log.i("JsonSyntaxErrorDebug", "--- ANALYZING SPECIFIC ERROR ---")
        
        val trimmed = jsonString.trim()
        
        when {
            trimmed.matches(Regex("^\\d+$")) -> {
                Log.e("JsonSyntaxErrorDebug", "❌ ISSUE: JSON is a plain number: $trimmed")
                Log.e("JsonSyntaxErrorDebug", "💡 EXPECTED: JSON array like [1,2,3] or object like {\"key\":\"value\"}")
                printNumberInsteadOfArraySolutions()
            }
            
            trimmed.startsWith("\"") && trimmed.endsWith("\"") -> {
                Log.e("JsonSyntaxErrorDebug", "❌ ISSUE: JSON is a quoted string")
                Log.e("JsonSyntaxErrorDebug", "💡 EXPECTED: Unquoted JSON structure")
                printStringInsteadOfObjectSolutions()
            }
            
            trimmed.matches(Regex("^(true|false)$")) -> {
                Log.e("JsonSyntaxErrorDebug", "❌ ISSUE: JSON is a boolean: $trimmed")
                Log.e("JsonSyntaxErrorDebug", "💡 EXPECTED: JSON array or object")
                printBooleanInsteadOfArraySolutions()
            }
            
            !trimmed.startsWith("{") && !trimmed.startsWith("[") -> {
                Log.e("JsonSyntaxErrorDebug", "❌ ISSUE: JSON doesn't start with { or [")
                Log.e("JsonSyntaxErrorDebug", "💡 EXPECTED: Valid JSON structure")
                printInvalidJsonFormatSolutions()
            }
            
            else -> {
                Log.w("JsonSyntaxErrorDebug", "⚠️ ISSUE: Unknown JSON format issue")
                printGeneralSolutions()
            }
        }
    }

    private fun provideSolutions(jsonString: String, expectedType: String) {
        Log.i("JsonSyntaxErrorDebug", "--- PROVIDING SOLUTIONS ---")
        
        val trimmed = jsonString.trim()
        
        when (expectedType.lowercase()) {
            "list", "array" -> {
                if (trimmed.matches(Regex("^\\d+$"))) {
                    Log.i("JsonSyntaxErrorDebug", "🔧 SOLUTION 1: Wrap number in array: [$trimmed]")
                    Log.i("JsonSyntaxErrorDebug", "🔧 SOLUTION 2: Change code to expect single number instead of array")
                    Log.i("JsonSyntaxErrorDebug", "🔧 SOLUTION 3: Check API endpoint - might be returning count instead of list")
                }
            }
            
            "object" -> {
                if (trimmed.matches(Regex("^\\d+$"))) {
                    Log.i("JsonSyntaxErrorDebug", "🔧 SOLUTION 1: Wrap number in object: {\"value\":$trimmed}")
                    Log.i("JsonSyntaxErrorDebug", "🔧 SOLUTION 2: Change code to expect number instead of object")
                }
            }
        }
        
        // General solutions
        Log.i("JsonSyntaxErrorDebug", "🔧 GENERAL SOLUTIONS:")
        Log.i("JsonSyntaxErrorDebug", "  1. Add null checks before JSON parsing")
        Log.i("JsonSyntaxErrorDebug", "  2. Use try-catch blocks around JSON parsing")
        Log.i("JsonSyntaxErrorDebug", "  3. Validate JSON format before parsing")
        Log.i("JsonSyntaxErrorDebug", "  4. Add fallback values for parsing failures")
        Log.i("JsonSyntaxErrorDebug", "  5. Check API documentation for correct response format")
    }

    private fun printNullJsonSolutions() {
        Log.i("JsonSyntaxErrorDebug", """
            🔧 SOLUTIONS FOR NULL JSON:
            
            1. Check data source:
               - Verify API response is not null
               - Check database query results
               - Ensure file reading succeeded
            
            2. Add null checks:
               if (jsonString != null) {
                   // Parse JSON
               } else {
                   // Handle null case
               }
            
            3. Use safe parsing:
               val result = JsonErrorHandler.safeParseJson<YourClass>(jsonString, fallback)
        """.trimIndent())
    }

    private fun printNumberInsteadOfArraySolutions() {
        Log.i("JsonSyntaxErrorDebug", """
            🔧 SOLUTIONS FOR NUMBER INSTEAD OF ARRAY:
            
            PROBLEM: Code expects [1,2,3] but got 123
            
            1. API ENDPOINT ISSUE:
               - Check if you're calling the right endpoint
               - List endpoint: /api/items (returns array)
               - Count endpoint: /api/items/count (returns number)
            
            2. DATABASE QUERY ISSUE:
               - SELECT * FROM items (returns list)
               - SELECT COUNT(*) FROM items (returns number)
            
            3. CODE FIXES:
               // Option A: Handle both cases
               if (response.startsWith("[")) {
                   List<Item> items = gson.fromJson(response, new TypeToken<List<Item>>(){}.getType());
               } else {
                   int count = gson.fromJson(response, Integer.class);
                   // Handle count case
               }
               
               // Option B: Use safe parsing
               List<Item> items = JsonErrorHandler.safeParseJsonArray<Item>(response, emptyList());
        """.trimIndent())
    }

    private fun printStringInsteadOfObjectSolutions() {
        Log.i("JsonSyntaxErrorDebug", """
            🔧 SOLUTIONS FOR STRING INSTEAD OF OBJECT:
            
            PROBLEM: Code expects {"key":"value"} but got "some string"
            
            1. DOUBLE-ENCODED JSON:
               String jsonString = gson.fromJson(response, String.class);
               YourClass obj = gson.fromJson(jsonString, YourClass.class);
            
            2. ERROR RESPONSE:
               - API might be returning error message as string
               - Check HTTP status codes first
               - Handle error responses separately
            
            3. CONTENT-TYPE ISSUE:
               - Check API response headers
               - Ensure Content-Type: application/json
        """.trimIndent())
    }

    private fun printBooleanInsteadOfArraySolutions() {
        Log.i("JsonSyntaxErrorDebug", """
            🔧 SOLUTIONS FOR BOOLEAN INSTEAD OF ARRAY:
            
            PROBLEM: Code expects [true,false] but got true
            
            1. SINGLE VALUE VS ARRAY:
               // Handle both cases
               if (response.equals("true") || response.equals("false")) {
                   boolean single = gson.fromJson(response, Boolean.class);
                   List<Boolean> list = Arrays.asList(single);
               } else {
                   List<Boolean> list = gson.fromJson(response, new TypeToken<List<Boolean>>(){}.getType());
               }
            
            2. API DESIGN ISSUE:
               - Check if API should return array instead of single boolean
               - Update API to return consistent format
        """.trimIndent())
    }

    private fun printInvalidJsonFormatSolutions() {
        Log.i("JsonSyntaxErrorDebug", """
            🔧 SOLUTIONS FOR INVALID JSON FORMAT:
            
            PROBLEM: JSON doesn't start with { or [
            
            1. VALIDATE BEFORE PARSING:
               JsonValidationResult validation = JsonErrorHandler.validateJsonFormat(jsonString);
               if (validation.isValid()) {
                   // Parse JSON
               } else {
                   // Handle invalid format
               }
            
            2. COMMON INVALID FORMATS:
               - Plain text: "Hello World"
               - HTML: <html>...</html>
               - XML: <?xml version="1.0"?>
               - Empty string: ""
               - Whitespace only: "   "
            
            3. CHECK DATA SOURCE:
               - Verify API returns JSON, not HTML error page
               - Check file contains JSON, not plain text
               - Ensure network request succeeded
        """.trimIndent())
    }

    private fun printGeneralSolutions() {
        Log.i("JsonSyntaxErrorDebug", """
            🔧 GENERAL JSON ERROR SOLUTIONS:
            
            1. USE SAFE PARSING:
               val result = JsonErrorHandler.safeParseJson<YourClass>(jsonString, fallback)
            
            2. ADD VALIDATION:
               val validation = JsonErrorHandler.validateJsonFormat(jsonString)
               if (validation.isValid) { /* parse */ }
            
            3. IMPLEMENT FALLBACKS:
               val items = try {
                   gson.fromJson(jsonString, List::class.java)
               } catch (e: JsonSyntaxException) {
                   emptyList() // fallback
               }
            
            4. LOG DEBUGGING INFO:
               JsonDebugger.debugJsonIssue(jsonString, "ExpectedType")
            
            5. CHECK NETWORK/API:
               - Verify API endpoint URL
               - Check HTTP status codes
               - Validate response headers
               - Test API with Postman/curl
        """.trimIndent())
    }
}

/**
 * Quick fix methods for common JSON errors
 */
object JsonSyntaxQuickFix {

    /**
     * Quick fix for "Expected BEGIN_ARRAY but was NUMBER"
     */
    fun fixExpectedArrayButWasNumber(jsonString: String): List<Any> {
        return try {
            // Try parsing as array first
            val gson = com.google.gson.Gson()
            gson.fromJson(jsonString, List::class.java)
        } catch (e: Exception) {
            // If it's a single number, wrap it in a list
            if (jsonString.trim().matches(Regex("^\\d+$"))) {
                listOf(jsonString.trim().toInt())
            } else {
                emptyList()
            }
        }
    }

    /**
     * Quick fix for "Expected BEGIN_OBJECT but was STRING"
     */
    fun fixExpectedObjectButWasString(jsonString: String): Map<String, Any> {
        return try {
            // Try parsing as object first
            val gson = com.google.gson.Gson()
            gson.fromJson(jsonString, Map::class.java) as Map<String, Any>
        } catch (e: Exception) {
            // If it's a string, create a map with the string value
            mapOf("value" to jsonString)
        }
    }

    /**
     * Universal JSON fixer - attempts to parse any JSON safely
     */
    fun universalJsonFix(jsonString: String?): Any? {
        if (jsonString.isNullOrEmpty()) return null
        
        val trimmed = jsonString.trim()
        val gson = com.google.gson.Gson()
        
        return try {
            when {
                trimmed.startsWith("{") -> gson.fromJson(trimmed, Map::class.java)
                trimmed.startsWith("[") -> gson.fromJson(trimmed, List::class.java)
                trimmed.matches(Regex("^\\d+$")) -> trimmed.toInt()
                trimmed.matches(Regex("^\\d+\\.\\d+$")) -> trimmed.toDouble()
                trimmed.matches(Regex("^(true|false)$")) -> trimmed.toBoolean()
                trimmed.startsWith("\"") && trimmed.endsWith("\"") -> {
                    // Try parsing the inner string as JSON
                    val innerString = gson.fromJson(trimmed, String::class.java)
                    universalJsonFix(innerString) ?: innerString
                }
                else -> trimmed
            }
        } catch (e: Exception) {
            Log.w("JsonSyntaxQuickFix", "Could not parse JSON: ${e.message}")
            trimmed
        }
    }
}
