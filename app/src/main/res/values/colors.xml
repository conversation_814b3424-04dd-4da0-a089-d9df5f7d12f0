<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="background_gradient_start">#000000</color>
    <color name="background_gradient_end">#DDDDDD</color>
    <color name="fastlane_background">#0096a6</color>
    <color name="search_opaque">#ffaa3f</color>
    <color name="selected_background">#ffaa3f</color>
    <color name="default_background">#3d3d3d</color>
    <color name="neutral_200">#F3F4F6FF</color>
    <color name="light_blue">#F1F8FDFF</color>
    <color name="neutral_700">#323743FF</color>
    <color name="link_colour">#379AE6FF</color>
    <color name="spinner_focused_bg">#DDDDDD</color>
    <color name="primary_200">#B9B9B9</color>
    <color name="primary_500">#000000</color>
    <color name="dark_grey">#A9A9A9</color>
    <color name="neutral_600">#565D6D</color>
    <color name="light_grey">#DEE1E6FF</color>
    <color name="search_grey">#F3F4F6</color>
    <color name="dark_yellow">#A36A00</color>
    <color name="blue_400">#42A5F5</color>
    <color name="grey_starting">#6F7787</color>
    <color name="neutral_300">#E5E5E5</color>
    <color name="text_grey">#424856</color>
    <color name="red">#FF0000</color>
    <color name="green">#388E3C</color>

    <!-- Primary colors for educational app theme -->
    <color name="colorPrimary">#2196F3</color>          <!-- Material Blue - friendly and trustworthy -->
    <color name="colorPrimaryDark">#1976D2</color>      <!-- Darker blue for status bar -->
    <color name="colorPrimaryLight">#BBDEFB</color>     <!-- Light blue for backgrounds -->
    <color name="colorAccent">#FF9800</color>           <!-- Orange accent - energetic and engaging -->
    <color name="colorAccentDark">#F57C00</color>       <!-- Darker orange for pressed states -->

    <!-- Educational theme colors -->
    <color name="educational_blue">#2196F3</color>      <!-- Primary blue for buttons -->
    <color name="educational_orange">#FF9800</color>    <!-- Accent orange for highlights -->
    <color name="educational_green">#4CAF50</color>     <!-- Success green -->
    <color name="educational_red">#F44336</color>       <!-- Error red -->
    <color name="educational_purple">#9C27B0</color>    <!-- Creative purple -->

    <!-- Focus and selection colors for TV -->
    <color name="tv_focus_border">#FFFFFF</color>       <!-- White border for TV focus -->
    <color name="tv_focus_glow">#FFD700</color>         <!-- Gold glow for emphasis -->
    <color name="tv_selected_bg">#E3F2FD</color>        <!-- Light blue background when selected -->

</resources>