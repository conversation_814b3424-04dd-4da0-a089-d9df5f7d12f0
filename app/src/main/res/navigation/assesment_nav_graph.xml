<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/assesment_nav_graph"
    app:startDestination="@id/assesmentViewFragment">

    <!-- AssesmentViewFragment -->
    <fragment
        android:id="@+id/assesmentViewFragment"
        android:name="clt.india.classroom.ui.assesment.AssesmentViewFragment"
        android:label="fragment_assesment_view"
        tools:layout="@layout/fragment_assesment_view">
        <argument
            android:name="initialTab"
            android:defaultValue="1"
            app:argType="integer" />
        <argument
            android:name="selectedGrade"
            android:defaultValue=""
            app:argType="string" />
        <!-- navigation from assesmentViewFragment to assesmentSubmissionFragment -->
        <action
            android:id="@+id/action_assesmentViewFragment_to_assesmentSubmissionFragment"
            app:destination="@id/assesmentSubmissionFragment" />
    </fragment>
    <!-- AssesmentSubmissionFragment -->
    <fragment
        android:id="@+id/assesmentSubmissionFragment"
        android:name="clt.india.classroom.ui.assesment.AssesmentSubmissionFragment"
        android:label="fragment_assesment_submission"
        tools:layout="@layout/fragment_assesment_submission">

        <!-- navigation from assesmentSubmissionFragment to assessmentResultsFragment -->
        <action
            android:id="@+id/action_assesmentSubmissionFragment_to_assessmentResultsFragment"
            app:destination="@id/assessmentResultsFragment" />

        <!-- navigation from assesmentSubmissionFragment to assesmentViewFragment -->
        <action
            android:id="@+id/action_assesmentSubmissionFragment_to_assesmentViewFragment"
            app:destination="@id/assesmentViewFragment" />
    </fragment>
    <!-- AssessmentResultsFragment -->
    <fragment
        android:id="@+id/assessmentResultsFragment"
        android:name="clt.india.classroom.ui.assesment.AssessmentResultsFragment"
        android:label="fragment_assessment_results"
        tools:layout="@layout/fragment_assessment_results" />
</navigation>