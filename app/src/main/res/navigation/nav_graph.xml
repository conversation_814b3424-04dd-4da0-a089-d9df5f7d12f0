<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/gradeSelectionFragment">

    <!--Gradeselectionfragment-->
    <fragment
        android:id="@+id/gradeSelectionFragment"
        android:name="clt.india.classroom.ui.gradeselection.GradeSelectionFragment"
        android:label="fragment_grade_selection"
        tools:layout="@layout/fragment_grade_selection">
        <argument
            android:name="startTab"
            android:defaultValue="classroom"
            app:argType="string" />
        <!--gradeSelectionFragment Navigates to IndexPageFragment (Grades 5 and above) -->
        <action
            android:id="@+id/action_gradeSelectionFragment_to_indexPageFragment"
            app:destination="@id/indexPageFragment"/>
        <!--gradeSelectionFragment Navigates to OneToFourIndexPageFragment (Grades 1 to 4) -->
        <action
            android:id="@+id/action_gradeSelectionFragment_to_oneToFourIndexPageFragment"
            app:destination="@id/oneToFourIndexPageFragment" />
        <!-- gradeSelectionFragment Navigates to Digital Literacy Program -->
        <action
            android:id="@+id/action_gradeSelectionFragment_to_digitalLiteracyProgramFragment"
            app:destination="@id/digitalLiteracyProgramFragment" />
    </fragment>
    <!-- IndexPageFragment-->
    <fragment
        android:id="@+id/indexPageFragment"
        android:name="clt.india.classroom.ui.gradeselection.IndexPageFragment"
        android:label="fragment_index_page"
        tools:layout="@layout/fragment_index_page">
    </fragment>
    <!-- OneToFourIndexPageFragment -->
    <fragment
        android:id="@+id/oneToFourIndexPageFragment"
        android:name="clt.india.classroom.ui.egr.OneToFourIndexPageFragment"
        android:label="fragment_one_to_four_index_page"
        tools:layout="@layout/fragment_one_to_four_index_page">
        <!-- OneToFourIndexPageFragment Navigates back to GradeSelectionFragment -->
        <action
            android:id="@+id/action_oneToFourIndexPageFragment_to_gradeSelectionFragment"
            app:destination="@id/gradeSelectionFragment" />
    </fragment>
    <!--DigitalLiteracyProgramFragment -->
    <fragment
        android:id="@+id/digitalLiteracyProgramFragment"
        android:name="clt.india.classroom.ui.digitalliteracy.DigitalLiteracyProgramFragment"
        android:label="fragment_digital_literacy_program"
        tools:layout="@layout/fragment_digital_literacy_program">
        <!-- DigitalLiteracyProgramFragment Navigates to GradeSelectionFragment -->
        <action
            android:id="@+id/action_digitalLiteracyProgramFragment_to_gradeSelectionFragment"
            app:destination="@id/gradeSelectionFragment" />
    </fragment>

</navigation>