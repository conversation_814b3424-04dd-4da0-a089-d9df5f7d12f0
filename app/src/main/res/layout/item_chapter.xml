<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:orientation="horizontal"
                android:padding="12dp">

<!--                <TextView-->
<!--                    android:id="@+id/chapterNumber"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginEnd="30dp"-->
<!--                    android:gravity="start"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="14sp"-->
<!--                    android:text="Chapter 1" />-->

                <TextView
                    android:id="@+id/chapterTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:text="Air"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:layout_marginEnd="16dp" />

            </LinearLayout>

            <FrameLayout
                android:id="@+id/viewButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/button_background"
                android:layout_gravity="end|center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="View"
                android:textSize="12sp"
                android:padding="5dp"
                android:layout_gravity="center"
                android:fontFamily="@font/archivo_bold"
                android:textColor="@color/white"/>
            </FrameLayout>
        </FrameLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0" />
    </LinearLayout>
</LinearLayout>
