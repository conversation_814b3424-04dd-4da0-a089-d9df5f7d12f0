<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/questionText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="12sp"
        android:textStyle="bold" />

    <RadioGroup
        android:id="@+id/optionsRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/option1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:buttonTint="@color/black"
            android:minHeight="0dp"
            android:textColor="@color/black"
            android:textSize="12sp" />


        <RadioButton
            android:id="@+id/option2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:buttonTint="@color/black"
            android:minHeight="0dp"
            android:textColor="@color/black"
            android:textSize="12sp" />


        <RadioButton
            android:id="@+id/option3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:buttonTint="@color/black"
            android:minHeight="0dp"
            android:textColor="@color/black"
            android:textSize="12sp" />


        <RadioButton
            android:id="@+id/option4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:buttonTint="@color/black"
            android:minHeight="0dp"
            android:textColor="@color/black"
            android:textSize="12sp" />

    </RadioGroup>
</LinearLayout>
