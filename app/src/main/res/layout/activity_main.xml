<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.MainActivity">

    <include layout="@layout/custom_progress_bar" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/txtLay_emailAdd"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:hint="Email"
        app:endIconMode="clear_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.4"
        app:layout_constraintWidth_percent="0.9">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txtInput_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLength="20" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/txtLay_pass_signup"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:hint="PASSWORD"
        app:endIconDrawable="@drawable/ic_lock"
        app:endIconMode="password_toggle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtLay_emailAdd"
        app:layout_constraintWidth_percent="0.9">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txt_pass"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLength="15" />
    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/btn_login"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginEnd="@dimen/_2sdp"
        android:text="@string/login"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_register"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btn_register"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/_2sdp"
        android:text="@string/register"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/btn_login" />
</androidx.constraintlayout.widget.ConstraintLayout>
