<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_logo"
    tools:context=".ui.gradeselection.HomeActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="30dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/curve_rectangle_bg_2dp"
                android:padding="12dp">

                <ImageView
                    android:layout_width="217dp"
                    android:layout_height="32dp"
                    android:layout_gravity="top|start"
                    android:layout_marginStart="30dp"
                    android:src="@drawable/clt_patashale" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/account_icon" />

                    <TextView
                        android:id="@+id/school_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/school_name"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/grade_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="37dp"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/archivo_bold"
                    android:text="@string/grade"
                    android:textColor="@color/black"
                    android:textSize="20sp" />

                <TextView
                    android:id="@+id/change_grade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/change_grade"
                    android:textColor="@color/blue_400"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/subjectTabsRecyclerView"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="33dp"
                    android:layout_weight="1"
                    android:clipToPadding="false"
                    android:orientation="vertical"
                    android:overScrollMode="never"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_subject_tab" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="16dp"
                    android:layout_weight="3"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/chapter_navigation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/back_button_bg"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <ImageView
                            android:id="@+id/back_to_chapters"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/back_arrow_black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_gravity="center"
                            android:text="@string/back"
                            android:textColor="@color/dark_yellow"
                            android:textSize="10sp" />
                    </LinearLayout>

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginTop="8dp"-->
<!--                        android:orientation="horizontal">-->

<!--                        <TextView-->
<!--                            android:id="@+id/chapter_title"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:fontFamily="@font/archivo_bold"-->
<!--                            android:text="Lorem Ipsum"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:textColor="@color/black"-->
<!--                            android:textSize="16sp" />-->

<!--                        <FrameLayout-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginStart="12dp"-->
<!--                            android:background="@drawable/bg_subject_selected">-->

<!--                            <TextView-->
<!--                                android:id="@+id/chapter_titles"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:fontFamily="@font/inter_regular"-->
<!--                                android:padding="8dp"-->
<!--                                android:text="Science"-->
<!--                                android:textColor="@color/blue_400"-->
<!--                                android:textSize="10sp" />-->
<!--                        </FrameLayout>-->
<!--                    </LinearLayout>-->

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:background="@color/white"
                        app:tabMode="scrollable"
                        app:tabGravity="start"

                        app:tabIndicatorColor="@color/black"
                        app:tabSelectedTextColor="@color/black"
                        app:tabTextColor="@color/dark_grey" />

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/viewPager"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"/>

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>