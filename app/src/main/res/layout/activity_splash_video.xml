<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".ui.splash.SplashVideoActivity">
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/splash_image"
        android:scaleType="centerCrop"
        android:adjustViewBounds="true" />
    <LinearLayout
        android:id="@+id/controlLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:padding="4dp">

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:progressDrawable="@color/white" />


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/playPauseButton"
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"
                    android:layout_marginEnd="8dp"
                    android:contentDescription="Play/Pause"
                    android:src="@drawable/ic_pause" />

                <ImageView
                    android:id="@+id/nextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"
                    android:layout_marginStart="8dp"
                    android:contentDescription="Next"
                    android:src="@drawable/ic_next" />

                <ImageView
                    android:id="@+id/volumeButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="18.55dp"
                    android:contentDescription="Volume"
                    android:src="@drawable/ic_volume" />

                <TextView
                    android:id="@+id/timeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="16dp"
                    android:gravity="start"
                    android:text="1:11 / 2:58"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/replayButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:contentDescription="Replay"
                    android:src="@drawable/ic_replay" />

                <ImageView
                    android:id="@+id/forwardbutton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="7dp"
                    android:contentDescription="Fullscreen"
                    android:src="@drawable/ic_forward" />
            </LinearLayout>

            <ImageView
                android:id="@+id/full_screen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center"
                android:src="@drawable/ic_fullscreen" />

        </FrameLayout>
    </LinearLayout>
</LinearLayout>