<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_logo"
    tools:context=".ui.gradeselection.IndexPageFragment">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="0dp"
        android:layout_marginTop="74dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/curve_rectangle_bg_2dp"
                android:padding="16dp">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|start"
                    android:src="@drawable/clte_patashales" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/account_icon" />

                    <TextView
                        android:id="@+id/school_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/school_name"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical"
                android:weightSum="1">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.6"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/grade_select"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/archivo_bold"
                        android:text="5th Grade (English)"
                        android:textColor="@color/black"
                        android:textSize="20sp" />

                    <TextView
                        android:id="@+id/change_grade"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="Change Grade"
                        android:textColor="@color/blue_400"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/search_fun"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="0.4"
                    android:background="@drawable/search_bcakground"
                    android:orientation="horizontal"
                    android:padding="10dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_search" />

                    <EditText
                        android:id="@+id/search_video"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:background="@android:color/transparent"
                        android:hint="Search"
                        android:inputType="text"
                        android:fontFamily="@font/inter_regular"
                        android:textColor="@color/black"
                        android:textColorHint="@color/lb_grey"
                        android:textSize="16sp"/>

                </LinearLayout>
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingBottom="16dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/subjectTabsRecyclerView"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="20dp"
                    tools:listitem="@layout/item_subject_tab" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/curve_rectangle_bg_2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <View
                                android:layout_width="8dp"
                                android:layout_height="32dp"
                                android:background="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:fontFamily="@font/archivo_bold"
                                android:text="Chapters"
                                android:textColor="@color/black"
                                android:textSize="20sp" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/light_grey" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/chaptersRecyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="400dp"
                            android:background="@drawable/curve_rectangle_bg_2dp"
                            android:clipToPadding="false"
                            android:paddingVertical="4dp"
                            tools:listitem="@layout/item_chapter" />
                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>
