<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    android:layout_marginHorizontal="4dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/card_image"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:scaleType="centerCrop"
            android:src="@drawable/image1" />

        <TextView
            android:id="@+id/card_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="4dp"
            android:paddingHorizontal="6dp"
            android:background="@color/white"
            android:text="Example Card Title"
            android:textAppearance="?attr/textAppearanceBodyMedium" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
