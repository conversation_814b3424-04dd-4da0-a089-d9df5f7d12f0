<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    card_view:cardCornerRadius="8dp"
    card_view:cardElevation="2dp"
    card_view:cardUseCompatPadding="false"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minHeight="160dp"
        android:background="@color/white">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@color/search_grey"
            android:gravity="center">

            <ImageView
                android:id="@+id/playIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/img_resume"
                android:layout_gravity="center"

               />
        </FrameLayout>

        <TextView
            android:id="@+id/chapterTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="6dp"
            android:gravity="center"
            android:fontFamily="@font/inter_regular"
            android:text="Lorem Ipsum"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />
    </LinearLayout>

</androidx.cardview.widget.CardView>
