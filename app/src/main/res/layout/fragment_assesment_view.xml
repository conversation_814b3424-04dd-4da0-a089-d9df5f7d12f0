<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_logo"
    tools:context=".ui.assesment.AssesmentViewFragment">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="40dp"
        android:background="@drawable/curve_rectangle_bg_2dp"
        android:backgroundTint="@color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/headerSection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginStart="20dp"
                    android:src="@drawable/clte_patashales" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:gravity="end|center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/account_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:text="@string/school_name"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:id="@+id/gradeSection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/headerSection"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:id="@+id/grade_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:fontFamily="@font/archivo_bold"
                    android:text="@string/grade"
                    android:textColor="@color/black"
                    android:textSize="20sp" />

                <TextView
                    android:id="@+id/change_grade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/change_grade"
                    android:textColor="@color/blue_400"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/chapterSection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/gradeSection"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:id="@+id/back_button"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/arrow_left_black" />

                <TextView
                    android:id="@+id/chapter_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/archivo_bold"
                    android:text="Air Pollution"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@drawable/bg_subject_selected"
                    android:backgroundTint="@color/light_grey">

                    <TextView
                        android:id="@+id/chapter_titles"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_regular"
                        android:padding="8dp"
                        android:text="Science"
                        android:textColor="@color/dark_grey"
                        android:textSize="12sp" />
                </FrameLayout>
            </LinearLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/recyclerContainer"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_margin="16dp"
                android:background="@drawable/curve_rectangle_bg_2dp"
                app:layout_constraintTop_toBottomOf="@id/chapterSection"
                app:layout_constraintBottom_toTopOf="@id/submitButton"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <LinearLayout
                    android:id="@+id/customTabLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp"
                    android:layout_marginStart="16dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <TextView
                        android:id="@+id/tab_assessment1"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="Assessment 1"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:background="@drawable/selected_tab_background"
                        android:padding="8dp" />

                    <TextView
                        android:id="@+id/tab_assessment2"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="Assessment 2"
                        android:textColor="@android:color/black"
                        android:textSize="14sp"
                        android:background="@drawable/unselected_tab_background"
                        android:padding="8dp"

                        />
                </LinearLayout>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/assessmentRecyclerView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="8dp"
                    app:layout_constraintTop_toBottomOf="@id/customTabLayout"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:listitem="@layout/item_questions" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <LinearLayout
                android:id="@+id/submitButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="8dp"
                android:padding="12dp"
                android:background="@drawable/curve_rectangle_bg_2dp"
                android:backgroundTint="@color/black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_regular"
                    android:text="Submit and view results"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/navigate_next"/>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>


</FrameLayout>
