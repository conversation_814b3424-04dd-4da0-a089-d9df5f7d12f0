<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.gradeselection.GradeSelectionFragment">

    <ImageView
        android:id="@+id/backgroundImage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:alpha="0.3"
        android:scaleType="centerCrop"
        android:src="@drawable/splash_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.cardview.widget.CardView
        android:id="@+id/startCard"
        android:layout_width="550dp"
        android:layout_height="450dp"
        android:layout_margin="32dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="18dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginBottom="10dp"
                android:src="@drawable/clt_patashale" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:gravity="center"
                android:singleLine="true"
                android:text="@string/access_to_quality_Education"
                android:textColor="@color/black"
                android:textSize="18sp" />

<!--
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/languageTabs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                app:tabBackground="@drawable/tab_background_selector"
                app:tabIndicatorColor="@color/black"
                app:tabIndicatorHeight="0dp"
                app:tabSelectedTextColor="@color/white"
                app:tabTextColor="@color/black" />-->

            <LinearLayout
                android:id="@+id/customTabLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="center"
                android:layout_marginTop="18dp">

                <TextView
                    android:id="@+id/tab_classroom"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="30dp"
                    android:gravity="center"
                    android:paddingHorizontal="16dp"
                    android:text="Classroom Lessons"
                    android:textColor="@color/white"
                    android:background="@drawable/selected_tab_background"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tab_digital"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="30dp"
                    android:gravity="center"
                    android:paddingHorizontal="16dp"
                    android:text="Digital Literacy Program"
                    android:textColor="@color/white"
                    android:background="@drawable/unselected_tab_background"
                    android:textSize="14sp" />
            </LinearLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="23dp"
                android:background="@color/light_blue"
                app:cardCornerRadius="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="12dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:src="@drawable/info_icon"
                        app:tint="@color/teal_700" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/note"
                        android:textColor="@color/black"
                        android:textSize="14sp" />


                </LinearLayout>
            </FrameLayout>

            <TextView
                android:id="@+id/select_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/archivo_bold"
                android:text="@string/select_grade"
                android:textColor="@color/black" />

            <!--
                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="12dp"
                            android:background="@drawable/curve_rectangle_bg_2dp">

                            <Spinner
                                android:id="@+id/grade_select"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:spinnerMode="dropdown"
                                android:visibility="visible"
                                android:focusable="true"
                                android:focusableInTouchMode="true"
                                android:clickable="true"
                                android:importantForAccessibility="yes"

                                 />

                        </RelativeLayout>
            -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/curve_rectangle_bg_2dp"
                android:padding="12dp">

                <TextView
                    android:id="@+id/grade_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Grade 1"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/dropdown_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/dropdown_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_dropdown_arrow"
                    android:padding="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <FrameLayout
                android:id="@+id/submit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:background="@drawable/curve_rectangle_bg_2dp"
                android:backgroundTint="@color/black"
                android:padding="10dp">

                <TextView
                    android:id="@+id/submit_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/inter_regular"
                    android:text="Next"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <ProgressBar
                    android:id="@+id/submit_progress"
                    android:layout_width="@dimen/_18sdp"
                    android:layout_height="@dimen/_18sdp"
                    android:layout_gravity="center"
                    android:indeterminateTint="@color/white"
                    android:visibility="gone" />

            </FrameLayout>
        </LinearLayout>

    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>