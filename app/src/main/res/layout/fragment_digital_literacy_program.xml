<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_logo"
    tools:context=".ui.digitalliteracy.DigitalLiteracyProgramFragment">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="30dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/curve_rectangle_bg_2dp"
                android:paddingVertical="8dp"
                >

                <ImageView
                    android:layout_width="217dp"
                    android:layout_height="32dp"
                    android:layout_gravity="top|start"
                    android:src="@drawable/clt_patashale"
                    android:layout_marginStart="25dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/account_icon" />

                    <TextView
                        android:id="@+id/school_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/school_name"
                        android:textColor="@color/black"
                        android:layout_marginEnd="10dp"
                        android:textSize="16sp" />
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginStart="25dp"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/back_button"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:src="@drawable/arrow_left_black" />
                <TextView
                    android:id="@+id/grade_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:fontFamily="@font/archivo_bold"
                    android:text="Digital Literacy Program"
                    android:textColor="@color/black"
                    android:textSize="20sp" />
                <TextView
                    android:id="@+id/version_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/archivo_bold"
                    android:text="(Version 1)"
                    android:textColor="@color/black"
                    android:textSize="20sp" />
                <TextView
                    android:id="@+id/change_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/change_version"
                    android:textColor="@color/blue_400"
                    android:textSize="12sp" />
            </LinearLayout>
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginStart="25dp"
                android:layout_marginEnd="25dp"
                android:background="@color/light_blue"
                app:cardCornerRadius="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="8dp"
                    android:gravity="start"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:src="@drawable/info_icon"
                        app:tint="@color/teal_700" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="Please Checkout the sample video for DLP Version 1"
                        android:textColor="@color/black"
                        android:textSize="14sp" />


                </LinearLayout>

            </FrameLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewChapters"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="25dp"
                android:orientation="horizontal"
                tools:listitem="@layout/item_digital_literacy"/>
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_subject_selected">

                <TextView
                    android:id="@+id/chapter_titles"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/archivo_bold"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="5dp"
                    android:text="For more videos Reach out to us"
                    android:textColor="@color/blue_400"
                    android:textSize="10sp" />
            </FrameLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:background="@drawable/search_bcakground"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginHorizontal="25dp"
                android:paddingHorizontal="12dp"
                android:paddingVertical="8dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginEnd="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/img_mail"
                        android:layout_marginEnd="8dp"
                        android:contentDescription="Email"
                        app:tint="@android:color/holo_red_dark" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </LinearLayout>



                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginStart="8dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/img_whastsapp"
                        android:layout_marginEnd="8dp"
                        android:contentDescription="WhatsApp"
                        app:tint="@android:color/holo_green_dark" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="+91 12345 67890"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewChapterss"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="25dp"
                android:orientation="horizontal"
                tools:listitem="@layout/item_digital_literacy"/>


        </LinearLayout>
    </androidx.cardview.widget.CardView>

</FrameLayout>