<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/exo_controller"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="#CC000000">

    <androidx.media3.ui.DefaultTimeBar
        android:id="@+id/exo_progress"
        android:layout_width="match_parent"
        android:layout_height="26dp"
        android:layout_marginBottom="4dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:weightSum="1">



        <ImageButton
            android:id="@+id/exo_pause"
            style="@style/ExoMediaButton.Pause" />
        <ImageButton
            android:id="@+id/exo_play"
            style="@style/ExoMediaButton.Play" />
        <TextView
            android:id="@+id/exo_position"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0:00"
            android:textColor="@android:color/white"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />
        <TextView
            android:id="@+id/exo_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0:00"
            android:textColor="@android:color/white" />

        <ImageButton
            android:id="@+id/exo_rew"
            style="@style/ExoMediaButton.Rewind" />

        <ImageButton
            android:id="@+id/exo_ffwd"
            style="@style/ExoMediaButton.FastForward" />





        <ImageButton
            android:id="@+id/exo_fullscreen"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_fullscreen"
            android:background="@null" />
    </LinearLayout>
</LinearLayout>
