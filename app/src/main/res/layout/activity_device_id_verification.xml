<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_logo"
    tools:context=".ui.DeviceIdVerificationActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="600dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="10dp"
        android:backgroundTint="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/device"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="20dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <TextView
                android:id="@+id/tv_device_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/archivo_bold"
                android:gravity="center"
                android:text="@string/device_id"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:drawablePadding="10dp"
                app:drawableLeftCompat="@drawable/ic_device"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_device_id_val"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/archivo_bold"
                android:gravity="center"
                android:text="-------------------"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:paddingLeft="10dp"
                app:layout_constraintStart_toEndOf="@id/tv_device_id"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_device_id_success"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/archivo_bold"
                android:gravity="center"
                android:text="@string/device_act"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textFontWeight="700"
                android:drawablePadding="10dp"
                android:visibility="gone"
                app:drawableLeftCompat="@drawable/ic_activate"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_id" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/no_device"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">


            <ImageView
                android:id="@+id/no_device_logo"
                android:layout_width="148dp"
                android:layout_height="137dp"
                android:layout_marginTop="37dp"
                android:background="@drawable/no_device"
                app:layout_constraintBottom_toTopOf="@+id/tv_no_device"
                app:layout_constraintHorizontal_bias="0.466"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

            </ImageView>

            <TextView
                android:id="@+id/tv_no_device"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="35dp"
                android:fontFamily="@font/archivo_bold"
                android:text="No Device ID Found"
                android:textColor="@color/black"
                android:textSize="40sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_bias="0.502"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/no_device_logo"
                app:layout_constraintVertical_bias="0.0" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>