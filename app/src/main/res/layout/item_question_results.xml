<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="16dp"
    android:padding="12dp"
    android:layout_marginHorizontal="8dp">

    <TextView
        android:id="@+id/questionText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <RadioGroup
        android:id="@+id/optionsRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/option1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:minHeight="0dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:buttonTint="@color/black"
            android:textColor="@color/black"
            android:enabled="false" />

        <RadioButton
            android:id="@+id/option2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:minHeight="0dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:buttonTint="@color/black"
            android:textColor="@color/black"
            android:enabled="false" />

        <RadioButton
            android:id="@+id/option3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:minHeight="0dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:buttonTint="@color/black"
            android:textColor="@color/black"
            android:enabled="false" />

        <RadioButton
            android:id="@+id/option4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:minHeight="0dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:buttonTint="@color/black"
            android:textColor="@color/black"
            android:enabled="false" />

    </RadioGroup>

    <TextView
        android:id="@+id/correctAnswerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Correct Answer: "
        android:textColor="@color/blue_400"
        android:textSize="12sp"
        android:fontFamily="@font/inter_regular"
        android:visibility="gone" />

</LinearLayout>