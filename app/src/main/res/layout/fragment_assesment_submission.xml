<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/splash_logo"
    tools:context=".ui.assesment.AssesmentSubmissionFragment">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="40dp"
        android:background="@drawable/curve_rectangle_bg_2dp"
        android:backgroundTint="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/headerSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginStart="20dp"
                    android:src="@drawable/clte_patashales" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:gravity="end|center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/account_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:text="@string/school_name"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:id="@+id/gradeSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/headerSection"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:id="@+id/grade_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:fontFamily="@font/archivo_bold"
                    android:text="@string/grade"
                    android:textColor="@color/black"
                    android:textSize="20sp" />

                <TextView
                    android:id="@+id/change_grade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/change_grade"
                    android:textColor="@color/blue_400"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/chapterSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/gradeSection"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:id="@+id/back_button"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/arrow_left_black" />

                <TextView
                    android:id="@+id/chapter_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/archivo_bold"
                    android:text="Air Pollution"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@drawable/bg_subject_selected"
                    android:backgroundTint="@color/light_grey">

                    <TextView
                        android:id="@+id/chapter_titles"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_regular"
                        android:padding="8dp"
                        android:text="Science"
                        android:textColor="@color/dark_grey"
                        android:textSize="12sp" />
                </FrameLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginVertical="25dp"
                android:orientation="vertical"
                android:layout_marginHorizontal="40dp"
                android:background="@drawable/curve_rectangle_bg_2dp">
                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="28dp"
                    android:src="@drawable/assesment_submission"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="80dp"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Assesment Submitted"
                    android:textSize="20sp"
                    android:layout_marginTop="22dp"
                    android:fontFamily="@font/archivo_bold"
                    android:textColor="@color/black"
                    android:layout_gravity="center_horizontal"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="The Assessment  Have been Saved Successfully!"
                    android:textSize="16sp"
                    android:layout_marginTop="7dp"
                    android:fontFamily="@font/inter_regular"
                    android:textColor="@color/text_grey"
                    android:layout_gravity="center_horizontal"/>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="39dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <!-- View Assessment Results Button -->
                    <LinearLayout
                        android:id="@+id/submitButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginEnd="16dp"
                        android:padding="12dp"
                        android:background="@drawable/view_assesment_background"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/view" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/inter_regular"
                            android:text="View Assessment Results"
                            android:textColor="@color/black"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/continuebutton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="@drawable/view_assesment_background"
                        android:backgroundTint="@color/black"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter_regular"
                            android:text="Continue with Assesments"
                            android:textColor="@color/white"
                            android:textSize="18sp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginStart="8dp"
                            android:src="@drawable/navigate_next" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</FrameLayout>