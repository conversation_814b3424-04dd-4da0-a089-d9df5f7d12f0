<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.resources.VideoFragment">

  <!--  <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="6dp">

        <EditText
            android:id="@+id/search_video"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:background="@drawable/search_bcakground"
            android:drawableStart="@drawable/ic_search"
            android:textColorHint="@color/lb_grey"
            android:fontFamily="@font/inter_regular"
            android:hint="Search Text Books"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:textSize="16sp" />
    </FrameLayout>-->
<!--  <LinearLayout
      android:layout_width="453dp"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      android:padding="10dp"
      android:layout_marginTop="22dp"
      android:background="@drawable/search_bcakground"
      android:layout_marginBottom="6dp">
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"

        android:layout_marginStart="10dp"

        android:src="@drawable/ic_search"/>
    <TextView
        android:id="@+id/search_video"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColorHint="@color/lb_grey"
        android:fontFamily="@font/inter_regular"
        android:layout_marginStart="10dp"
        android:hint="Search Text Books"
        android:paddingEnd="16dp"
        android:textSize="16sp" />

  </LinearLayout>
  <View
      android:layout_width="match_parent"
      android:layout_height="1dp"
      android:layout_marginTop="4dp"
       android:background="@color/search_grey"/>

    <TextView
        android:id="@+id/video_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Total available Videos"
        android:layout_marginStart="2dp"
        android:layout_marginVertical="8dp"
        android:textColor="@color/dark_grey"
        android:textSize="14sp" />-->

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/video_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_chapter_details" />

</LinearLayout>
