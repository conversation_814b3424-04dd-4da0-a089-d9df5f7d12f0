<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state (TV remote selection) - Educational theme -->
    <item android:state_focused="true">
        <layer-list>
            <!-- Shadow layer for depth -->
            <item android:top="2dp" android:left="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#40000000" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <!-- Main background -->
            <item android:bottom="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="@color/educational_blue"
                        android:endColor="@color/colorPrimaryDark"
                        android:angle="135" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <!-- Focus border -->
            <item android:bottom="3dp" android:right="3dp" android:top="3dp" android:left="3dp">
                <shape android:shape="rectangle">
                    <stroke android:width="4dp" android:color="@color/tv_focus_border" />
                    <corners android:radius="13dp" />
                </shape>
            </item>
            <!-- Inner highlight -->
            <item android:bottom="7dp" android:right="7dp" android:top="7dp" android:left="7dp">
                <shape android:shape="rectangle">
                    <stroke android:width="2dp" android:color="@color/tv_focus_glow" />
                    <corners android:radius="9dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Pressed state (when clicked) -->
    <item android:state_pressed="true">
        <layer-list>
            <!-- Pressed background -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="@color/colorPrimaryDark"
                        android:endColor="@color/colorPrimary"
                        android:angle="135" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <!-- Pressed border -->
            <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
                <shape android:shape="rectangle">
                    <stroke android:width="3dp" android:color="@color/colorAccent" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Selected state -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Selected background -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/tv_selected_bg" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <!-- Selected border -->
            <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
                <shape android:shape="rectangle">
                    <stroke android:width="3dp" android:color="@color/educational_orange" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Default state (normal appearance) -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/colorPrimary"
                android:endColor="@color/colorPrimaryLight"
                android:angle="135" />
            <corners android:radius="16dp" />
            <padding 
                android:left="16dp" 
                android:top="12dp" 
                android:right="16dp" 
                android:bottom="12dp" />
        </shape>
    </item>
    
</selector>
