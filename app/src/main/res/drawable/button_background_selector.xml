<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state (TV remote selection) -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="3dp" 
                android:color="@color/white" />
            <padding 
                android:left="12dp" 
                android:top="8dp" 
                android:right="12dp" 
                android:bottom="8dp" />
        </shape>
    </item>
    
    <!-- Pressed state (when clicked) -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimaryDark" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="2dp" 
                android:color="@color/colorAccent" />
            <padding 
                android:left="12dp" 
                android:top="8dp" 
                android:right="12dp" 
                android:bottom="8dp" />
        </shape>
    </item>
    
    <!-- Selected state (alternative focus indicator) -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="2dp" 
                android:color="@color/colorAccent" />
            <padding 
                android:left="12dp" 
                android:top="8dp" 
                android:right="12dp" 
                android:bottom="8dp" />
        </shape>
    </item>
    
    <!-- Default state (normal appearance) -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="8dp" />
            <padding 
                android:left="12dp" 
                android:top="8dp" 
                android:right="12dp" 
                android:bottom="8dp" />
        </shape>
    </item>
    
</selector>
