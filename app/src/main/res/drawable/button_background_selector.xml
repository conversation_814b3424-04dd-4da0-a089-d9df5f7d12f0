<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state (TV remote selection) - Enhanced visibility -->
    <item android:state_focused="true">
        <layer-list>
            <!-- Background layer -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/bl" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
            <!-- Outer focus border -->
            <item android:top="2dp" android:bottom="2dp" android:left="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <stroke android:width="4dp" android:color="@color/tv_focus_border" />
                    <corners android:radius="10dp" />
                </shape>
            </item>
            <!-- Inner glow effect -->
            <item android:top="6dp" android:bottom="6dp" android:left="6dp" android:right="6dp">
                <shape android:shape="rectangle">
                    <stroke android:width="2dp" android:color="@color/tv_focus_glow" />
                    <corners android:radius="6dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- Pressed state (when clicked) -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimaryDark" />
            <corners android:radius="12dp" />
            <stroke
                android:width="3dp"
                android:color="@color/colorAccent" />
            <padding
                android:left="12dp"
                android:top="8dp"
                android:right="12dp"
                android:bottom="8dp" />
        </shape>
    </item>

    <!-- Selected state (alternative focus indicator) -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/tv_selected_bg" />
            <corners android:radius="12dp" />
            <stroke
                android:width="3dp"
                android:color="@color/colorAccent" />
            <padding
                android:left="12dp"
                android:top="8dp"
                android:right="12dp"
                android:bottom="8dp" />
        </shape>
    </item>

    <!-- Default state (normal appearance) -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="12dp" />
            <padding
                android:left="12dp"
                android:top="8dp"
                android:right="12dp"
                android:bottom="8dp" />
        </shape>
    </item>
    
</selector>
