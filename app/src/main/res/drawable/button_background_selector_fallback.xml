<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state (TV remote selection) - Enhanced visibility -->
    <item android:state_focused="true">
        <layer-list>
            <!-- Background layer -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/colorPrimary" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
            <!-- Focus border layer -->
            <item android:top="2dp" android:bottom="2dp" android:left="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <stroke android:width="4dp" android:color="#FFFFFF" />
                    <corners android:radius="6dp" />
                </shape>
            </item>
            <!-- Inner glow effect -->
            <item android:top="4dp" android:bottom="4dp" android:left="4dp" android:right="4dp">
                <shape android:shape="rectangle">
                    <stroke android:width="2dp" android:color="#FFD700" />
                    <corners android:radius="4dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimaryDark" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#FFFFFF" />
        </shape>
    </item>
    
    <!-- Selected state -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="8dp" />
            <stroke android:width="3dp" android:color="#FFD700" />
        </shape>
    </item>
    
    <!-- Default state - use original button_background if it exists -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
