package clt.india.classroom.utils

import org.junit.Assert.*
import org.junit.jupiter.api.Test

class ValidationUtilsTest {

    @Test
    fun `isValidEmail should return true for valid email addresses`() {
        // Test with valid email addresses
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"))
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"))
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"))
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"))
    }
    
    @Test
    fun `isValidEmail should return false for invalid email addresses`() {
        // Test with invalid email addresses
        assertFalse(ValidationUtils.isValidEmail(""))
        assertFalse(ValidationUtils.isValidEmail("test"))
        assertFalse(ValidationUtils.isValidEmail("test@"))
        assertFalse(ValidationUtils.isValidEmail("@example.com"))
        assertFalse(ValidationUtils.isValidEmail("test@example"))
        assertFalse(ValidationUtils.isValidEmail("test@.com"))
        assertFalse(ValidationUtils.isValidEmail("test@example."))
        assertFalse(ValidationUtils.isValidEmail("test@exam ple.com"))
    }
    
    @Test
    fun `isValidPassword should return true for valid passwords`() {
        // Test with valid passwords
        assertTrue(ValidationUtils.isValidPassword("Password1!"))
        assertTrue(ValidationUtils.isValidPassword("Abcd1234$"))
        assertTrue(ValidationUtils.isValidPassword("SecureP@ss123"))
        assertTrue(ValidationUtils.isValidPassword("P@ssw0rd"))
    }
    
    @Test
    fun `isValidPassword should return false for invalid passwords`() {
        // Test with invalid passwords
        
        // Too short
        assertFalse(ValidationUtils.isValidPassword("Pw1!"))
        
        // No uppercase
        assertFalse(ValidationUtils.isValidPassword("password1!"))
        
        // No lowercase
        assertFalse(ValidationUtils.isValidPassword("PASSWORD1!"))
        
        // No digit
        assertFalse(ValidationUtils.isValidPassword("Password!"))
        
        // No special character
        assertFalse(ValidationUtils.isValidPassword("Password1"))
        
        // Empty string
        assertFalse(ValidationUtils.isValidPassword(""))
    }
    
    @Test
    fun `isValidPhoneNumber should return true for valid phone numbers`() {
        // Test with valid phone numbers
        assertTrue(ValidationUtils.isValidPhoneNumber("1234567890"))
        assertTrue(ValidationUtils.isValidPhoneNumber("************"))
        assertTrue(ValidationUtils.isValidPhoneNumber("(*************"))
        assertTrue(ValidationUtils.isValidPhoneNumber("+911234567890"))
        assertTrue(ValidationUtils.isValidPhoneNumber("****** 456 7890"))
    }
    
    @Test
    fun `isValidPhoneNumber should return false for invalid phone numbers`() {
        // Test with invalid phone numbers
        assertFalse(ValidationUtils.isValidPhoneNumber(""))
        assertFalse(ValidationUtils.isValidPhoneNumber("123"))
        assertFalse(ValidationUtils.isValidPhoneNumber("12345"))
        assertFalse(ValidationUtils.isValidPhoneNumber("123456789")) // 9 digits
        assertFalse(ValidationUtils.isValidPhoneNumber("abcdefghij"))
        assertFalse(ValidationUtils.isValidPhoneNumber("+"))
        assertFalse(ValidationUtils.isValidPhoneNumber("+abc1234567890"))
    }
    
    @Test
    fun `isValidName should return true for valid names`() {
        // Test with valid names
        assertTrue(ValidationUtils.isValidName("John"))
        assertTrue(ValidationUtils.isValidName("Mary Jane"))
        assertTrue(ValidationUtils.isValidName("O'Connor"))
        assertTrue(ValidationUtils.isValidName("Smith-Johnson"))
        assertTrue(ValidationUtils.isValidName("Jean-Claude"))
    }
    
    @Test
    fun `isValidName should return false for invalid names`() {
        // Test with invalid names
        assertFalse(ValidationUtils.isValidName(""))
        assertFalse(ValidationUtils.isValidName("A")) // Too short
        assertFalse(ValidationUtils.isValidName("John123"))
        assertFalse(ValidationUtils.isValidName("John@Doe"))
        assertFalse(ValidationUtils.isValidName("123"))
    }
}
