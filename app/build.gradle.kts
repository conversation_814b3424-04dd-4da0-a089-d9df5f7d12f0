plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.google.gms.google.services)
    alias(libs.plugins.google.firebase.crashlytics)
    id("androidx.navigation.safeargs.kotlin")

    jacoco
    id("org.sonarqube") version "4.3.1.3277"

    id("kotlin-kapt")
}
android {
    namespace = "clt.india.classroom"
    compileSdk = 35
    defaultConfig {
        applicationId = "clt.india.classroom"
        minSdk = 30
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"
    }
    buildTypes {
        getByName("debug") {
            isTestCoverageEnabled = true
        }
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
    }
    buildFeatures {
        //noinspection DataBindingWithoutKapt
        dataBinding = true
    }

    lint {
        baseline = file("lint-baseline.xml")
    }

}
dependencies {
    implementation(libs.androidx.core.ktx.v180)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.preference.ktx)
    // Firebase
    implementation(platform("com.google.firebase:firebase-bom:32.7.4"))
    implementation("com.google.firebase:firebase-analytics-ktx")
    implementation(libs.firebase.crashlytics)
    implementation(libs.androidx.leanback)
    implementation(libs.glide)
    implementation(libs.androidx.activity)

    // Testing
    testImplementation(libs.junit)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.inline)
    testImplementation(libs.androidx.core.testing)
    testImplementation(libs.kotlinx.coroutines.test)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    // RetroFit Dependencies
    implementation(libs.gson)
    implementation(libs.retrofit)
    implementation(libs.converter.gson)
    implementation(libs.logging.interceptor)
    // Coroutines
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.androidx.lifecycle.viewmodel.ktx) // viewModel scope
    implementation(libs.androidx.lifecycle.lifecycle.runtime.ktx) // lifecycle scope
    implementation(libs.androidx.fragment.ktx)
    // Lifecycle
    implementation(libs.androidx.lifecycle.common)
    implementation(libs.androidx.lifecycle.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    // Size dp/sp
    implementation(libs.sdp.android)
    implementation(libs.ssp.android)

    //material-design
    implementation (libs.material)

    //navigation
    implementation (libs.androidx.navigation.fragment.ktx)
    implementation( libs.androidx.navigation.ui.ktx)

    //recycler
    implementation(libs.androidx.recyclerview)

    //mediaplayer
    implementation (libs.androidx.media3.exoplayer)
    implementation (libs.androidx.media3.ui)
    implementation ("com.github.mhiew:android-pdf-viewer:3.2.0-beta.3")

    //room
    implementation ("androidx.room:room-runtime:2.6.1")
    kapt ("androidx.room:room-compiler:2.6.1")
    implementation ("androidx.room:room-ktx:2.6.1")
    implementation ("com.google.code.gson:gson:2.10.1")





    // Declare the BOM in the testImplementation configuration
    testImplementation(platform(libs.junit.bom)) // Use the BOM version

    // Then declare the JUnit 5 artifacts WITHOUT versions
    testImplementation(libs.junit.jupiter.api)
    testRuntimeOnly(libs.junit.jupiter.engine)
    testRuntimeOnly(libs.junit.platform.launcher)
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")

    // You can also include the vintage engine if needed for JUnit 4 tests
    // testRuntimeOnly('org.junit.vintage:junit-vintage-engine')
    implementation(libs.kotlinx.serialization.json)
}

//tasks.withType<Test> {
//    useJUnitPlatform() // ✅ Only if you're using JUnit 5; remove if you're on JUnit 4
//    finalizedBy("jacocoTestReport") // Ensures report runs after test
//}
//
//tasks.register<JacocoReport>("jacocoTestReport") {
//    dependsOn("testDebugUnitTest") // ✅ Important: match this with your CI job's test task
//
//    jacoco {
//        toolVersion = "0.8.13"
//        reportsDirectory = layout.buildDirectory.dir("customJacocoReportDir")
//    }
//    reports {
//        xml.required.set(true)
//        html.required.set(true)
//    }
//
//    classDirectories.setFrom(
//        fileTree("build/intermediates/javac/debug/classes") {
//            exclude("**/*Test*.*")
//        },
//        fileTree("build/tmp/kotlin-classes/debug") {
//            exclude("**/*Test*.*")
//        }
//    )
//    sourceDirectories.setFrom(files("src/main/java", "src/main/kotlin"))
//    executionData.setFrom(files("build/jacoco/testDebugUnitTest.exec"))
//}
// ✅ Add this at the bottom:

tasks.register<JacocoReport>("jacocoTestReport") {
    dependsOn("testDebugUnitTest")

    val fileFilter = listOf(
        "**/R.class", "**/R$*.class", "**/BuildConfig.*",
        "**/Manifest*.*", "**/*Test*.*", "android/**/*.*"
    )

    val javaDebugTree = fileTree(
        "${buildDir}/intermediates/javac/debug/classes"
    ) {
        exclude(fileFilter)
    }

    val kotlinDebugTree = fileTree(
        "${buildDir}/tmp/kotlin-classes/debug"
    ) {
        exclude(fileFilter)
    }

    classDirectories.setFrom(files(javaDebugTree, kotlinDebugTree))

    sourceDirectories.setFrom(files("src/main/java", "src/main/kotlin"))

    executionData.setFrom(
        fileTree(buildDir) {
            include(
                "jacoco/testDebugUnitTest.exec",
                "outputs/unit_test_code_coverage/debugUnitTest/testDebugUnitTest.exec"
            )
        }
    )

    reports {
        xml.required.set(true)
        html.required.set(true)
    }
}
// ✅ Configure testDebugUnitTest task *after* it exists
afterEvaluate {
    tasks.named("testDebugUnitTest", Test::class).configure {
        useJUnitPlatform()
        filter {
            isFailOnNoMatchingTests = false
        }
    }
}