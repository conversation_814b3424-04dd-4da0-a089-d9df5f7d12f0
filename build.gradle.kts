// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id("org.sonarqube")
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.google.gms.google.services) apply false
    alias(libs.plugins.google.firebase.crashlytics) apply false
    id("androidx.navigation.safeargs.kotlin") version "2.7.7" apply false
}

sonarqube {
    properties {
//        property("sonar.projectKey", "your_project_key")
//        property("sonar.projectName", "Your Android App")
        property("sonar.projectVersion", "1.0")
        property("sonar.sources", "src/main/java,src/main/kotlin")
        property("sonar.language", "kotlin")
        property("sonar.sourceEncoding", "UTF-8")
        property("sonar.java.binaries", "build/intermediates/javac/debug/classes")
        property("sonar.junit.reportPaths", "build/test-results/testDebugUnitTest")
        property("sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/jacocoTestReport/jacocoTestReport.xml")
    }
}
