# stages:
#   - Test

# Sonarqube-Check:
#   image:
#     name: sonarsource/sonar-scanner-cli:latest
#     entrypoint: [""]
#   variables:
#     GIT_DEPTH: 0
#   cache:
#     key: "${CI_JOB_NAME}"
#     paths:
#       - .sonar/cache
#       - .scannerwork
#   stage: Test
#   script: 
#     - sonar-scanner -X -Dsonar.projectKey=$SONAR_TOKEN  -Dsonar.sources=. -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_TOKEN -D sonar.projectName=$CI_PROJECT_NAME -Dsonar.qualitygate.wait=true -Dsonar.branch.name=$CI_COMMIT_REF_NAME -Dsonar.sourceEncoding=UTF-8 
#     - rm -rf ./.scannerwork
#   allow_failure: true
#   only:
#     - develop
#   tags:
#    - pacewisdom-docker-shared-runner

stages:
  - test-coverage
  - test

Generate-Coverage:
  image: cimg/android:2024.01
  stage: test-coverage
  allow_failure: true
  cache:
    paths:
      - .gradle/
      - app/build/
  before_script:
    - yes | sdkmanager --licenses || true
  script:
    - bash ./gradlew testDebugUnitTest --continue || true
    - bash ./gradlew jacocoTestReport || true
  artifacts:
    when: always  # Upload even if job fails
    paths:
      - app/build/reports/tests/testDebugUnitTest/  # JUnit test results
      - app/build/reports/jacoco/jacocoTestReport/  # Coverage (if created)
    expire_in: 1 week
  tags:
    - pacewisdom-docker-shared-runner
  only:
    - sonar_issue_27_may_2025_ab

Sonarqube-Check:
  image: sonarsource/sonar-scanner-cli:latest
  stage: test
  dependencies:
    - Generate-Coverage
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
      - .scannerwork
  script:
    - >
      sonar-scanner
      -Dsonar.projectKey=$CI_PROJECT_NAME
      -Dsonar.projectName=$CI_PROJECT_NAME
      -Dsonar.sources=app/src/main
      -Dsonar.tests=app/src/test
      -Dsonar.java.binaries=app/build/intermediates/javac/debug
      -Dsonar.kotlin.binaries=app/build/tmp/kotlin-classes/debug
      -Dsonar.coverage.jacoco.xmlReportPaths=app/build/reports/jacoco/jacocoTestReport/jacocoTestReport.xml
      -Dsonar.host.url=$SONAR_HOST_URL
      -Dsonar.login=$SONAR_TOKEN
      -Dsonar.branch.name=$CI_COMMIT_REF_NAME

  tags:
    - pacewisdom-docker-shared-runner
  only:
    - sonar_issue_27_may_2025_ab
#  allow_failure: true
