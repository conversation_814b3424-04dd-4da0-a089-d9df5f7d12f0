[versions]
agp = "8.9.1"
androidPdfViewer = "3.2.0-beta.1"
androidPdfViewerVersion = "2.8.2"
appcompat = "1.7.0"
constraintlayout = "2.2.1"
converterGson = "2.9.0"
coreKtxVersion = "1.8.0"
coreTesting = "2.2.0"
espressoCore = "3.6.1"
fragmentKtx = "1.8.6"
gson = "2.10.1"
junit = "4.13.2"
junitBom = "5.10.2"
junitVersion = "1.2.1"
kotlin = "2.0.21"
coreKtx = "1.16.0"
kotlinxCoroutinesAndroid = "1.5.2-native-mt"
kotlinxCoroutinesTest = "1.7.3"
kotlinxSerializationJson = "1.2.2"
lifecycleLivedataKtx = "2.8.7"
loggingInterceptor = "5.0.0-alpha.1"
material = "1.12.0"
mockitoCore = "4.0.0"
media3Exoplayer = "1.6.1"
navigationFragmentKtx = "2.8.9"
preferenceKtx = "1.2.1"
recyclerview = "1.4.0"
sdpAndroid = "1.0.6"
sspAndroid = "1.0.6"
firebaseCrashlytics = "19.4.3"
googleGmsGoogleServices = "4.4.2"
googleFirebaseCrashlytics = "3.0.3"
leanback = "1.0.0"
glide = "4.11.0"
activity = "1.10.1"

[libraries]
android-pdf-viewer = { module = "com.github.barteksc:android-pdf-viewer", version.ref = "androidPdfViewer" }
android-pdf-viewer-v282 = { module = "com.github.barteksc:android-pdf-viewer", version.ref = "androidPdfViewerVersion" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-core-ktx-v180 = { module = "androidx.core:core-ktx", version.ref = "coreKtxVersion" }
androidx-core-testing = { module = "androidx.arch.core:core-testing", version.ref = "coreTesting" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCore" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragmentKtx" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "junitVersion" }
androidx-lifecycle-common = { module = "androidx.lifecycle:lifecycle-common", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3Exoplayer" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3Exoplayer" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationFragmentKtx" }
androidx-preference-ktx = { module = "androidx.preference:preference-ktx", version.ref = "preferenceKtx" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "converterGson" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
junit = { module = "junit:junit", version.ref = "junit" }
junit-bom = { module = "org.junit:junit-bom", version.ref = "junitBom" }
junit-jupiter-api = { module = "org.junit.jupiter:junit-jupiter-api" }
junit-jupiter-engine = { module = "org.junit.jupiter:junit-jupiter-engine" }
junit-platform-launcher = { module = "org.junit.platform:junit-platform-launcher" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesAndroid" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinxCoroutinesTest" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "loggingInterceptor" }
material = { module = "com.google.android.material:material", version.ref = "material" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockitoCore" }
mockito-inline = { module = "org.mockito:mockito-inline", version.ref = "mockitoCore" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "converterGson" }
sdp-android = { module = "com.intuit.sdp:sdp-android", version.ref = "sdpAndroid" }
ssp-android = { module = "com.intuit.ssp:ssp-android", version.ref = "sspAndroid" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics", version.ref = "firebaseCrashlytics" }
androidx-leanback = { group = "androidx.leanback", name = "leanback", version.ref = "leanback" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
google-gms-google-services = { id = "com.google.gms.google-services", version.ref = "googleGmsGoogleServices" }
google-firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "googleFirebaseCrashlytics" }

